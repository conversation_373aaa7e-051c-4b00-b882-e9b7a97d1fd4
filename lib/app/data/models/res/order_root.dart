import 'dart:convert';

import 'package:muyipork/app/data/models/other/order_detail.dart';
import 'package:muyipork/app/data/models/res/res_base.dart';

class OrderRoot extends ResBase {
  OrderRoot({
    this.data,
    this.subOrder,
  });

  OrderDetail data;
  List<OrderDetail> subOrder;

  OrderRoot copyWith({
    OrderDetail data,
    List<OrderDetail> subOrder,
  }) =>
      OrderRoot(
        data: data ?? this.data,
        subOrder: subOrder ?? this.subOrder,
      );

  factory OrderRoot.fromRawJson(String str) =>
      OrderRoot.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrderRoot.fromJson(Map<String, dynamic> json) => OrderRoot(
        data: json["data"] == null ? null : OrderDetail.fromJson(json["data"]),
        subOrder: json["sub_order"] == null
            ? null
            : List<OrderDetail>.from(
                json["sub_order"].map((x) => OrderDetail.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "data": data == null ? null : data.toJson(),
        "sub_order": subOrder == null
            ? null
            : List<dynamic>.from(subOrder.map((x) => x.toJson())),
      };
}
