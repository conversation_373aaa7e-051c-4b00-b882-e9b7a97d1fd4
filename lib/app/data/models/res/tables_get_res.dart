import 'package:muyipork/app/data/models/req/id_sort.dart';

import 'res_base.dart';
import 'package:dio/dio.dart';

class TablesGetRes extends ResBase {
  List<Partition> data;

  TablesGetRes({this.data});

  TablesGetRes.resError(Response response) : super.resError(response);
  TablesGetRes.unKnownError() : super.unKnownError();

  TablesGetRes.fromJson(Map<String, dynamic> json) : super.fromErrorJson(json) {
    data = [];
    if (json['data'] != null) {
      json['data'].forEach((v) {
        data.add(new Partition.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data.map((v) => v.toJson()).toList();
    }
    return data;
  }

  //這個 Sort 是絕對會新增到最後一格的順序 (Server 習性順應)
  int getTrailNewSortValue() {
    int maxSort = 0;
    if (data != null) {
      data.forEach((element) {
        if (element.sort > maxSort) {
          maxSort = element.sort;
        }
      });
    }
    return maxSort + 1;
  }

  //嘗試移動某格至某格(只產生回傳新的IDSort)
  List<IDSort> preMovePartition(int oldIndex, int newIndex) {
    List<IDSort> returnIDSorts = [];
    if (data != null) {
      data.forEach((element) {
        returnIDSorts.add(IDSort(id: element.id));
      });

      //Swap the old index with new index.
      IDSort moving = returnIDSorts.removeAt(oldIndex);
      returnIDSorts.insert(newIndex, moving);

      for (int i = 0; i < returnIDSorts.length; ++i) {
        //Start from 1?
        returnIDSorts[i].sort = i + 1;
      }
    }
    return returnIDSorts;
  }

  //嘗試完成移動某格至某格當作Server已完成後改變app端的資料。
  postMovePartition(int oldIndex, int newIndex) {
    if (data != null) {
      Partition moving = data.removeAt(oldIndex);
      data.insert(newIndex, moving);
      for (int i = 0; i < data.length; ++i) {
        data[i].sort = i;
      }
    }
  }

  //按照傳入 Id 拼湊出顯示桌號字串
  String getDisplayName(int partitionId, int tableId) {
    for (int i = 0; i < data.length; ++i) {
      if (data[i].id == partitionId) {
        return data[i].getDisplayName(tableId);
      }
    }
    return '';
  }
}

class Partition {
  num id;
  String name;
  num parentId;
  num sort;
  List<TableV1> child;

  Partition({this.id, this.name, this.parentId, this.sort, this.child});

  Partition.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    parentId = json['parent_id'];
    sort = json['sort'];
    if (json['child'] != null) {
      child = [];
      json['child'].forEach((v) {
        child.add(new TableV1.fromJson(v));
      });
    }
  }

  paste(Partition partition) {
    id = partition.id;
    name = partition.name;
    parentId = partition.parentId;
    sort = partition.sort;
    for (int i = 0; i < partition.child.length; ++i) {
      child.add(TableV1.clone(partition.child[i]));
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['parent_id'] = this.parentId;
    data['sort'] = this.sort;
    if (this.child != null) {
      data['child'] = this.child.map((v) => v.toJson()).toList();
    }
    return data;
  }

  //這個 Sort 是絕對會新增到最後一格的順序 (Server 習性順應)
  int getTrailNewSortValue() {
    int maxSort = 0;
    if (child != null) {
      child.forEach((element) {
        if (element.sort > maxSort) {
          maxSort = element.sort;
        }
      });
    }
    return maxSort + 1;
  }

  //嘗試移動某格至某格(只產生回傳新的IDSort)
  List<IDSort> preMoveTable(int oldIndex, int newIndex) {
    List<IDSort> returnIDSorts = [];
    if (child != null) {
      child.forEach((element) {
        returnIDSorts.add(IDSort(id: element.id));
      });

      //Swap the old index with new index.
      IDSort moving = returnIDSorts.removeAt(oldIndex);
      returnIDSorts.insert(newIndex, moving);

      for (int i = 0; i < returnIDSorts.length; ++i) {
        //Start from 1?
        returnIDSorts[i].sort = i + 1;
      }
    }
    return returnIDSorts;
  }

  //嘗試完成移動某格至某格當作Server已完成後改變app端的資料。
  postMoveTable(int oldIndex, int newIndex) {
    if (child != null) {
      TableV1 moving = child.removeAt(oldIndex);
      child.insert(newIndex, moving);
      for (int i = 0; i < child.length; ++i) {
        child[i].sort = i;
      }
    }
  }

  //按照傳入 Id 拼湊出顯示桌號字串
  String getDisplayName(int tableId) {
    String returnStr = name;
    for (int i = 0; i < child.length; ++i) {
      if (child[i].id == tableId) {
        returnStr += child[i].name;
        break;
      }
    }
    return returnStr;
  }
}

// TODO: remove me, use okshop_model/Table instead
class TableV1 {
  num id;
  String name;
  num parentId;
  num sort;
  List<TableV1> child;

  TableV1({this.id, this.name, this.parentId, this.sort, this.child});

  TableV1.clone(TableV1 table) {
    id = table.id;
    name = table.name;
    parentId = table.parentId;
    sort = table.sort;
    for (int i = 0; i < table.child.length; ++i) {
      child.add(TableV1.clone(table.child[i]));
    }
  }

  TableV1.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    parentId = json['parent_id'];
    sort = json['sort'];
    if (json['child'] != null) {
      child = [];
      json['child'].forEach((v) {
        child.add(new TableV1.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['parent_id'] = this.parentId;
    data['sort'] = this.sort;
    if (this.child != null) {
      data['child'] = this.child.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
