import 'res_base.dart';
import 'package:dio/dio.dart';

class ProductsProductIdSoldOutGetRes extends ResBase {
  bool isUpdated;
  int productId;

  ProductsProductIdSoldOutGetRes({this.isUpdated, this.productId});

  ProductsProductIdSoldOutGetRes.resError(Response response) : super.resError(response);
  ProductsProductIdSoldOutGetRes.unKnownError() : super.unKnownError();

  ProductsProductIdSoldOutGetRes.fromJson(Map<String, dynamic> json) {
    isUpdated = json['is_updated'];
    productId = json['product_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_updated'] = this.isUpdated;
    data['product_id'] = this.productId;
    return data;
  }
}