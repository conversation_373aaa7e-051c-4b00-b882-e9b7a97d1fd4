import 'package:dio/dio.dart';
import 'package:muyipork/app/data/models/res/res_base.dart';

class SettingLabelsDeleteRes extends ResBase {
  bool isDeleted;

  SettingLabelsDeleteRes({this.isDeleted});

  SettingLabelsDeleteRes.resError(Response response) : super.resError(response);
  SettingLabelsDeleteRes.unKnownError() : super.unKnownError();

  SettingLabelsDeleteRes.fromJson(Map<String, dynamic> json) {
    isDeleted = json['is_deleted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_deleted'] = this.isDeleted;
    return data;
  }
}