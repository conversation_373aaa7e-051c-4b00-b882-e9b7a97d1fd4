import 'res_base.dart';
import 'package:dio/dio.dart';

class AdditionCategoriesPostRes extends ResBase {
  bool isCreated;
  int additionCategoryId;

  AdditionCategoriesPostRes({this.isCreated, this.additionCategoryId});

  AdditionCategoriesPostRes.resError(Response response) : super.resError(response);
  AdditionCategoriesPostRes.unKnownError() : super.unKnownError();

  AdditionCategoriesPostRes.fromJson(Map<String, dynamic> json) : super.fromErrorJson(json) {
    isCreated = json['is_created'];
    additionCategoryId = json['addition_category_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_created'] = this.isCreated;
    data['addition_category_id'] = this.additionCategoryId;
    return data;
  }
}