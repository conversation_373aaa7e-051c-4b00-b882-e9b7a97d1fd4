import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter_star_prnt/flutter_star_prnt.dart';
import 'package:muyipork/app/data/models/req/setting_labels_batch_with_truncate_post_req.dart';
import 'package:muyipork/app/data/models/res/res_base.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

class SettingLabelsGetRes extends ResBase {
  List<SettingLabel> data;

  SettingLabelsGetRes({this.data = const []});

  SettingLabelsGetRes.resError(Response response) : super.resError(response);
  SettingLabelsGetRes.unKnownError() : super.unKnownError();

  SettingLabelsGetRes.fromJson(Map<String, dynamic> json) {
    data = [];
    if (json['data'] != null) {
      json['data'].forEach((v) {
        data.add(new SettingLabel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data.map((v) => v.toJson()).toList();
    }
    return data;
  }

  // TODO: remove me
  //嘗試更新
  bool tryUpdateAllPrinters(Iterable<WifiPrinterInfo> wifiPrinterList) {
    bool hasUpdateOne = false;

    //嘗試新增發現的印表機
    wifiPrinterList.forEach((element) {
      if (_tryAddOrUpdateDiscoveredPrinter(element)) {
        hasUpdateOne = true;
      }
    });
    // for (int i = 0; i < wifiPrinterList.length; ++i) {
    //   if (_tryAddOrUpdateDiscoveredPrinter(wifiPrinterList[i])) {
    //     hasUpdateOne = true;
    //   }
    // }

    //嘗試移除已經不存在的印表機
    data.removeWhere((label) {
      final needToRemove = wifiPrinterList
          .every((printer) => printer.macAddress != label.macAddress);
      if (needToRemove) {
        hasUpdateOne = true;
      }
      return needToRemove;
    });
    // for (int i = data.length - 1; i >= 0; --i) {
    //   wifiPrinterList.every((element) => element.macAddress)
    //   if (!wifiPrinterList
    //       .any((wifiPrinter) => wifiPrinter.macAddress == data[i].macAddress)) {
    //     data.removeAt(i);
    //     hasUpdateOne = true;
    //   }
    // }

    // TODO: 移除沒有 ip address 或 mac address 的標籤機

    return hasUpdateOne;
  }

  // TODO: remove me
  //嘗試新增發現的 Printer (每次 Wifi 設定重掃時都會嘗試加入所有掃到的 Printer)
  bool _tryAddOrUpdateDiscoveredPrinter(WifiPrinterInfo wifiPrinterInfo) {
    if (wifiPrinterInfo != null) {
      // print('_tryAddOrUpdateDiscoveredPrinter: ' + wifiPrinterInfo.info());
      //更新 ip address 以及 mac address.
      //這邊比較麻煩一點，先轉成 Map 處理最後再變回 List 比較保險
      Map<String, SettingLabel> settingsMap = Map<String, SettingLabel>();
      for (int i = 0; i < data.length; ++i) {
        if (!settingsMap.containsKey(data[i].macAddress)) {
          // print('Form settings map [' + data[i].name + ']');
          settingsMap[data[i].macAddress] = data[i];
        }
      }

      //現在已經確保 settingsMap 有對 macAddress 唯一性.
      bool hasFoundExist = false;
      bool hasUpdateOrAddOne = false;
      if (settingsMap.containsKey(wifiPrinterInfo.macAddress)) {
        hasFoundExist = true;
      }

      if (!hasFoundExist) {
        //不存在，直接新增一個新設定
        settingsMap[wifiPrinterInfo.macAddress] = SettingLabel(
          name: wifiPrinterInfo.name,
          ip: wifiPrinterInfo.ip,
          macAddress: wifiPrinterInfo.macAddress,
          categoryIds: [],
          printCount: 1,
          status: 1,
        );
        // print('Add a new printer [' + macAddress + '] IP to [' + ipAddress + ']');
        hasUpdateOrAddOne = true;
      }

      //把 settingsMap 轉回 printer settings.
      data = [];
      settingsMap.forEach((key, value) {
        data.add(value);
      });

      // print('data length after update [' + data.length.toString() + ']');

      return hasUpdateOrAddOne;
    }
    return false;
  }

  // TODO: remove me
  //嘗試更新
  bool tryUpdateAllPortInfos(List<PortInfo> portInfoList) {
    bool hasUpdateOne = false;

    //嘗試新增發現的印表機
    for (int i = 0; i < portInfoList.length; ++i) {
      if (_tryAddOrUpdateDiscoveredPortInfo(portInfoList[i])) {
        hasUpdateOne = true;
      }
    }

    //嘗試移除已經不存在的印表機
    for (int i = data.length - 1; i <= 0; --i) {
      if (!portInfoList
          .any((wifiPrinter) => wifiPrinter.macAddress == data[i].macAddress)) {
        data.removeAt(i);
        hasUpdateOne = true;
      }
    }

    return hasUpdateOne;
  }

  // TODO: remove me
  //嘗試新增發現的 Printer (每次 Wifi 設定重掃時都會嘗試加入所有掃到的 Printer)
  bool _tryAddOrUpdateDiscoveredPortInfo(PortInfo portInfo) {
    if (portInfo != null) {
      print('_tryAddOrUpdateDiscoveredPortInfo: [' + portInfo.modelName + ']');
      //更新 ip address 以及 mac address.
      //這邊比較麻煩一點，先轉成 Map 處理最後再變回 List 比較保險
      Map<String, SettingLabel> settingsMap = Map<String, SettingLabel>();
      for (int i = 0; i < data.length; ++i) {
        if (!settingsMap.containsKey(data[i].macAddress)) {
          // print('Form settings map [' + data[i].name + ']');
          settingsMap[data[i].macAddress] = data[i];
        }
      }

      //現在已經確保 settingsMap 有對 macAddress 唯一性.
      bool hasFoundExist = false;
      bool hasUpdateOrAddOne = false;
      if (settingsMap.containsKey(portInfo.macAddress)) {
        hasFoundExist = true;
      }

      if (!hasFoundExist) {
        //不存在，直接新增一個新設定
        settingsMap[portInfo.macAddress] = SettingLabel(
          name: portInfo.modelName,
          ip: portInfo.portName,
          macAddress: portInfo.macAddress,
          categoryIds: [],
          printCount: 1,
          status: 1,
        );
        // print('Add a new printer [' + macAddress + '] IP to [' + ipAddress + ']');
        hasUpdateOrAddOne = true;
      }

      //把 settingsMap 轉回 printer settings.
      data = [];
      settingsMap.forEach((key, value) {
        data.add(value);
      });

      // print('data length after update [' + data.length.toString() + ']');

      return hasUpdateOrAddOne;
    }
    return false;
  }

  //--

  //Generate a post data.
  SettingLabelsBatchWithTruncatePostReq
      generateSettingLabelsBatchWithTruncatePostReq() {
    return SettingLabelsBatchWithTruncatePostReq(labels: data);
  }

  //取得相對應的 SettingLabel.
  SettingLabel getSettingLabel(WifiPrinterInfo wifiPrinterInfo) {
    for (int i = 0; i < data.length; ++i) {
      if (data[i].macAddress == wifiPrinterInfo.macAddress) {
        return data[i];
      }
    }
    return null;
  }

  //-- 列印相關 API, 以下提供最方便的方式讓 controller 快速列印。

  //取得設定內有某個分類的所有 Printers
  Iterable<SettingLabel> getSettingLabelsWithCategories(
      Iterable<num> categoryIds) {
    return data.where((element) => element.containsCategories(categoryIds));
    // final returnList = <SettingLabel>[];
    // for (int i = 0; i < data.length; ++i) {
    //   if (data[i].containsCategories(categoryId)) {
    //     returnList.add(data[i]);
    //   }
    // }
    // return returnList;
  }

  // 指定列印圖片至某個主餐分類
  // Future<void> printCategoryImage(
  //     Iterable<num> categoryIds, Uint8List image) async {
  //   // 我們需要辨識要印到那些份類
  //   final printerSettingsWithCats = getSettingLabelsWithCategories(categoryIds);
  //   await Future.forEach(printerSettingsWithCats, (element) {
  //     try {
  //       return printImage(element.ip, image);
  //     } catch (e) {
  //       throw e;
  //     }
  //   });
  //   // for (int i = 0; i < printerSettingsWithCats.length; ++i) {
  //   //   if (printerSettingsWithCats[i].containsCategories(categoryIds)) {
  //   //     try {
  //   //       await printImage(printerSettingsWithCats[i].ip, image);
  //   //     } catch (e) {
  //   //       throw e;
  //   //     }
  //   //   }
  //   // }
  // }
}
