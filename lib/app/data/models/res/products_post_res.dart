import 'res_base.dart';
import 'package:dio/dio.dart';

class ProductsPostRes extends ResBase {
  bool isCreated;
  int productId;

  ProductsPostRes({this.isCreated, this.productId});

  ProductsPostRes.resError(Response response) : super.resError(response);
  ProductsPostRes.unKnownError() : super.unKnownError();

  ProductsPostRes.fromJson(Map<String, dynamic> json) {
    isCreated = json['is_created'];
    productId = json['product_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_created'] = this.isCreated;
    data['product_id'] = this.productId;
    return data;
  }
}