import 'res_base.dart';
import 'package:dio/dio.dart';

class AdditionProductsDeleteRes extends ResBase {
  bool isDeleted;

  AdditionProductsDeleteRes({this.isDeleted});

  AdditionProductsDeleteRes.resError(Response response) : super.resError(response);
  AdditionProductsDeleteRes.unKnownError() : super.unKnownError();

  AdditionProductsDeleteRes.fromJson(Map<String, dynamic> json) : super.fromErrorJson(json) {
    isDeleted = json['is_deleted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_deleted'] = this.isDeleted;
    return data;
  }
}