// To parse this JSON data, do
//
//     final productsGetRes = productsGetResFromJson(jsonString);

import 'dart:convert';
import 'package:muyipork/app/data/models/other/pagination.dart';
import 'package:muyipork/extension.dart';

import 'res_base.dart';
import 'package:dio/dio.dart';

class ProductsGetRes extends ResBase {
  Pagination pagination;
  List<ProductInfo> data;

  ProductsGetRes({
    this.data,
    this.pagination,
  });

  ProductsGetRes.resError(Response response) : super.resError(response);
  ProductsGetRes.unKnownError() : super.unKnownError();

  factory ProductsGetRes.fromRawJson(String str) =>
      ProductsGetRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProductsGetRes.fromJson(Map<String, dynamic> json) => ProductsGetRes(
        data: json["data"] == null
            ? null
            : List<ProductInfo>.from(
                json["data"].map((x) => ProductInfo.fromJson(x))),
        pagination: json["pagination"] == null
            ? null
            : Pagination.fromJson(json["pagination"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data == null
            ? null
            : List<dynamic>.from(data.map((x) => x.toJson())),
        "pagination": pagination == null ? null : pagination.toJson(),
      };

  //是否有某個 Category 的商品
  bool hasACategoryProduct(int categoryId) {
    if (data != null) {
      for (int i = 0; i < data.length; ++i) {
        if (data[i].categoryId == categoryId) {
          return true;
        }
      }
    }
    return false;
  }

  //取得所有在此 cat 內的商品名稱
  List<String> getProductTitlesInCat(int categoryId) {
    List<String> returnList = [];
    if (data != null) {
      for (int i = 0; i < data.length; ++i) {
        if (data[i].categoryId == categoryId) {
          returnList.add(data[i].title);
        }
      }
    }
    return returnList;
  }

  //Get all product infos.
  List<ProductInfo> getProductInfosInTabWithStock(int categoryId) {
    List<ProductInfo> returnList = [];
    if (data != null) {
      for (int i = 0; i < data.length; ++i) {
        if (!(data[i].isSoldOut) && data[i].categoryId == categoryId) {
          returnList.add(data[i]);
        }
      }
    }
    return returnList;
  }

  num getProductPrice(int productId) {
    for (int i = 0; i < data.length; ++i) {
      if (data[i].productId == productId) {
        return data[i].price;
      }
    }
    // print('Oops! getProductPrice() productId[' + productId.toString() + '] cannot found price!');
    return 0;
  }

  bool isProductExist(int productId) {
    for (int i = 0; i < data.length; ++i) {
      if (data[i].productId == productId) {
        return true;
      }
    }
    return false;
  }

  //取得產品庫存 (可以用於比對是否售完)
  num getProductStock(int productId) {
    for (int i = 0; i < data.length; ++i) {
      if (data[i].productId == productId) {
        return data[i].stock;
      }
    }
    // print('Oops! getProductPrice() productId[' + productId.toString() + '] cannot found price!');
    return 0;
  }
}
