import 'package:muyipork/app/data/models/res/res_base.dart';
import 'package:dio/dio.dart';

class OrdersPostRes extends ResBase {
  bool isCreated;
  int orderId;

  OrdersPostRes({this.isCreated, this.orderId});

  OrdersPostRes.resError(Response response) : super.resError(response);
  OrdersPostRes.unKnownError() : super.unKnownError();

  OrdersPostRes.fromJson(Map<String, dynamic> json) {
    isCreated = json['is_created'];
    orderId = json['order_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_created'] = this.isCreated;
    data['order_id'] = this.orderId;
    return data;
  }
}