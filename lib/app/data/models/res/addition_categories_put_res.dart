import 'res_base.dart';
import 'package:dio/dio.dart';

class AdditionCategoriesPutRes extends ResBase {
  bool isUpdated;
  int additionCategoryId;

  AdditionCategoriesPutRes({this.isUpdated, this.additionCategoryId});

  AdditionCategoriesPutRes.resError(Response response) : super.resError(response);
  AdditionCategoriesPutRes.unKnownError() : super.unKnownError();

  AdditionCategoriesPutRes.fromJson(Map<String, dynamic> json) : super.fromErrorJson(json) {
    isUpdated = json['is_updated'];
    additionCategoryId = json['addition_category_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_updated'] = this.isUpdated;
    data['addition_category_id'] = this.additionCategoryId;
    return data;
  }
}