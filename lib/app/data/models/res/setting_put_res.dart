import 'res_base.dart';
import 'package:dio/dio.dart';

class SettingPutRes extends ResBase {
  bool isUpdated;
  int channelId;

  SettingPutRes({this.isUpdated, this.channelId});

  SettingPutRes.resError(Response response) : super.resError(response);
  SettingPutRes.unKnownError() : super.unKnownError();

  SettingPutRes.fromJson(Map<String, dynamic> json) {
    isUpdated = json['is_updated'];
    channelId = json['channel_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['is_updated'] = this.isUpdated;
    data['channel_id'] = this.channelId;
    return data;
  }
}