// To parse this JSON data, do
//
//     final memberReq = memberReqFromJson(jsonString);

import 'dart:convert';

class MemberReq {
  MemberReq({
    this.page,
    this.limit,
    this.keyword,
    this.hasMore,
  });

  num page;
  num limit;
  String keyword;
  bool hasMore;

  MemberReq copyWith({
    num page,
    num limit,
    String keyword,
    bool hasMore,
  }) =>
      MemberReq(
        page: page ?? this.page,
        limit: limit ?? this.limit,
        keyword: keyword ?? this.keyword,
        hasMore: hasMore ?? this.hasMore,
      );

  factory MemberReq.fromRawJson(String str) =>
      MemberReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MemberReq.fromJson(Map<String, dynamic> json) => MemberReq(
        page: json["page"],
        limit: json["limit"],
        keyword: json["keyword"],
        hasMore: json["has_more"],
      );

  Map<String, dynamic> toJson() => {
        "page": page,
        "limit": limit,
        "keyword": keyword,
        "has_more": hasMore,
      };
}
