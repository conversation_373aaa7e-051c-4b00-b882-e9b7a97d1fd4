// To parse this JSON data, do
//
//     final productCategoriesPut = productCategoriesPutFromJson(jsonString);

import 'dart:convert';

class ProductCategoriesPut {
  ProductCategoriesPut({
    this.kind,
    this.categoryId,
    this.productId,
    this.sort,
  });

  num kind;
  num categoryId;
  num productId;
  num sort;

  ProductCategoriesPut copyWith({
    num kind,
    num categoryId,
    num productId,
    num sort,
  }) =>
      ProductCategoriesPut(
        kind: kind ?? this.kind,
        categoryId: categoryId ?? this.categoryId,
        productId: productId ?? this.productId,
        sort: sort ?? this.sort,
      );

  factory ProductCategoriesPut.fromRawJson(String str) =>
      ProductCategoriesPut.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProductCategoriesPut.fromJson(Map<String, dynamic> json) =>
      ProductCategoriesPut(
        kind: json["kind"] == null ? null : json["kind"],
        categoryId: json["category_id"] == null ? null : json["category_id"],
        productId: json["product_id"] == null ? null : json["product_id"],
        sort: json["sort"] == null ? null : json["sort"],
      );

  Map<String, dynamic> toJson() => {
        "kind": kind == null ? null : kind,
        "category_id": categoryId == null ? null : categoryId,
        "product_id": productId == null ? null : productId,
        "sort": sort == null ? null : sort,
      };
}
