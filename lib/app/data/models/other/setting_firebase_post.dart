// To parse this JSON data, do
//
//     final settingFirebasePost = settingFirebasePostFromJson(jsonString);

import 'dart:convert';

class SettingFirebasePost {
  SettingFirebasePost({
    this.machineNo,
    this.token,
    this.status,
  });

  String machineNo;
  String token;
  num status;

  SettingFirebasePost copyWith({
    String machineNo,
    String token,
    num status,
  }) =>
      SettingFirebasePost(
        machineNo: machineNo ?? this.machineNo,
        token: token ?? this.token,
        status: status ?? this.status,
      );

  factory SettingFirebasePost.fromRawJson(String str) =>
      SettingFirebasePost.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SettingFirebasePost.fromJson(Map<String, dynamic> json) =>
      SettingFirebasePost(
        machineNo: json["machine_no"] == null ? null : json["machine_no"],
        token: json["token"] == null ? null : json["token"],
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "machine_no": machineNo == null ? null : machineNo,
        "token": token == null ? null : token,
        "status": status == null ? null : status,
      };
}
