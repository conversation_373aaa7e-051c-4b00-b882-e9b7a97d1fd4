// To parse this JSON data, do
//
//     final settingAll = settingAllFromJson(jsonString);

import 'dart:convert';

class SettingAll {
  SettingAll({
    this.info,
    this.payment,
    this.shipping,
  });

  Info info;
  Payment payment;
  Shipping shipping;

  SettingAll copyWith({
    Info info,
    Payment payment,
    Shipping shipping,
  }) =>
      SettingAll(
        info: info ?? this.info,
        payment: payment ?? this.payment,
        shipping: shipping ?? this.shipping,
      );

  factory SettingAll.fromRawJson(String str) =>
      SettingAll.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SettingAll.fromJson(Map<String, dynamic> json) => SettingAll(
        info: json["info"] == null ? null : Info.fromJson(json["info"]),
        payment:
            json["payment"] == null ? null : Payment.fromJson(json["payment"]),
        shipping: json["shipping"] == null
            ? null
            : Shipping.fromJson(json["shipping"]),
      );

  Map<String, dynamic> toJson() => {
        "info": info == null ? null : info.toJson(),
        "payment": payment == null ? null : payment.toJson(),
        "shipping": shipping == null ? null : shipping.toJson(),
      };
}

class Info {
  Info({
    this.businessHours,
    this.other,
  });

  BusinessHours businessHours;
  Other other;

  Info copyWith({
    BusinessHours businessHours,
    Other other,
  }) =>
      Info(
        businessHours: businessHours ?? this.businessHours,
        other: other ?? this.other,
      );

  factory Info.fromRawJson(String str) => Info.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Info.fromJson(Map<String, dynamic> json) => Info(
        businessHours: json["business_hours"] == null
            ? null
            : BusinessHours.fromJson(json["business_hours"]),
        other: json["other"] == null ? null : Other.fromJson(json["other"]),
      );

  Map<String, dynamic> toJson() => {
        "business_hours": businessHours == null ? null : businessHours.toJson(),
        "other": other == null ? null : other.toJson(),
      };
}

class BusinessHours {
  BusinessHours({
    this.comment,
    this.week,
  });

  String comment;
  List<Week> week;

  BusinessHours copyWith({
    String comment,
    List<Week> week,
  }) =>
      BusinessHours(
        comment: comment ?? this.comment,
        week: week ?? this.week,
      );

  factory BusinessHours.fromRawJson(String str) =>
      BusinessHours.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory BusinessHours.fromJson(Map<String, dynamic> json) => BusinessHours(
        comment: json["comment"] == null ? null : json["comment"],
        week: json["week"] == null
            ? null
            : List<Week>.from(json["week"].map((x) => Week.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "comment": comment == null ? null : comment,
        "week": week == null
            ? null
            : List<dynamic>.from(week.map((x) => x.toJson())),
      };
}

class Week {
  Week({
    this.day,
    this.hours,
    this.status,
  });

  num day;
  List<Hour> hours;
  num status;

  Week copyWith({
    num day,
    List<Hour> hours,
    num status,
  }) =>
      Week(
        day: day ?? this.day,
        hours: hours ?? this.hours,
        status: status ?? this.status,
      );

  factory Week.fromRawJson(String str) => Week.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Week.fromJson(Map<String, dynamic> json) => Week(
        day: json["day"] == null ? null : json["day"],
        hours: json["hours"] == null
            ? null
            : List<Hour>.from(json["hours"].map((x) => Hour.fromJson(x))),
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "day": day == null ? null : day,
        "hours": hours == null
            ? null
            : List<dynamic>.from(hours.map((x) => x.toJson())),
        "status": status == null ? null : status,
      };
}

class Hour {
  Hour({
    this.close,
    this.open,
  });

  String close;
  String open;

  Hour copyWith({
    String close,
    String open,
  }) =>
      Hour(
        close: close ?? this.close,
        open: open ?? this.open,
      );

  factory Hour.fromRawJson(String str) => Hour.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Hour.fromJson(Map<String, dynamic> json) => Hour(
        close: json["close"] == null ? null : json["close"],
        open: json["open"] == null ? null : json["open"],
      );

  Map<String, dynamic> toJson() => {
        "close": close == null ? null : close,
        "open": open == null ? null : open,
      };
}

class Other {
  Other({
    this.auto,
    this.checkoutType,
    this.fee,
    this.lineOrder,
    this.printDetail,
    this.printOrder,
  });

  Auto auto;
  num checkoutType;
  Fee fee;
  LineOrder lineOrder;
  num printDetail;
  num printOrder;

  Other copyWith({
    Auto auto,
    num checkoutType,
    Fee fee,
    LineOrder lineOrder,
    num printDetail,
    num printOrder,
  }) =>
      Other(
        auto: auto ?? this.auto,
        checkoutType: checkoutType ?? this.checkoutType,
        fee: fee ?? this.fee,
        lineOrder: lineOrder ?? this.lineOrder,
        printDetail: printDetail ?? this.printDetail,
        printOrder: printOrder ?? this.printOrder,
      );

  factory Other.fromRawJson(String str) => Other.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Other.fromJson(Map<String, dynamic> json) => Other(
        auto: json["auto"] == null ? null : Auto.fromJson(json["auto"]),
        checkoutType:
            json["checkout_type"] == null ? null : json["checkout_type"],
        fee: json["fee"] == null ? null : Fee.fromJson(json["fee"]),
        lineOrder: json["line_order"] == null
            ? null
            : LineOrder.fromJson(json["line_order"]),
        printDetail: json["print_detail"] == null ? null : json["print_detail"],
        printOrder: json["print_order"] == null ? null : json["print_order"],
      );

  Map<String, dynamic> toJson() => {
        "auto": auto == null ? null : auto.toJson(),
        "checkout_type": checkoutType == null ? null : checkoutType,
        "fee": fee == null ? null : fee.toJson(),
        "line_order": lineOrder == null ? null : lineOrder.toJson(),
        "print_detail": printDetail == null ? null : printDetail,
        "print_order": printOrder == null ? null : printOrder,
      };
}

class Auto {
  Auto({
    this.abandon,
    this.abandonMin,
    this.close,
    this.closeMin,
    this.order,
    this.orderPrint,
  });

  num abandon;
  num abandonMin;
  num close;
  num closeMin;
  num order;
  num orderPrint;

  Auto copyWith({
    num abandon,
    num abandonMin,
    num close,
    num closeMin,
    num order,
    num orderPrint,
  }) =>
      Auto(
        abandon: abandon ?? this.abandon,
        abandonMin: abandonMin ?? this.abandonMin,
        close: close ?? this.close,
        closeMin: closeMin ?? this.closeMin,
        order: order ?? this.order,
        orderPrint: orderPrint ?? this.orderPrint,
      );

  factory Auto.fromRawJson(String str) => Auto.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Auto.fromJson(Map<String, dynamic> json) => Auto(
        abandon: json["abandon"] == null ? null : json["abandon"],
        abandonMin: json["abandon_min"] == null ? null : json["abandon_min"],
        close: json["close"] == null ? null : json["close"],
        closeMin: json["close_min"] == null ? null : json["close_min"],
        order: json["order"] == null ? null : json["order"],
        orderPrint: json["order_print"] == null ? null : json["order_print"],
      );

  Map<String, dynamic> toJson() => {
        "abandon": abandon == null ? null : abandon,
        "abandon_min": abandonMin == null ? null : abandonMin,
        "close": close == null ? null : close,
        "close_min": closeMin == null ? null : closeMin,
        "order": order == null ? null : order,
        "order_print": orderPrint == null ? null : orderPrint,
      };
}

class Fee {
  Fee({
    this.percent,
    this.type,
  });

  num percent;
  num type;

  Fee copyWith({
    num percent,
    num type,
  }) =>
      Fee(
        percent: percent ?? this.percent,
        type: type ?? this.type,
      );

  factory Fee.fromRawJson(String str) => Fee.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Fee.fromJson(Map<String, dynamic> json) => Fee(
        percent: json["percent"] == null ? null : json["percent"],
        type: json["type"] == null ? null : json["type"],
      );

  Map<String, dynamic> toJson() => {
        "percent": percent == null ? null : percent,
        "type": type == null ? null : type,
      };
}

class LineOrder {
  LineOrder({
    this.delivery,
    this.dineIn,
    this.limit,
    this.preparationMin,
    this.toGo,
  });

  num delivery;
  num dineIn;
  num limit;
  num preparationMin;
  num toGo;

  LineOrder copyWith({
    num delivery,
    num dineIn,
    num limit,
    num preparationMin,
    num toGo,
  }) =>
      LineOrder(
        delivery: delivery ?? this.delivery,
        dineIn: dineIn ?? this.dineIn,
        limit: limit ?? this.limit,
        preparationMin: preparationMin ?? this.preparationMin,
        toGo: toGo ?? this.toGo,
      );

  factory LineOrder.fromRawJson(String str) =>
      LineOrder.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LineOrder.fromJson(Map<String, dynamic> json) => LineOrder(
        delivery: json["delivery"] == null ? null : json["delivery"],
        dineIn: json["dine_in"] == null ? null : json["dine_in"],
        limit: json["limit"] == null ? null : json["limit"],
        preparationMin:
            json["preparation_min"] == null ? null : json["preparation_min"],
        toGo: json["to_go"] == null ? null : json["to_go"],
      );

  Map<String, dynamic> toJson() => {
        "delivery": delivery == null ? null : delivery,
        "dine_in": dineIn == null ? null : dineIn,
        "limit": limit == null ? null : limit,
        "preparation_min": preparationMin == null ? null : preparationMin,
        "to_go": toGo == null ? null : toGo,
      };
}

class Payment {
  Payment({
    this.bank,
    this.cod,
    this.instore,
  });

  Bank bank;
  Cod cod;
  Instore instore;

  Payment copyWith({
    Bank bank,
    Cod cod,
    Instore instore,
  }) =>
      Payment(
        bank: bank ?? this.bank,
        cod: cod ?? this.cod,
        instore: instore ?? this.instore,
      );

  factory Payment.fromRawJson(String str) => Payment.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Payment.fromJson(Map<String, dynamic> json) => Payment(
        bank: json["bank"] == null ? null : Bank.fromJson(json["bank"]),
        cod: json["cod"] == null ? null : Cod.fromJson(json["cod"]),
        instore:
            json["instore"] == null ? null : Instore.fromJson(json["instore"]),
      );

  Map<String, dynamic> toJson() => {
        "bank": bank == null ? null : bank.toJson(),
        "cod": cod == null ? null : cod.toJson(),
        "instore": instore == null ? null : instore.toJson(),
      };
}

class Bank {
  Bank({
    this.account,
    this.accountName,
    this.branch,
    this.description,
    this.name,
    this.status,
  });

  String account;
  String accountName;
  String branch;
  String description;
  String name;
  num status;

  Bank copyWith({
    String account,
    String accountName,
    String branch,
    String description,
    String name,
    num status,
  }) =>
      Bank(
        account: account ?? this.account,
        accountName: accountName ?? this.accountName,
        branch: branch ?? this.branch,
        description: description ?? this.description,
        name: name ?? this.name,
        status: status ?? this.status,
      );

  factory Bank.fromRawJson(String str) => Bank.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Bank.fromJson(Map<String, dynamic> json) => Bank(
        account: json["account"] == null ? null : json["account"],
        accountName: json["account_name"] == null ? null : json["account_name"],
        branch: json["branch"] == null ? null : json["branch"],
        description: json["description"] == null ? null : json["description"],
        name: json["name"] == null ? null : json["name"],
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "account": account == null ? null : account,
        "account_name": accountName == null ? null : accountName,
        "branch": branch == null ? null : branch,
        "description": description == null ? null : description,
        "name": name == null ? null : name,
        "status": status == null ? null : status,
      };
}

class Cod {
  Cod({
    this.description,
    this.paymentFee,
    this.paymentFree,
    this.status,
  });

  String description;
  num paymentFee;
  num paymentFree;
  num status;

  Cod copyWith({
    String description,
    num paymentFee,
    num paymentFree,
    num status,
  }) =>
      Cod(
        description: description ?? this.description,
        paymentFee: paymentFee ?? this.paymentFee,
        paymentFree: paymentFree ?? this.paymentFree,
        status: status ?? this.status,
      );

  factory Cod.fromRawJson(String str) => Cod.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Cod.fromJson(Map<String, dynamic> json) => Cod(
        description: json["description"] == null ? null : json["description"],
        paymentFee: json["payment_fee"] == null ? null : json["payment_fee"],
        paymentFree: json["payment_free"] == null ? null : json["payment_free"],
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "description": description == null ? null : description,
        "payment_fee": paymentFee == null ? null : paymentFee,
        "payment_free": paymentFree == null ? null : paymentFree,
        "status": status == null ? null : status,
      };
}

class Instore {
  Instore({
    this.description,
    this.status,
  });

  String description;
  num status;

  Instore copyWith({
    String description,
    num status,
  }) =>
      Instore(
        description: description ?? this.description,
        status: status ?? this.status,
      );

  factory Instore.fromRawJson(String str) => Instore.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Instore.fromJson(Map<String, dynamic> json) => Instore(
        description: json["description"] == null ? null : json["description"],
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "description": description == null ? null : description,
        "status": status == null ? null : status,
      };
}

class Shipping {
  Shipping({
    this.delivery,
    this.instore,
  });

  Delivery delivery;
  Instore instore;

  Shipping copyWith({
    Delivery delivery,
    Instore instore,
  }) =>
      Shipping(
        delivery: delivery ?? this.delivery,
        instore: instore ?? this.instore,
      );

  factory Shipping.fromRawJson(String str) =>
      Shipping.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Shipping.fromJson(Map<String, dynamic> json) => Shipping(
        delivery: json["delivery"] == null
            ? null
            : Delivery.fromJson(json["delivery"]),
        instore:
            json["instore"] == null ? null : Instore.fromJson(json["instore"]),
      );

  Map<String, dynamic> toJson() => {
        "delivery": delivery == null ? null : delivery.toJson(),
        "instore": instore == null ? null : instore.toJson(),
      };
}

class Delivery {
  Delivery({
    this.amb,
    this.cold,
  });

  Amb amb;
  Amb cold;

  Delivery copyWith({
    Amb amb,
    Amb cold,
  }) =>
      Delivery(
        amb: amb ?? this.amb,
        cold: cold ?? this.cold,
      );

  factory Delivery.fromRawJson(String str) =>
      Delivery.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Delivery.fromJson(Map<String, dynamic> json) => Delivery(
        amb: json["amb"] == null ? null : Amb.fromJson(json["amb"]),
        cold: json["cold"] == null ? null : Amb.fromJson(json["cold"]),
      );

  Map<String, dynamic> toJson() => {
        "amb": amb == null ? null : amb.toJson(),
        "cold": cold == null ? null : cold.toJson(),
      };
}

class Amb {
  Amb({
    this.description,
    this.shippingFee,
    this.shippingFree,
    this.status,
  });

  String description;
  num shippingFee;
  num shippingFree;
  num status;

  Amb copyWith({
    String description,
    num shippingFee,
    num shippingFree,
    num status,
  }) =>
      Amb(
        description: description ?? this.description,
        shippingFee: shippingFee ?? this.shippingFee,
        shippingFree: shippingFree ?? this.shippingFree,
        status: status ?? this.status,
      );

  factory Amb.fromRawJson(String str) => Amb.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Amb.fromJson(Map<String, dynamic> json) => Amb(
        description: json["description"] == null ? null : json["description"],
        shippingFee: json["shipping_fee"] == null ? null : json["shipping_fee"],
        shippingFree:
            json["shipping_free"] == null ? null : json["shipping_free"],
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "description": description == null ? null : description,
        "shipping_fee": shippingFee == null ? null : shippingFee,
        "shipping_free": shippingFree == null ? null : shippingFree,
        "status": status == null ? null : status,
      };
}
