// To parse this JSON data, do
//
//     final shippingDelivery = shippingDeliveryFromJson(jsonString);

import 'dart:convert';

class ShippingDelivery {
  ShippingDelivery({
    this.amb,
    this.cold,
  });

  Amb amb;
  Amb cold;

  ShippingDelivery copyWith({
    Amb amb,
    Amb cold,
  }) =>
      ShippingDelivery(
        amb: amb ?? this.amb,
        cold: cold ?? this.cold,
      );

  factory ShippingDelivery.fromRawJson(String str) =>
      ShippingDelivery.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ShippingDelivery.fromJson(Map<String, dynamic> json) =>
      ShippingDelivery(
        amb: json["amb"] == null ? null : Amb.fromJson(json["amb"]),
        cold: json["cold"] == null ? null : Amb.fromJson(json["cold"]),
      );

  Map<String, dynamic> toJson() => {
        "amb": amb == null ? null : amb.toJson(),
        "cold": cold == null ? null : cold.toJson(),
      };
}

class Amb {
  Amb({
    this.description,
    this.shippingFee,
    this.shippingFree,
    this.status,
    this.preOrderAfterDays,
    this.preOrderWithinDays,
  });

  String description;
  num shippingFee;
  num shippingFree;
  num status;
  num preOrderAfterDays;
  num preOrderWithinDays;

  Amb copyWith({
    String description,
    num shippingFee,
    num shippingFree,
    num status,
    num preOrderAfterDays,
    num preOrderWithinDays,
  }) =>
      Amb(
        description: description ?? this.description,
        shippingFee: shippingFee ?? this.shippingFee,
        shippingFree: shippingFree ?? this.shippingFree,
        status: status ?? this.status,
        preOrderAfterDays: preOrderAfterDays ?? this.preOrderAfterDays,
        preOrderWithinDays: preOrderWithinDays ?? this.preOrderWithinDays,
      );

  factory Amb.fromRawJson(String str) => Amb.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Amb.fromJson(Map<String, dynamic> json) => Amb(
        description: json["description"] == null ? null : json["description"],
        shippingFee: json["shipping_fee"] == null ? null : json["shipping_fee"],
        shippingFree:
            json["shipping_free"] == null ? null : json["shipping_free"],
        status: json["status"] == null ? null : json["status"],
        preOrderAfterDays: json["pre_order_after_days"] == null
            ? null
            : json["pre_order_after_days"],
        preOrderWithinDays: json["pre_order_within_days"] == null
            ? null
            : json["pre_order_within_days"],
      );

  Map<String, dynamic> toJson() => {
        "description": description == null ? null : description,
        "shipping_fee": shippingFee == null ? null : shippingFee,
        "shipping_free": shippingFree == null ? null : shippingFree,
        "status": status == null ? null : status,
        "pre_order_after_days":
            preOrderAfterDays == null ? null : preOrderAfterDays,
        "pre_order_within_days":
            preOrderWithinDays == null ? null : preOrderWithinDays,
      };
}
