// To parse this JSON data, do
//
//     final jwt = jwtFromJson(jsonString);

import 'dart:convert';

Jwt jwtFromJson(String str) => Jwt.fromJson(json.decode(str));

String jwtToJson(Jwt data) => json.encode(data.toJson());

class Jwt {
  Jwt({
    this.clientId,
    this.timezone,
    this.lastLogin,
    this.id,
    this.brandId,
    this.name,
    this.channelId,
    this.role,
    this.sysType,
    this.tokenType,
    this.iat,
    this.exp,
  });

  num clientId;
  String timezone;
  String lastLogin;
  num id;
  num brandId;
  String name;
  num channelId;
  Role role;
  String sysType;
  String tokenType;
  num iat;
  num exp;

  factory Jwt.fromJson(Map<String, dynamic> json) => Jwt(
        clientId: json["client_id"] == null ? null : json["client_id"],
        timezone: json["timezone"] == null ? null : json["timezone"],
        lastLogin: json["last_login"] == null ? null : json["last_login"],
        id: json["id"] == null ? null : json["id"],
        brandId: json["brand_id"] == null ? null : json["brand_id"],
        name: json["name"] == null ? null : json["name"],
        channelId: json["channel_id"] == null ? null : json["channel_id"],
        role: json["role"] == null ? null : Role.fromJson(json["role"]),
        sysType: json["sys_type"] == null ? null : json["sys_type"],
        tokenType: json["token_type"] == null ? null : json["token_type"],
        iat: json["iat"] == null ? null : json["iat"],
        exp: json["exp"] == null ? null : json["exp"],
      );

  Map<String, dynamic> toJson() => {
        "client_id": clientId == null ? null : clientId,
        "timezone": timezone == null ? null : timezone,
        "last_login": lastLogin == null ? null : lastLogin,
        "id": id == null ? null : id,
        "brand_id": brandId == null ? null : brandId,
        "name": name == null ? null : name,
        "channel_id": channelId == null ? null : channelId,
        "role": role == null ? null : role.toJson(),
        "sys_type": sysType == null ? null : sysType,
        "token_type": tokenType == null ? null : tokenType,
        "iat": iat == null ? null : iat,
        "exp": exp == null ? null : exp,
      };
}

class Role {
  Role({
    this.name,
    this.id,
  });

  String name;
  num id;

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        name: json["name"] == null ? null : json["name"],
        id: json["id"] == null ? null : json["id"],
      );

  Map<String, dynamic> toJson() => {
        "name": name == null ? null : name,
        "id": id == null ? null : id,
      };
}
