// // To parse this JSON data, do
// //
// //     final order = orderFromJson(jsonString);
//
// import 'dart:convert';
//
// Order orderFromJson(String str) => Order.fromJson(json.decode(str));
//
// String orderToJson(Order data) => json.encode(data.toJson());
//
// class Order {
//   Order({
//     this.adult,
//     this.child,
//     this.createdAt,
//     this.id,
//     this.itemCount,
//     this.masterId,
//     this.mealAt,
//     this.orderNumber,
//     this.source,
//     this.status,
//     this.table1Id,
//     this.table1Name,
//     this.table2Id,
//     this.table2Name,
//     this.total,
//     this.type,
//   });
//
//   int adult;
//   int child;
//   String createdAt;
//   int id;
//   double itemCount;
//   int masterId;
//   String mealAt;
//   String orderNumber;
//
//   //來源
//   // 0：店員點餐
//   // 1：LINE 點餐
//   int source;
//
//   //狀態
//   // 0：處理中
//   // 1：已確認
//   // 2：訂單完成
//   // 3：訂單取消 （店家）
//   // 4：訂單異常
//   // 5：訂單退貨、退款
//   // 6：訂單取消（消費者）
//   int status;
//
//   int table1Id;
//   String table1Name;
//   int table2Id;
//   String table2Name;
//   double total;
//
// 0：餐飲: 內用
// 1：餐飲: 店內->外帶 / 餐飲: 線上->自取
// 2：餐飲: 外送
// 3：零售: 自取
// 4：零售: 宅配
// 5：零售: 超商
//   int type;
//
//   factory Order.fromJson(Map<String, dynamic> json) => Order(
//         adult: json["adult"],
//         child: json["child"],
//         createdAt: json["created_at"],
//         id: json["id"],
//         itemCount: json["item_count"],
//         masterId: json["master_id"],
//         mealAt: json["meal_at"],
//         orderNumber: json["order_number"],
//         source: json["source"],
//         status: json["status"],
//         table1Id: json["table1_id"],
//         table1Name: json["table1_name"],
//         table2Id: json["table2_id"],
//         table2Name: json["table2_name"],
//         total: json["total"],
//         type: json["type"],
//       );
//
//   Map<String, dynamic> toJson() => {
//         "adult": adult,
//         "child": child,
//         "created_at": createdAt,
//         "id": id,
//         "item_count": itemCount,
//         "master_id": masterId,
//         "meal_at": mealAt,
//         "order_number": orderNumber,
//         "source": source,
//         "status": status,
//         "table1_id": table1Id,
//         "table1_name": table1Name,
//         "table2_id": table2Id,
//         "table2_name": table2Name,
//         "total": total,
//         "type": type,
//       };
// }
