// To parse this JSON data, do
//
//     final memberReports = memberReportsFromJson(jsonString);

import 'dart:convert';

class MemberReports {
  MemberReports({
    this.dinerAppAmount,
    this.dinerOnlineAmount,
    this.dinerTotal,
    this.lastOrder,
    this.shippings,
    this.storeAppAmount,
    this.storeOnlineAmount,
    this.storeTotal,
    this.total,
  });

  num dinerAppAmount;
  num dinerOnlineAmount;
  num dinerTotal;
  LastOrder lastOrder;
  List<Shipping> shippings;
  num storeAppAmount;
  num storeOnlineAmount;
  num storeTotal;
  num total;

  MemberReports copyWith({
    num dinerAppAmount,
    num dinerOnlineAmount,
    num dinerTotal,
    LastOrder lastOrder,
    List<Shipping> shippings,
    num storeAppAmount,
    num storeOnlineAmount,
    num storeTotal,
    num total,
  }) =>
      MemberReports(
        dinerAppAmount: dinerAppAmount ?? this.dinerAppAmount,
        dinerOnlineAmount: dinerOnlineAmount ?? this.dinerOnlineAmount,
        dinerTotal: dinerTotal ?? this.dinerTotal,
        lastOrder: lastOrder ?? this.lastOrder,
        shippings: shippings ?? this.shippings,
        storeAppAmount: storeAppAmount ?? this.storeAppAmount,
        storeOnlineAmount: storeOnlineAmount ?? this.storeOnlineAmount,
        storeTotal: storeTotal ?? this.storeTotal,
        total: total ?? this.total,
      );

  factory MemberReports.fromRawJson(String str) =>
      MemberReports.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MemberReports.fromJson(Map<String, dynamic> json) => MemberReports(
        dinerAppAmount:
            json["diner_app_amount"] == null ? null : json["diner_app_amount"],
        dinerOnlineAmount: json["diner_online_amount"] == null
            ? null
            : json["diner_online_amount"],
        dinerTotal: json["diner_total"] == null ? null : json["diner_total"],
        lastOrder: json["last_order"] == null
            ? null
            : LastOrder.fromJson(json["last_order"]),
        shippings: json["shippings"] == null
            ? null
            : List<Shipping>.from(
                json["shippings"].map((x) => Shipping.fromJson(x))),
        storeAppAmount:
            json["store_app_amount"] == null ? null : json["store_app_amount"],
        storeOnlineAmount: json["store_online_amount"] == null
            ? null
            : json["store_online_amount"],
        storeTotal: json["store_total"] == null ? null : json["store_total"],
        total: json["total"] == null ? null : json["total"],
      );

  Map<String, dynamic> toJson() => {
        "diner_app_amount": dinerAppAmount == null ? null : dinerAppAmount,
        "diner_online_amount":
            dinerOnlineAmount == null ? null : dinerOnlineAmount,
        "diner_total": dinerTotal == null ? null : dinerTotal,
        "last_order": lastOrder == null ? null : lastOrder.toJson(),
        "shippings": shippings == null
            ? null
            : List<dynamic>.from(shippings.map((x) => x.toJson())),
        "store_app_amount": storeAppAmount == null ? null : storeAppAmount,
        "store_online_amount":
            storeOnlineAmount == null ? null : storeOnlineAmount,
        "store_total": storeTotal == null ? null : storeTotal,
        "total": total == null ? null : total,
      };
}

class LastOrder {
  LastOrder({
    this.createdAt,
    this.id,
    this.orderNumber,
  });

  String createdAt;
  num id;
  String orderNumber;

  LastOrder copyWith({
    String createdAt,
    num id,
    String orderNumber,
  }) =>
      LastOrder(
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
        orderNumber: orderNumber ?? this.orderNumber,
      );

  factory LastOrder.fromRawJson(String str) =>
      LastOrder.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LastOrder.fromJson(Map<String, dynamic> json) => LastOrder(
        createdAt: json["created_at"] == null ? null : json["created_at"],
        id: json["id"] == null ? null : json["id"],
        orderNumber: json["order_number"] == null ? null : json["order_number"],
      );

  Map<String, dynamic> toJson() => {
        "created_at": createdAt == null ? null : createdAt,
        "id": id == null ? null : id,
        "order_number": orderNumber == null ? null : orderNumber,
      };
}

class Shipping {
  Shipping({
    this.orderCount,
    this.source,
    this.type,
  });

  num orderCount;
  num source;
  num type;

  Shipping copyWith({
    num orderCount,
    num source,
    num type,
  }) =>
      Shipping(
        orderCount: orderCount ?? this.orderCount,
        source: source ?? this.source,
        type: type ?? this.type,
      );

  factory Shipping.fromRawJson(String str) =>
      Shipping.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Shipping.fromJson(Map<String, dynamic> json) => Shipping(
        orderCount: json["order_count"] == null ? null : json["order_count"],
        source: json["source"] == null ? null : json["source"],
        type: json["type"] == null ? null : json["type"],
      );

  Map<String, dynamic> toJson() => {
        "order_count": orderCount == null ? null : orderCount,
        "source": source == null ? null : source,
        "type": type == null ? null : type,
      };
}
