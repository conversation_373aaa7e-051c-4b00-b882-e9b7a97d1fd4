// To parse this JSON data, do
//
//     final brandsInfo = brandsInfoFromJson(jsonString);

import 'dart:convert';

class BrandsInfo {
  BrandsInfo({
    this.address,
    this.businessHours,
    this.city,
    this.cityarea,
    this.code,
    this.domain,
    this.lineAccessToken,
    this.lineBaseUrl,
    this.lineChannelId,
    this.lineId,
    this.lineLiffId,
    this.lineName,
    this.lineSecretCode,
    this.logoUrl,
    this.name,
    this.okshopDinerUrl,
    this.okshopShopUrl,
    this.other,
    this.phone,
    this.status,
    this.taxId,
    this.timezone,
    this.type,
    this.expiryDate,
  });

  String address;
  String businessHours;
  City city;
  City cityarea;
  String code;
  String domain;
  String lineAccessToken;
  String lineBaseUrl;
  String lineChannelId;
  String lineId;
  String lineLiffId;
  String lineName;
  String lineSecretCode;
  String logoUrl;
  String name;
  String okshopDinerUrl;
  String okshopShopUrl;
  String other;
  String phone;
  num status;
  String taxId;
  String timezone;
  num type;
  String expiryDate;

  BrandsInfo copyWith({
    String address,
    String businessHours,
    City city,
    City cityarea,
    String code,
    String domain,
    String lineAccessToken,
    String lineBaseUrl,
    String lineChannelId,
    String lineId,
    String lineLiffId,
    String lineName,
    String lineSecretCode,
    String logoUrl,
    String name,
    String okshopDinerUrl,
    String okshopShopUrl,
    String other,
    String phone,
    num status,
    String taxId,
    String timezone,
    num type,
    String expiryDate,
  }) =>
      BrandsInfo(
        address: address ?? this.address,
        businessHours: businessHours ?? this.businessHours,
        city: city ?? this.city,
        cityarea: cityarea ?? this.cityarea,
        code: code ?? this.code,
        domain: domain ?? this.domain,
        lineAccessToken: lineAccessToken ?? this.lineAccessToken,
        lineBaseUrl: lineBaseUrl ?? this.lineBaseUrl,
        lineChannelId: lineChannelId ?? this.lineChannelId,
        lineId: lineId ?? this.lineId,
        lineLiffId: lineLiffId ?? this.lineLiffId,
        lineName: lineName ?? this.lineName,
        lineSecretCode: lineSecretCode ?? this.lineSecretCode,
        logoUrl: logoUrl ?? this.logoUrl,
        name: name ?? this.name,
        okshopDinerUrl: okshopDinerUrl ?? this.okshopDinerUrl,
        okshopShopUrl: okshopShopUrl ?? this.okshopShopUrl,
        other: other ?? this.other,
        phone: phone ?? this.phone,
        status: status ?? this.status,
        taxId: taxId ?? this.taxId,
        timezone: timezone ?? this.timezone,
        type: type ?? this.type,
        expiryDate: expiryDate ?? this.expiryDate,
      );

  factory BrandsInfo.fromRawJson(String str) =>
      BrandsInfo.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory BrandsInfo.fromJson(Map<String, dynamic> json) => BrandsInfo(
        address: json["address"] == null ? null : json["address"],
        businessHours:
            json["business_hours"] == null ? null : json["business_hours"],
        city: json["city"] == null ? null : City.fromJson(json["city"]),
        cityarea:
            json["cityarea"] == null ? null : City.fromJson(json["cityarea"]),
        code: json["code"] == null ? null : json["code"],
        domain: json["domain"] == null ? null : json["domain"],
        lineAccessToken: json["line_access_token"] == null
            ? null
            : json["line_access_token"],
        lineBaseUrl:
            json["line_base_url"] == null ? null : json["line_base_url"],
        lineChannelId:
            json["line_channel_id"] == null ? null : json["line_channel_id"],
        lineId: json["line_id"] == null ? null : json["line_id"],
        lineLiffId: json["line_liff_id"] == null ? null : json["line_liff_id"],
        lineName: json["line_name"] == null ? null : json["line_name"],
        lineSecretCode:
            json["line_secret_code"] == null ? null : json["line_secret_code"],
        logoUrl: json["logo_url"] == null ? null : json["logo_url"],
        name: json["name"] == null ? null : json["name"],
        okshopDinerUrl:
            json["okshop_diner_url"] == null ? null : json["okshop_diner_url"],
        okshopShopUrl:
            json["okshop_shop_url"] == null ? null : json["okshop_shop_url"],
        other: json["other"] == null ? null : json["other"],
        phone: json["phone"] == null ? null : json["phone"],
        status: json["status"] == null ? null : json["status"],
        taxId: json["tax_id"] == null ? null : json["tax_id"],
        timezone: json["timezone"] == null ? null : json["timezone"],
        type: json["type"] == null ? null : json["type"],
        expiryDate: json["expiry_date"] == null ? null : json["expiry_date"],
      );

  Map<String, dynamic> toJson() => {
        "address": address == null ? null : address,
        "business_hours": businessHours == null ? null : businessHours,
        "city": city == null ? null : city.toJson(),
        "cityarea": cityarea == null ? null : cityarea.toJson(),
        "code": code == null ? null : code,
        "domain": domain == null ? null : domain,
        "line_access_token": lineAccessToken == null ? null : lineAccessToken,
        "line_base_url": lineBaseUrl == null ? null : lineBaseUrl,
        "line_channel_id": lineChannelId == null ? null : lineChannelId,
        "line_id": lineId == null ? null : lineId,
        "line_liff_id": lineLiffId == null ? null : lineLiffId,
        "line_name": lineName == null ? null : lineName,
        "line_secret_code": lineSecretCode == null ? null : lineSecretCode,
        "logo_url": logoUrl == null ? null : logoUrl,
        "name": name == null ? null : name,
        "okshop_diner_url": okshopDinerUrl == null ? null : okshopDinerUrl,
        "okshop_shop_url": okshopShopUrl == null ? null : okshopShopUrl,
        "other": other == null ? null : other,
        "phone": phone == null ? null : phone,
        "status": status == null ? null : status,
        "tax_id": taxId == null ? null : taxId,
        "timezone": timezone == null ? null : timezone,
        "type": type == null ? null : type,
        "expiry_date": expiryDate == null ? null : expiryDate,
      };
}

class City {
  City({
    this.createdAt,
    this.id,
    this.name,
    this.updatedAt,
    this.cityId,
  });

  String createdAt;
  num id;
  String name;
  String updatedAt;
  num cityId;

  City copyWith({
    String createdAt,
    num id,
    String name,
    String updatedAt,
    num cityId,
  }) =>
      City(
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
        name: name ?? this.name,
        updatedAt: updatedAt ?? this.updatedAt,
        cityId: cityId ?? this.cityId,
      );

  factory City.fromRawJson(String str) => City.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory City.fromJson(Map<String, dynamic> json) => City(
        createdAt: json["created_at"] == null ? null : json["created_at"],
        id: json["id"] == null ? null : json["id"],
        name: json["name"] == null ? null : json["name"],
        updatedAt: json["updated_at"] == null ? null : json["updated_at"],
        cityId: json["city_id"] == null ? null : json["city_id"],
      );

  Map<String, dynamic> toJson() => {
        "created_at": createdAt == null ? null : createdAt,
        "id": id == null ? null : id,
        "name": name == null ? null : name,
        "updated_at": updatedAt == null ? null : updatedAt,
        "city_id": cityId == null ? null : cityId,
      };
}
