// To parse this JSON data, do
//
//     final district = districtFromJson(jsonString);

import 'dart:convert';

class District {
  District({
    this.cityId,
    this.createdAt,
    this.id,
    this.name,
    this.updatedAt,
  });

  num cityId;
  String createdAt;
  num id;
  String name;
  String updatedAt;

  District copyWith({
    num cityId,
    String createdAt,
    num id,
    String name,
    String updatedAt,
  }) =>
      District(
        cityId: cityId ?? this.cityId,
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
        name: name ?? this.name,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  factory District.fromRawJson(String str) =>
      District.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory District.fromJson(Map<String, dynamic> json) => District(
        cityId: json["city_id"] == null ? null : json["city_id"],
        createdAt: json["created_at"] == null ? null : json["created_at"],
        id: json["id"] == null ? null : json["id"],
        name: json["name"] == null ? null : json["name"],
        updatedAt: json["updated_at"] == null ? null : json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "city_id": cityId == null ? null : cityId,
        "created_at": createdAt == null ? null : createdAt,
        "id": id == null ? null : id,
        "name": name == null ? null : name,
        "updated_at": updatedAt == null ? null : updatedAt,
      };
}
