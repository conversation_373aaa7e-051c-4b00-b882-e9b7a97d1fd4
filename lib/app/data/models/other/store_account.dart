// To parse this JSON data, do
//
//     final storeAccount = storeAccountFromJson(jsonString);

import 'dart:convert';

class StoreAccount {
  StoreAccount({
    this.brandId,
    this.channelId,
    this.clientId,
    this.comment,
    this.createdAt,
    this.id,
    this.lastLogin,
    this.name,
    this.role,
    this.status,
    this.updatedAt,
    this.username,
  });

  num brandId;
  num channelId;
  num clientId;
  String comment;
  String createdAt;
  num id;
  String lastLogin;
  String name;
  Role role;
  num status;
  String updatedAt;
  String username;

  StoreAccount copyWith({
    num brandId,
    num channelId,
    num clientId,
    String comment,
    String createdAt,
    num id,
    String lastLogin,
    String name,
    Role role,
    num status,
    String updatedAt,
    String username,
  }) =>
      StoreAccount(
        brandId: brandId ?? this.brandId,
        channelId: channelId ?? this.channelId,
        clientId: clientId ?? this.clientId,
        comment: comment ?? this.comment,
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
        lastLogin: lastLogin ?? this.lastLogin,
        name: name ?? this.name,
        role: role ?? this.role,
        status: status ?? this.status,
        updatedAt: updatedAt ?? this.updatedAt,
        username: username ?? this.username,
      );

  factory StoreAccount.fromRawJson(String str) =>
      StoreAccount.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StoreAccount.fromJson(Map<String, dynamic> json) => StoreAccount(
        brandId: json["brand_id"] == null ? null : json["brand_id"],
        channelId: json["channel_id"] == null ? null : json["channel_id"],
        clientId: json["client_id"] == null ? null : json["client_id"],
        comment: json["comment"] == null ? null : json["comment"],
        createdAt: json["created_at"] == null ? null : json["created_at"],
        id: json["id"] == null ? null : json["id"],
        lastLogin: json["last_login"] == null ? null : json["last_login"],
        name: json["name"] == null ? null : json["name"],
        role: json["role"] == null ? null : Role.fromJson(json["role"]),
        status: json["status"] == null ? null : json["status"],
        updatedAt: json["updated_at"] == null ? null : json["updated_at"],
        username: json["username"] == null ? null : json["username"],
      );

  Map<String, dynamic> toJson() => {
        "brand_id": brandId == null ? null : brandId,
        "channel_id": channelId == null ? null : channelId,
        "client_id": clientId == null ? null : clientId,
        "comment": comment == null ? null : comment,
        "created_at": createdAt == null ? null : createdAt,
        "id": id == null ? null : id,
        "last_login": lastLogin == null ? null : lastLogin,
        "name": name == null ? null : name,
        "role": role == null ? null : role.toJson(),
        "status": status == null ? null : status,
        "updated_at": updatedAt == null ? null : updatedAt,
        "username": username == null ? null : username,
      };
}

class Role {
  Role({
    this.id,
    this.name,
  });

  num id;
  String name;

  Role copyWith({
    num id,
    String name,
  }) =>
      Role(
        id: id ?? this.id,
        name: name ?? this.name,
      );

  factory Role.fromRawJson(String str) => Role.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        id: json["id"] == null ? null : json["id"],
        name: json["name"] == null ? null : json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "name": name == null ? null : name,
      };
}
