// To parse this JSON data, do
//
//     final paymentBankPut = paymentBankPutFromJson(jsonString);

import 'dart:convert';

class PaymentBankPut {
  PaymentBankPut({
    this.bankAccountName,
    this.bankBranch,
    this.codPaymentFee,
    this.bankAccount,
    this.codPaymentFree,
    this.bankName,
    this.instoreStatus,
    this.bankStatus,
    this.codDescription,
    this.codStatus,
    this.bankDescription,
    this.instoreDescription,
  });

  String bankAccountName;
  String bankBranch;
  num codPaymentFee;
  String bankAccount;
  num codPaymentFree;
  String bankName;
  num instoreStatus;
  num bankStatus;
  String codDescription;
  num codStatus;
  String bankDescription;
  String instoreDescription;

  PaymentBankPut copyWith({
    String bankAccountName,
    String bankBranch,
    num codPaymentFee,
    String bankAccount,
    num codPaymentFree,
    String bankName,
    num instoreStatus,
    num bankStatus,
    String codDescription,
    num codStatus,
    String bankDescription,
    String instoreDescription,
  }) =>
      PaymentBankPut(
        bankAccountName: bankAccountName ?? this.bankAccountName,
        bankBranch: bankBranch ?? this.bankBranch,
        codPaymentFee: codPaymentFee ?? this.codPaymentFee,
        bankAccount: bankAccount ?? this.bankAccount,
        codPaymentFree: codPaymentFree ?? this.codPaymentFree,
        bankName: bankName ?? this.bankName,
        instoreStatus: instoreStatus ?? this.instoreStatus,
        bankStatus: bankStatus ?? this.bankStatus,
        codDescription: codDescription ?? this.codDescription,
        codStatus: codStatus ?? this.codStatus,
        bankDescription: bankDescription ?? this.bankDescription,
        instoreDescription: instoreDescription ?? this.instoreDescription,
      );

  factory PaymentBankPut.fromRawJson(String str) =>
      PaymentBankPut.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PaymentBankPut.fromJson(Map<String, dynamic> json) => PaymentBankPut(
        bankAccountName: json["bank_account_name"] == null
            ? null
            : json["bank_account_name"],
        bankBranch: json["bank_branch"] == null ? null : json["bank_branch"],
        codPaymentFee:
            json["cod_payment_fee"] == null ? null : json["cod_payment_fee"],
        bankAccount: json["bank_account"] == null ? null : json["bank_account"],
        codPaymentFree:
            json["cod_payment_free"] == null ? null : json["cod_payment_free"],
        bankName: json["bank_name"] == null ? null : json["bank_name"],
        instoreStatus:
            json["instore_status"] == null ? null : json["instore_status"],
        bankStatus: json["bank_status"] == null ? null : json["bank_status"],
        codDescription:
            json["cod_description"] == null ? null : json["cod_description"],
        codStatus: json["cod_status"] == null ? null : json["cod_status"],
        bankDescription:
            json["bank_description"] == null ? null : json["bank_description"],
        instoreDescription: json["instore_description"] == null
            ? null
            : json["instore_description"],
      );

  Map<String, dynamic> toJson() => {
        "bank_account_name": bankAccountName == null ? null : bankAccountName,
        "bank_branch": bankBranch == null ? null : bankBranch,
        "cod_payment_fee": codPaymentFee == null ? null : codPaymentFee,
        "bank_account": bankAccount == null ? null : bankAccount,
        "cod_payment_free": codPaymentFree == null ? null : codPaymentFree,
        "bank_name": bankName == null ? null : bankName,
        "instore_status": instoreStatus == null ? null : instoreStatus,
        "bank_status": bankStatus == null ? null : bankStatus,
        "cod_description": codDescription == null ? null : codDescription,
        "cod_status": codStatus == null ? null : codStatus,
        "bank_description": bankDescription == null ? null : bankDescription,
        "instore_description":
            instoreDescription == null ? null : instoreDescription,
      };
}
