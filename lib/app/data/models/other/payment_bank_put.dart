// To parse this JSON data, do
//
//     final paymentBankPut = paymentBankPutFromJson(jsonString);

import 'dart:convert';

class PaymentBankPut {
  PaymentBankPut({
    this.status,
    this.name,
    this.branch,
    this.accountName,
    this.account,
    this.description,
  });

  num status;
  String name;
  String branch;
  String accountName;
  String account;
  String description;

  PaymentBankPut copyWith({
    num status,
    String name,
    String branch,
    String accountName,
    String account,
    String description,
  }) =>
      PaymentBankPut(
        status: status ?? this.status,
        name: name ?? this.name,
        branch: branch ?? this.branch,
        accountName: accountName ?? this.accountName,
        account: account ?? this.account,
        description: description ?? this.description,
      );

  factory PaymentBankPut.fromRawJson(String str) =>
      PaymentBankPut.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PaymentBankPut.fromJson(Map<String, dynamic> json) => PaymentBankPut(
        status: json["status"] == null ? null : json["status"],
        name: json["name"] == null ? null : json["name"],
        branch: json["branch"] == null ? null : json["branch"],
        accountName: json["account_name"] == null ? null : json["account_name"],
        account: json["account"] == null ? null : json["account"],
        description: json["description"] == null ? null : json["description"],
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "name": name == null ? null : name,
        "branch": branch == null ? null : branch,
        "account_name": accountName == null ? null : accountName,
        "account": account == null ? null : account,
        "description": description == null ? null : description,
      };
}
