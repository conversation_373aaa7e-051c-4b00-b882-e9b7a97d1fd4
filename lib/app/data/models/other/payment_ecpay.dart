// To parse this JSON data, do
//
//     final paymentEcpay = paymentEcpayFromJson(jsonString);

import 'dart:convert';

class PaymentEcpay {
  PaymentEcpay({
    this.creditStatus,
    this.description,
    this.setting,
  });

  num creditStatus;
  String description;
  Setting setting;

  PaymentEcpay copyWith({
    num creditStatus,
    String description,
    Setting setting,
  }) =>
      PaymentEcpay(
        creditStatus: creditStatus ?? this.creditStatus,
        description: description ?? this.description,
        setting: setting ?? this.setting,
      );

  factory PaymentEcpay.fromRawJson(String str) =>
      PaymentEcpay.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PaymentEcpay.fromJson(Map<String, dynamic> json) => PaymentEcpay(
        creditStatus:
            json["credit_status"] == null ? null : json["credit_status"],
        description: json["description"] == null ? null : json["description"],
        setting:
            json["setting"] == null ? null : Setting.fromJson(json["setting"]),
      );

  Map<String, dynamic> toJson() => {
        "credit_status": creditStatus == null ? null : creditStatus,
        "description": description == null ? null : description,
        "setting": setting == null ? null : setting.toJson(),
      };
}

class Setting {
  Setting({
    this.hashIv,
    this.hashKey,
    this.merchantId,
  });

  String hashIv;
  String hashKey;
  String merchantId;

  Setting copyWith({
    String hashIv,
    String hashKey,
    String merchantId,
  }) =>
      Setting(
        hashIv: hashIv ?? this.hashIv,
        hashKey: hashKey ?? this.hashKey,
        merchantId: merchantId ?? this.merchantId,
      );

  factory Setting.fromRawJson(String str) => Setting.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Setting.fromJson(Map<String, dynamic> json) => Setting(
        hashIv: json["hash_iv"] == null ? null : json["hash_iv"],
        hashKey: json["hash_key"] == null ? null : json["hash_key"],
        merchantId: json["merchant_id"] == null ? null : json["merchant_id"],
      );

  Map<String, dynamic> toJson() => {
        "hash_iv": hashIv == null ? null : hashIv,
        "hash_key": hashKey == null ? null : hashKey,
        "merchant_id": merchantId == null ? null : merchantId,
      };
}
