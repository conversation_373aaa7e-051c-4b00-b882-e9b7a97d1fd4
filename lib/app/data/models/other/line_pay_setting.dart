// To parse this JSON data, do
//
//     final linePaySetting = linePaySettingFromJson(jsonString);

import 'dart:convert';

class LinePaySetting {
  LinePaySetting({
    this.online,
    this.offline,
    this.channelId,
    this.channelSecretKey,
    this.offlineChannelId,
    this.offlineChannelSecretKey,
  });

  int online;
  int offline;
  String channelId;
  String channelSecretKey;
  String offlineChannelId;
  String offlineChannelSecretKey;

  LinePaySetting copyWith({
    int online,
    int offline,
    String channelId,
    String channelSecretKey,
    String offlineChannelId,
    String offlineChannelSecretKey,
  }) =>
      LinePaySetting(
        online: online ?? this.online,
        offline: offline ?? this.offline,
        channelId: channelId ?? this.channelId,
        channelSecretKey: channelSecretKey ?? this.channelSecretKey,
        offlineChannelId: offlineChannelId ?? this.offlineChannelId,
        offlineChannelSecretKey:
            offlineChannelSecretKey ?? this.offlineChannelSecretKey,
      );

  factory LinePaySetting.fromRawJson(String str) =>
      LinePaySetting.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LinePaySetting.fromJson(Map<String, dynamic> json) => LinePaySetting(
        online: json["online"] == null ? null : json["online"],
        offline: json["offline"] == null ? null : json["offline"],
        channelId: json["channel_id"] == null ? null : json["channel_id"],
        channelSecretKey: json["channel_secret_key"] == null
            ? null
            : json["channel_secret_key"],
        offlineChannelId: json["offline_channel_id"] == null
            ? null
            : json["offline_channel_id"],
        offlineChannelSecretKey: json["offline_channel_secret_key"] == null
            ? null
            : json["offline_channel_secret_key"],
      );

  Map<String, dynamic> toJson() => {
        "online": online == null ? null : online,
        "offline": offline == null ? null : offline,
        "channel_id": channelId == null ? null : channelId,
        "channel_secret_key":
            channelSecretKey == null ? null : channelSecretKey,
        "offline_channel_id":
            offlineChannelId == null ? null : offlineChannelId,
        "offline_channel_secret_key":
            offlineChannelSecretKey == null ? null : offlineChannelSecretKey,
      };
}
