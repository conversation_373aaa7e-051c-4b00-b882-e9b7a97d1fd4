// To parse this JSON data, do
//
//     final shippingInstore = shippingInstoreFromJson(jsonString);

import 'dart:convert';

class ShippingInstore {
  ShippingInstore({
    this.description,
    this.status,
    this.preOrderAfterDays,
    this.preOrderWithinDays,
  });

  String description;
  num status;
  num preOrderAfterDays;
  num preOrderWithinDays;

  ShippingInstore copyWith({
    String description,
    num status,
    num preOrderAfterDays,
    num preOrderWithinDays,
  }) =>
      ShippingInstore(
        description: description ?? this.description,
        status: status ?? this.status,
        preOrderAfterDays: preOrderAfterDays ?? this.preOrderAfterDays,
        preOrderWithinDays: preOrderWithinDays ?? this.preOrderWithinDays,
      );

  factory ShippingInstore.fromRawJson(String str) =>
      ShippingInstore.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ShippingInstore.fromJson(Map<String, dynamic> json) =>
      ShippingInstore(
        description: json["description"] == null ? null : json["description"],
        status: json["status"] == null ? null : json["status"],
        preOrderAfterDays: json["pre_order_after_days"] == null
            ? null
            : json["pre_order_after_days"],
        preOrderWithinDays: json["pre_order_within_days"] == null
            ? null
            : json["pre_order_within_days"],
      );

  Map<String, dynamic> toJson() => {
        "description": description == null ? null : description,
        "status": status == null ? null : status,
        "pre_order_after_days":
            preOrderAfterDays == null ? null : preOrderAfterDays,
        "pre_order_within_days":
            preOrderWithinDays == null ? null : preOrderWithinDays,
      };
}
