// To parse this JSON data, do
//
//     final brandsNews = brandsNewsFromJson(jsonString);

import 'dart:convert';

class BrandsNews {
  BrandsNews({
    this.news,
  });

  String news;

  BrandsNews copyWith({
    String news,
  }) =>
      BrandsNews(
        news: news ?? this.news,
      );

  factory BrandsNews.fromRawJson(String str) =>
      BrandsNews.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory BrandsNews.fromJson(Map<String, dynamic> json) => BrandsNews(
        news: json["news"] == null ? null : json["news"],
      );

  Map<String, dynamic> toJson() => {
        "news": news == null ? null : news,
      };
}
