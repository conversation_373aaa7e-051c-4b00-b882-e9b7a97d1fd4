// To parse this JSON data, do
//
//     final shippingDeliveryPut = shippingDeliveryPutFromJson(jsonString);

import 'dart:convert';

class ShippingDeliveryPut {
  ShippingDeliveryPut({
    this.ambStatus,
    this.ambShippingFee,
    this.ambShippingFree,
    this.coldStatus,
    this.coldShippingFee,
    this.coldShippingFree,
    this.ambDescription,
    this.coldDescription,
    this.ambPreOrderAfterDays,
    this.ambPreOrderWithinDays,
    this.coldPreOrderAfterDays,
    this.coldPreOrderWithinDays,
  });

  num ambStatus;
  num ambShippingFee;
  num ambShippingFree;
  num coldStatus;
  num coldShippingFee;
  num coldShippingFree;
  String ambDescription;
  String coldDescription;
  num ambPreOrderAfterDays;
  num ambPreOrderWithinDays;
  num coldPreOrderAfterDays;
  num coldPreOrderWithinDays;

  ShippingDeliveryPut copyWith({
    num ambStatus,
    num ambShippingFee,
    num ambShippingFree,
    num coldStatus,
    num coldShippingFee,
    num coldShippingFree,
    String ambDescription,
    String coldDescription,
    num ambPreOrderAfterDays,
    num ambPreOrderWithinDays,
    num coldPreOrderAfterDays,
    num coldPreOrderWithinDays,
  }) =>
      ShippingDeliveryPut(
        ambStatus: ambStatus ?? this.ambStatus,
        ambShippingFee: ambShippingFee ?? this.ambShippingFee,
        ambShippingFree: ambShippingFree ?? this.ambShippingFree,
        coldStatus: coldStatus ?? this.coldStatus,
        coldShippingFee: coldShippingFee ?? this.coldShippingFee,
        coldShippingFree: coldShippingFree ?? this.coldShippingFree,
        ambDescription: ambDescription ?? this.ambDescription,
        coldDescription: coldDescription ?? this.coldDescription,
        ambPreOrderAfterDays: ambPreOrderAfterDays ?? this.ambPreOrderAfterDays,
        ambPreOrderWithinDays:
            ambPreOrderWithinDays ?? this.ambPreOrderWithinDays,
        coldPreOrderAfterDays:
            coldPreOrderAfterDays ?? this.coldPreOrderAfterDays,
        coldPreOrderWithinDays:
            coldPreOrderWithinDays ?? this.coldPreOrderWithinDays,
      );

  factory ShippingDeliveryPut.fromRawJson(String str) =>
      ShippingDeliveryPut.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ShippingDeliveryPut.fromJson(Map<String, dynamic> json) =>
      ShippingDeliveryPut(
        ambStatus: json["amb_status"] == null ? null : json["amb_status"],
        ambShippingFee:
            json["amb_shipping_fee"] == null ? null : json["amb_shipping_fee"],
        ambShippingFree: json["amb_shipping_free"] == null
            ? null
            : json["amb_shipping_free"],
        coldStatus: json["cold_status"] == null ? null : json["cold_status"],
        coldShippingFee: json["cold_shipping_fee"] == null
            ? null
            : json["cold_shipping_fee"],
        coldShippingFree: json["cold_shipping_free"] == null
            ? null
            : json["cold_shipping_free"],
        ambDescription:
            json["amb_description"] == null ? null : json["amb_description"],
        coldDescription:
            json["cold_description"] == null ? null : json["cold_description"],
        ambPreOrderAfterDays: json["amb_pre_order_after_days"] == null
            ? null
            : json["amb_pre_order_after_days"],
        ambPreOrderWithinDays: json["amb_pre_order_within_days"] == null
            ? null
            : json["amb_pre_order_within_days"],
        coldPreOrderAfterDays: json["cold_pre_order_after_days"] == null
            ? null
            : json["cold_pre_order_after_days"],
        coldPreOrderWithinDays: json["cold_pre_order_within_days"] == null
            ? null
            : json["cold_pre_order_within_days"],
      );

  Map<String, dynamic> toJson() => {
        "amb_status": ambStatus == null ? null : ambStatus,
        "amb_shipping_fee": ambShippingFee == null ? null : ambShippingFee,
        "amb_shipping_free": ambShippingFree == null ? null : ambShippingFree,
        "cold_status": coldStatus == null ? null : coldStatus,
        "cold_shipping_fee": coldShippingFee == null ? null : coldShippingFee,
        "cold_shipping_free":
            coldShippingFree == null ? null : coldShippingFree,
        "amb_description": ambDescription == null ? null : ambDescription,
        "cold_description": coldDescription == null ? null : coldDescription,
        "amb_pre_order_after_days":
            ambPreOrderAfterDays == null ? null : ambPreOrderAfterDays,
        "amb_pre_order_within_days":
            ambPreOrderWithinDays == null ? null : ambPreOrderWithinDays,
        "cold_pre_order_after_days":
            coldPreOrderAfterDays == null ? null : coldPreOrderAfterDays,
        "cold_pre_order_within_days":
            coldPreOrderWithinDays == null ? null : coldPreOrderWithinDays,
      };
}
