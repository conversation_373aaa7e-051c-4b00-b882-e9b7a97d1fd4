// To parse this JSON data, do
//
//     final paymentBank = paymentBankFromJson(jsonString);

import 'dart:convert';

class PaymentBank {
  PaymentBank({
    this.description,
    this.setting,
    this.status,
  });

  String description;
  Setting setting;
  num status;

  PaymentBank copyWith({
    String description,
    Setting setting,
    num status,
  }) =>
      PaymentBank(
        description: description ?? this.description,
        setting: setting ?? this.setting,
        status: status ?? this.status,
      );

  factory PaymentBank.fromRawJson(String str) =>
      PaymentBank.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PaymentBank.fromJson(Map<String, dynamic> json) => PaymentBank(
        description: json["description"] == null ? null : json["description"],
        setting:
            json["setting"] == null ? null : Setting.fromJson(json["setting"]),
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "description": description == null ? null : description,
        "setting": setting == null ? null : setting.toJson(),
        "status": status == null ? null : status,
      };
}

class Setting {
  Setting({
    this.account,
    this.accountName,
    this.branch,
    this.name,
  });

  String account;
  String accountName;
  String branch;
  String name;

  Setting copyWith({
    String account,
    String accountName,
    String branch,
    String name,
  }) =>
      Setting(
        account: account ?? this.account,
        accountName: accountName ?? this.accountName,
        branch: branch ?? this.branch,
        name: name ?? this.name,
      );

  factory Setting.fromRawJson(String str) => Setting.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Setting.fromJson(Map<String, dynamic> json) => Setting(
        account: json["account"] == null ? null : json["account"],
        accountName: json["account_name"] == null ? null : json["account_name"],
        branch: json["branch"] == null ? null : json["branch"],
        name: json["name"] == null ? null : json["name"],
      );

  Map<String, dynamic> toJson() => {
        "account": account == null ? null : account,
        "account_name": accountName == null ? null : accountName,
        "branch": branch == null ? null : branch,
        "name": name == null ? null : name,
      };
}
