// To parse this JSON data, do
//
//     final imageModel = imageModelFromJson(jsonString);

import 'dart:convert';

class ImageModel {
  ImageModel({
    this.createdAt,
    this.filename,
    this.height,
    this.id,
    this.size,
    this.updatedAt,
    this.url,
    this.width,
  });

  String createdAt;
  String filename;
  num height;
  num id;
  num size;
  String updatedAt;
  String url;
  num width;

  ImageModel copyWith({
    String createdAt,
    String filename,
    num height,
    num id,
    num size,
    String updatedAt,
    String url,
    num width,
  }) =>
      ImageModel(
        createdAt: createdAt ?? this.createdAt,
        filename: filename ?? this.filename,
        height: height ?? this.height,
        id: id ?? this.id,
        size: size ?? this.size,
        updatedAt: updatedAt ?? this.updatedAt,
        url: url ?? this.url,
        width: width ?? this.width,
      );

  factory ImageModel.fromRawJson(String str) =>
      ImageModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ImageModel.fromJson(Map<String, dynamic> json) => ImageModel(
        createdAt: json["created_at"] == null ? null : json["created_at"],
        filename: json["filename"] == null ? null : json["filename"],
        height: json["height"] == null ? null : json["height"],
        id: json["id"] == null ? null : json["id"],
        size: json["size"] == null ? null : json["size"],
        updatedAt: json["updated_at"] == null ? null : json["updated_at"],
        url: json["url"] == null ? null : json["url"],
        width: json["width"] == null ? null : json["width"],
      );

  Map<String, dynamic> toJson() => {
        "created_at": createdAt == null ? null : createdAt,
        "filename": filename == null ? null : filename,
        "height": height == null ? null : height,
        "id": id == null ? null : id,
        "size": size == null ? null : size,
        "updated_at": updatedAt == null ? null : updatedAt,
        "url": url == null ? null : url,
        "width": width == null ? null : width,
      };
}
