// To parse this JSON data, do
//
//     final settingPoint = settingPointFromJson(jsonString);

import 'dart:convert';

class SettingPoint {
  SettingPoint({
    this.cashRatio,
    this.isRewardPoints,
    this.type,
  });

  num cashRatio;
  num isRewardPoints;
  num type;

  SettingPoint copyWith({
    num cashRatio,
    num isRewardPoints,
    num type,
  }) =>
      SettingPoint(
        cashRatio: cashRatio ?? this.cashRatio,
        isRewardPoints: isRewardPoints ?? this.isRewardPoints,
        type: type ?? this.type,
      );

  factory SettingPoint.fromRawJson(String str) =>
      SettingPoint.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SettingPoint.fromJson(Map<String, dynamic> json) => SettingPoint(
        cashRatio: json["cash_ratio"] == null ? null : json["cash_ratio"],
        isRewardPoints:
            json["is_reward_points"] == null ? null : json["is_reward_points"],
        type: json["type"] == null ? null : json["type"],
      );

  Map<String, dynamic> toJson() => {
        "cash_ratio": cashRatio == null ? null : cashRatio,
        "is_reward_points": isRewardPoints == null ? null : isRewardPoints,
        "type": type == null ? null : type,
      };
}
