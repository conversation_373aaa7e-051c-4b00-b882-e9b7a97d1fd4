// To parse this JSON data, do
//
//     final appSettings = appSettingsFromJson(jsonString);

import 'dart:convert';

AppSettings appSettingsFromJson(String str) =>
    AppSettings.fromJson(json.decode(str));

String appSettingsToJson(AppSettings data) => json.encode(data.toJson());

class AppSettings {
  AppSettings({
    this.buildNumber,
    this.development,
    this.production,
  });

  int buildNumber;
  String development;
  String production;

  factory AppSettings.fromJson(Map<String, dynamic> json) => AppSettings(
        buildNumber: json["buildNumber"] == null ? null : json["buildNumber"],
        development: json["development"] == null ? null : json["development"],
        production: json["production"] == null ? null : json["production"],
      );

  Map<String, dynamic> toJson() => {
        "buildNumber": buildNumber == null ? null : buildNumber,
        "development": development == null ? null : development,
        "production": production == null ? null : production,
      };
}
