import 'dart:convert';

import 'package:get/get.dart';
import 'package:muyipork/app/data/models/res/orders_get_res.dart';
import 'package:muyipork/app/data/models/res/setting_get_res.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

// 這是個幫忙整理多筆 OrdersGetRes 用的結構，這是為了讓使用分批讀取更明白
// 同時提供過濾功能來幫助 order_controller
class OrdersCollection {
  OrdersCollection({
    this.orderType,
    this.orderSource,
    this.orderStatus,
    this.storeType,
    this.sortType,
    this.sort,
    this.name,
    this.page,
    this.limit,
    this.hasMore,
  });

  List<num> orderType;
  List<num> orderSource;
  List<num> orderStatus;
  num storeType;
  String sortType;
  String sort;
  String name;
  num page;
  num limit;
  bool hasMore;

  OrdersCollection copyWith({
    List<num> orderType,
    List<num> orderSource,
    List<num> orderStatus,
    num storeType,
    String sortType,
    String sort,
    String name,
    num page,
    num limit,
    bool hasMore,
  }) =>
      OrdersCollection(
        orderType: orderType ?? this.orderType,
        orderSource: orderSource ?? this.orderSource,
        orderStatus: orderStatus ?? this.orderStatus,
        storeType: storeType ?? this.storeType,
        sortType: sortType ?? this.sortType,
        sort: sort ?? this.sort,
        name: name ?? this.name,
        page: page ?? this.page,
        limit: limit ?? this.limit,
        hasMore: hasMore ?? this.hasMore,
      );

  factory OrdersCollection.fromRawJson(String str) =>
      OrdersCollection.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrdersCollection.fromJson(Map<String, dynamic> json) =>
      OrdersCollection(
        orderType: json["order_type"] == null
            ? null
            : List<num>.from(json["order_type"].map((x) => x)),
        orderSource: json["order_source"] == null
            ? null
            : List<num>.from(json["order_source"].map((x) => x)),
        orderStatus: json["order_status"] == null
            ? null
            : List<num>.from(json["order_status"].map((x) => x)),
        storeType: json["store_type"],
        sortType: json["sort_type"],
        sort: json["sort"],
        name: json["name"],
        page: json["page"],
        limit: json["limit"],
        hasMore: json["has_more"],
      );

  Map<String, dynamic> toJson() => {
        "order_type": orderType == null
            ? null
            : List<dynamic>.from(orderType.map((x) => x)),
        "order_source": orderSource == null
            ? null
            : List<dynamic>.from(orderSource.map((x) => x)),
        "order_status": orderStatus == null
            ? null
            : List<dynamic>.from(orderStatus.map((x) => x)),
        "store_type": storeType,
        "sort_type": sortType,
        "sort": sort,
        "name": name,
        "page": page,
        "limit": limit,
        "has_more": hasMore,
      };

  final _orderProvider = Rx<OrderProvider>(null);
  final cached = <OrderSummary>[].obs;
  final _filter = Rx<OrdersCollectionFilter>(null);
}

extension OrdersCollectionX on OrdersCollection {
  set orderProvider(OrderProvider value) => _orderProvider.value = value;
  OrderProvider get orderProvider => _orderProvider.value;
  BoxProvider get boxProvider => orderProvider.boxProvider;

  void setFilter(
    OrdersCollectionFilter value, [
    Iterable<OrderSummary> source,
  ]) {
    _filter.value = value;
    _refreshWithFilterFromMemory(source);
  }

  void _refreshWithFilterFromMemory([Iterable<OrderSummary> orders]) {
    if (orders == null) {
      final box = boxProvider.getGsBox(kBoxOrder);
      orders = List.from(box.getValues(), growable: false)
          .map((e) => OrderSummary.fromJson(e));
    }
    final list = orders.where(_validate).toList(growable: false);
    final sort = (_filter.value?.hasOverrideSort ?? false)
        ? _filter.value.sort
        : this.sort;
    final asc = 'ASC' == sort;
    list.sort(asc ? _asc : _desc);
    cached.assignAll(list);
  }

  static int _asc(OrderSummary x, OrderSummary y) {
    final a = x.mealAt?.localAt?.millisecondsSinceEpoch ?? 0;
    final b = y.mealAt?.localAt?.millisecondsSinceEpoch ?? 0;
    return a.compareTo(b);
  }

  static int _desc(OrderSummary x, OrderSummary y) {
    // final a = num.tryParse(x.orderNumber.takeLast(14)) ?? 0;
    // final b = num.tryParse(y.orderNumber.takeLast(14)) ?? 0;
    final a = x.createdAt?.localAt?.millisecondsSinceEpoch ?? 0;
    final b = y.createdAt?.localAt?.millisecondsSinceEpoch ?? 0;
    return b.compareTo(a);
  }

  bool _validate(OrderSummary order) {
    // 不顯示子訂單
    if (order.isSlave) {
      return false;
    }
    if (orderStatusList != null && orderStatusList.isNotEmpty) {
      if (!orderStatusList.contains(order.status.orderStatus)) {
        return false;
      }
    }
    if (orderTypeList != null) {
      if (!orderTypeList.contains(order.type.orderType)) {
        return false;
      }
    }
    if (orderSourceList != null && orderSourceList.isNotEmpty) {
      if (!orderSourceList.contains(order.source.orderSource)) {
        return false;
      }
    }
    // 本時段單
    if (_filter.value?.inHours != null) {
      final x = order.mealAtLocalDateTime().millisecondsSinceEpoch;
      final y = _filter.value.inHours.end.millisecondsSinceEpoch;
      return x <= y;
    }
    // 下時段單
    if (_filter.value?.notInHours != null) {
      final x = order.mealAtLocalDateTime().millisecondsSinceEpoch;
      final y = _filter.value.notInHours.end.millisecondsSinceEpoch;
      return x > y;
    }
    return true;
  }

  num get offset => (page - 1) * limit;
  num get length => offset + limit;
  num get nnPage => page ?? 1;
  num get nnLimit => limit ?? kLimit;
  bool get nnHasMore => hasMore ?? false;
  num get nextPage => nnPage + 1;
  num get source {
    final ls = orderSourceList ?? <OrderSource>[];
    return ls.length == 1 ? ls.first.index : null;
  }

  OrderReq toOrderReq({Iterable<OrderStatus> status, OrdersViewMode mode}) {
    return OrderReq(
      page: nnPage,
      limit: nnLimit,
      source: source,
      status: List<num>.from(status.map((e) => e.index), growable: false),
      type: orderType,
      sortType: mode.isCompletedOrders ? '' : (sortType ?? ''),
      sort: sort ?? '',
      withSubOrder: 1,
    );
  }

  String get displayName {
    name ??= '';
    return name;
  }

  // 餐飲 或 零售
  StoreType get eStoreType {
    storeType ??= StoreType.Max.index;
    return storeType.storeType;
  }

  // 餐飲內用, 自取, 外帶, 外送...
  Iterable<OrderType> get orderTypeList {
    orderType ??= <num>[];
    return orderType.map((e) => e.orderType);
  }

  // online 或 offline
  Iterable<OrderSource> get orderSourceList {
    orderSource ??= <num>[];
    return orderSource.map((e) => e.orderSource);
  }

  // 處理中, 已確認, 訂單完成, 訂單取消...
  Iterable<OrderStatus> get orderStatusList {
    orderStatus ??= <num>[];
    return orderStatus.map((e) => e.orderStatus);
  }
}

class OrdersCollectionFilter {
  OrdersCollectionFilter({
    this.inHours,
    this.notInHours,
    this.sort,
  });

  final OpenHoursDateTime inHours;
  final OpenHoursDateTime notInHours;
  final String sort;

  bool get hasOverrideSort => sort != null && sort.isNotEmpty;
}
