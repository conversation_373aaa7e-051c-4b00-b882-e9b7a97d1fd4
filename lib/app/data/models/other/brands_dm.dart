// To parse this JSON data, do
//
//     final brandsDm = brandsDmFromJson(jsonString);

import 'dart:convert';

class BrandsDm {
  BrandsDm({
    this.cart,
    this.createOrder,
    this.orderList,
    this.type,
  });

  num cart;
  num createOrder;
  num orderList;
  num type;

  BrandsDm copyWith({
    num cart,
    num createOrder,
    num orderList,
    num type,
  }) =>
      BrandsDm(
        cart: cart ?? this.cart,
        createOrder: createOrder ?? this.createOrder,
        orderList: orderList ?? this.orderList,
        type: type ?? this.type,
      );

  factory BrandsDm.fromRawJson(String str) =>
      BrandsDm.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory BrandsDm.fromJson(Map<String, dynamic> json) => BrandsDm(
        cart: json["cart"] == null ? null : json["cart"],
        createOrder: json["create_order"] == null ? null : json["create_order"],
        orderList: json["order_list"] == null ? null : json["order_list"],
        type: json["type"] == null ? null : json["type"],
      );

  Map<String, dynamic> toJson() => {
        "cart": cart == null ? null : cart,
        "create_order": createOrder == null ? null : createOrder,
        "order_list": orderList == null ? null : orderList,
        "type": type == null ? null : type,
      };
}
