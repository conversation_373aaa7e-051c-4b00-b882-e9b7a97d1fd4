// To parse this JSON data, do
//
//     final payment = paymentFromJson(jsonString);

import 'dart:convert';

class Payment {
  Payment({
    this.bank,
    this.cod,
    this.instore,
  });

  Bank bank;
  Cod cod;
  Instore instore;

  Payment copyWith({
    Bank bank,
    Cod cod,
    Instore instore,
  }) =>
      Payment(
        bank: bank ?? this.bank,
        cod: cod ?? this.cod,
        instore: instore ?? this.instore,
      );

  factory Payment.fromRawJson(String str) => Payment.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Payment.fromJson(Map<String, dynamic> json) => Payment(
        bank: json["bank"] == null ? null : Bank.fromJson(json["bank"]),
        cod: json["cod"] == null ? null : Cod.fromJson(json["cod"]),
        instore:
            json["instore"] == null ? null : Instore.fromJson(json["instore"]),
      );

  Map<String, dynamic> toJson() => {
        "bank": bank == null ? null : bank.toJson(),
        "cod": cod == null ? null : cod.toJson(),
        "instore": instore == null ? null : instore.toJson(),
      };
}

class Bank {
  Bank({
    this.account,
    this.accountName,
    this.branch,
    this.description,
    this.name,
    this.status,
  });

  String account;
  String accountName;
  String branch;
  String description;
  String name;
  num status;

  Bank copyWith({
    String account,
    String accountName,
    String branch,
    String description,
    String name,
    num status,
  }) =>
      Bank(
        account: account ?? this.account,
        accountName: accountName ?? this.accountName,
        branch: branch ?? this.branch,
        description: description ?? this.description,
        name: name ?? this.name,
        status: status ?? this.status,
      );

  factory Bank.fromRawJson(String str) => Bank.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Bank.fromJson(Map<String, dynamic> json) => Bank(
        account: json["account"] == null ? null : json["account"],
        accountName: json["account_name"] == null ? null : json["account_name"],
        branch: json["branch"] == null ? null : json["branch"],
        description: json["description"] == null ? null : json["description"],
        name: json["name"] == null ? null : json["name"],
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "account": account == null ? null : account,
        "account_name": accountName == null ? null : accountName,
        "branch": branch == null ? null : branch,
        "description": description == null ? null : description,
        "name": name == null ? null : name,
        "status": status == null ? null : status,
      };
}

class Cod {
  Cod({
    this.description,
    this.paymentFee,
    this.paymentFree,
    this.status,
  });

  String description;
  num paymentFee;
  num paymentFree;
  num status;

  Cod copyWith({
    String description,
    num paymentFee,
    num paymentFree,
    num status,
  }) =>
      Cod(
        description: description ?? this.description,
        paymentFee: paymentFee ?? this.paymentFee,
        paymentFree: paymentFree ?? this.paymentFree,
        status: status ?? this.status,
      );

  factory Cod.fromRawJson(String str) => Cod.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Cod.fromJson(Map<String, dynamic> json) => Cod(
        description: json["description"] == null ? null : json["description"],
        paymentFee: json["payment_fee"] == null ? null : json["payment_fee"],
        paymentFree: json["payment_free"] == null ? null : json["payment_free"],
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "description": description == null ? null : description,
        "payment_fee": paymentFee == null ? null : paymentFee,
        "payment_free": paymentFree == null ? null : paymentFree,
        "status": status == null ? null : status,
      };
}

class Instore {
  Instore({
    this.description,
    this.status,
  });

  String description;
  num status;

  Instore copyWith({
    String description,
    num status,
  }) =>
      Instore(
        description: description ?? this.description,
        status: status ?? this.status,
      );

  factory Instore.fromRawJson(String str) => Instore.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Instore.fromJson(Map<String, dynamic> json) => Instore(
        description: json["description"] == null ? null : json["description"],
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "description": description == null ? null : description,
        "status": status == null ? null : status,
      };
}
