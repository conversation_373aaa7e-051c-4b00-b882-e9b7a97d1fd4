// To parse this JSON data, do
//
//     final couponsReq = couponsReqFromJson(jsonString);

import 'dart:convert';

class CouponsReq {
  CouponsReq({
    this.page,
    this.limit,
    this.kind,
    this.isOnline,
  });

  num page;
  num limit;
  num kind;
  num isOnline;

  CouponsReq copyWith({
    num page,
    num limit,
    num kind,
    num isOnline,
  }) =>
      CouponsReq(
        page: page ?? this.page,
        limit: limit ?? this.limit,
        kind: kind ?? this.kind,
        isOnline: isOnline ?? this.isOnline,
      );

  factory CouponsReq.fromRawJson(String str) =>
      CouponsReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CouponsReq.fromJson(Map<String, dynamic> json) => CouponsReq(
        page: json["page"] == null ? null : json["page"],
        limit: json["limit"] == null ? null : json["limit"],
        kind: json["kind"] == null ? null : json["kind"],
        isOnline: json["is_online"] == null ? null : json["is_online"],
      );

  Map<String, dynamic> toJson() => {
        "page": page == null ? null : page,
        "limit": limit == null ? null : limit,
        "kind": kind == null ? null : kind,
        "is_online": isOnline == null ? null : isOnline,
      };
}
