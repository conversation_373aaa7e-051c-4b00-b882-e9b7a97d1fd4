// To parse this JSON data, do
//
//     final brandsBanners = brandsBannersFromJson(jsonString);

import 'dart:convert';

// TODO: use image object instead
class BrandsBanners {
  BrandsBanners({
    this.imageId,
    this.imageUrl,
    this.sort,
  });

  num imageId;
  String imageUrl;
  num sort;

  BrandsBanners copyWith({
    num imageId,
    String imageUrl,
    num sort,
  }) =>
      BrandsBanners(
        imageId: imageId ?? this.imageId,
        imageUrl: imageUrl ?? this.imageUrl,
        sort: sort ?? this.sort,
      );

  factory BrandsBanners.fromRawJson(String str) =>
      BrandsBanners.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory BrandsBanners.fromJson(Map<String, dynamic> json) => BrandsBanners(
        imageId: json["image_id"] == null ? null : json["image_id"],
        imageUrl: json["image_url"] == null ? null : json["image_url"],
        sort: json["sort"] == null ? null : json["sort"],
      );

  Map<String, dynamic> toJson() => {
        "image_id": imageId == null ? null : imageId,
        "image_url": imageUrl == null ? null : imageUrl,
        "sort": sort == null ? null : sort,
      };
}
