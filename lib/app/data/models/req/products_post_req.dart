import 'dart:convert';

import 'package:muyipork/enums.dart';
import 'package:okshop_model/okshop_model.dart';

class ProductsPostReq {
  // 類型
  // 0: 餐飲店內
  // 1: 餐飲線上
  // 2: 零售
  int kind;

  // 產品名稱
  String title;

  // 產品簡介
  String summary;

  // 售價
  int price;

  // 庫存 (餐飲此欄位用於 售完=0/未售完=1)
  int stock;

  // 隱形商品
  // 0: 可見
  // 1: 隱藏
  num isHidden;

  // 商品稅金
  // 1: 應稅
  // 2: 免稅
  num taxType;

  // 售完/未售完設定(預設: 0, 零售用)
  // 0: 售完
  // 1: 未售完
  int isAvailable;

  // 庫存不限制(預設: 1)
  // 0: 關閉
  // 1: 開啟
  int stockUnlimited;

  // 物流類別(預設: 0)
  // 0: 常溫
  // 1: 低溫
  int shippingType;

  // 分類ID
  List<int> categories;

  // 附加品設定
  List<AdditionCategorySetting> additionCategories;

  // 商品圖片
  List<ProductsImage> productImages;

  // 是否為 vip 會員專屬產品
  num isVip;
  // 會員價格
  num vipPrice;

  ProductsPostReq({
    this.kind = 0,
    this.title = '',
    this.summary = '',
    this.price = 0,
    this.stock = 1,
    this.isAvailable = 1,
    this.stockUnlimited = 1,
    this.shippingType = 0,
    this.isHidden = 0,
    this.taxType = 1,
    this.categories,
    this.additionCategories,
    this.productImages,
    this.isVip = 0,
    this.vipPrice = 0,
  });

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['kind'] = kind;
    data['title'] = title;
    data['summary'] = summary;
    data['price'] = price;
    data['stock'] = stock;
    data['is_hidden'] = isHidden ?? Switcher.Off.index;
    data['tax_type'] = taxType ?? Switcher.On.index;
    data['is_available'] = isAvailable ?? Switcher.On.index;
    data['stock_unlimited'] = stockUnlimited;
    data['shipping_type'] = shippingType;
    data['categories'] = jsonEncode(categories);
    data['addition_categories'] =
        jsonEncode(additionCategories.map((e) => e.toJson()).toList());
    productImages ??= <ProductsImage>[];
    data['product_images'] =
        jsonEncode(productImages.map((e) => e.toJson()).toList());
    data['is_vip'] = isVip ?? Switcher.Off.index;
    data['vip_price'] = vipPrice?.round() ?? 0;
    return data;
  }
}

class AdditionCategorySetting {
  AdditionCategorySetting({
    this.additionCategoryId,
    this.title = '',
    this.required = 0,
    this.option = 0,
    this.optionMin = 0,
    this.optionMax = 1,
  });

  // 附加品分類編號
  num additionCategoryId;
  // 前台顯示名稱
  String title;
  // 必選
  // 0: 關閉
  // 1: 開啟
  num required;
  // 單選 or 複選
  // 0: 單選
  // 1: 複選
  num option;
  // 複選: 最少數量
  num optionMin;
  // 複選: 最多數量
  num optionMax;

  AdditionCategorySetting copyWith({
    num additionCategoryId,
    String title,
    num required,
    num option,
    num optionMin,
    num optionMax,
  }) =>
      AdditionCategorySetting(
        additionCategoryId: additionCategoryId ?? this.additionCategoryId,
        title: title ?? this.title,
        required: required ?? this.required,
        option: option ?? this.option,
        optionMin: optionMin ?? this.optionMin,
        optionMax: optionMax ?? this.optionMax,
      );

  factory AdditionCategorySetting.fromRawJson(String str) =>
      AdditionCategorySetting.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AdditionCategorySetting.fromJson(Map<String, dynamic> json) =>
      AdditionCategorySetting(
        additionCategoryId: json["addition_category_id"] == null
            ? null
            : json["addition_category_id"],
        title: json["title"] == null ? null : json["title"],
        required: json["required"] == null ? null : json["required"],
        option: json["option"] == null ? null : json["option"],
        optionMin: json["option_min"] == null ? null : json["option_min"],
        optionMax: json["option_max"] == null ? null : json["option_max"],
      );

  Map<String, dynamic> toJson() => {
        "addition_category_id":
            additionCategoryId == null ? null : additionCategoryId,
        "title": title == null ? null : title,
        "required": required == null ? null : required,
        "option": option == null ? null : option,
        "option_min": optionMin == null ? null : optionMin,
        "option_max": optionMax == null ? null : optionMax,
      };
}
