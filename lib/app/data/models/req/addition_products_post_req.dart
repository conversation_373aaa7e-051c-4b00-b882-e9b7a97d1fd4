class AdditionProductsPostReq {
  int kind;
  int additionCategoryId;
  String name;
  int price;
  num sort;

  AdditionProductsPostReq(
      {this.kind, this.additionCategoryId, this.name, this.price, this.sort});

  Map<String, dynamic> toBody() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['kind'] = this.kind;
    data['addition_category_id'] = this.additionCategoryId;
    data['name'] = this.name;
    data['price'] = this.price.toInt();
    data['sort'] = this.sort;
    return data;
  }
}
