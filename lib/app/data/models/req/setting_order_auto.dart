//接單設定
class SettingOrderAuto {
  //自動接單
  //0: 關閉
  //1: 開啟
  int order;

  //自動列印小白單
  // 0: 關閉
  // 1: 開啟
  int orderPrint;

  //自動棄單
  // 0: 關閉
  // 1: 開啟
  int abandon;

  //自動棄單時間（分鐘）
  int abandonMin;

  //關門前提前不接單
  // 0: 關閉
  // 1: 開啟
  int close;

  //提前幾分鐘關閉 LINE 接單（分鐘）
  int closeMin;

  SettingOrderAuto({
    this.order = 0,
    this.orderPrint = 0,
    this.abandon = 0,
    this.abandonMin = 0,
    this.close = 0,
    this.closeMin = 0,
  });

  SettingOrderAuto.fromJson(Map<String, dynamic> json) {
    order = json['order'] ?? 0;
    orderPrint = json['order_print'] ?? 0;
    abandon = json['abandon'] ?? 0;
    abandonMin = json['abandon_min'] ?? 0;
    close = json['close'] ?? 0;
    closeMin = json['close_min'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['order'] = this.order ?? 0;
    data['order_print'] = this.orderPrint ?? 0;
    data['abandon'] = this.abandon ?? 0;
    data['abandon_min'] = this.abandonMin ?? 0;
    data['close'] = this.close ?? 0;
    data['close_min'] = this.closeMin ?? 0;
    return data;
  }
}
