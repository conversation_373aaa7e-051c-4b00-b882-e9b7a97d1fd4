// To parse this JSON data, do
//
//     final memberCouponsIdReq = memberCouponsIdReqFromJson(jsonString);

import 'dart:convert';

class MemberCouponsIdReq {
  MemberCouponsIdReq({
    this.couponId,
  });

  List<int> couponId;

  MemberCouponsIdReq copyWith({
    List<int> couponId,
  }) =>
      MemberCouponsIdReq(
        couponId: couponId ?? this.couponId,
      );

  factory MemberCouponsIdReq.fromRawJson(String str) =>
      MemberCouponsIdReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MemberCouponsIdReq.fromJson(Map<String, dynamic> json) =>
      MemberCouponsIdReq(
        couponId: json["coupon_id"] == null
            ? null
            : List<int>.from(json["coupon_id"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "coupon_id": couponId == null
            ? null
            : List<dynamic>.from(couponId.map((x) => x)),
      };
}
