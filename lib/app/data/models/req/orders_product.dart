class OrdersProduct {
  int finalPrice;
  int id;
  int productId;
  String productName;
  String productSpec1;
  int quantity;

  OrdersProduct(
      {this.finalPrice,
        this.id,
        this.productId,
        this.productName,
        this.productSpec1,
        this.quantity});

  OrdersProduct.fromJson(Map<String, dynamic> json) {
    finalPrice = json['final_price'];
    id = json['id'];
    productId = json['product_id'];
    productName = json['product_name'];
    productSpec1 = json['product_spec_1'];
    quantity = json['quantity'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['final_price'] = this.finalPrice;
    data['id'] = this.id;
    data['product_id'] = this.productId;
    data['product_name'] = this.productName;
    data['product_spec_1'] = this.productSpec1;
    data['quantity'] = this.quantity;
    return data;
  }
}