//[Dep]: Just use SettingGetData
// import 'package:muyipork/app/data/models/req/setting_line_order.dart';
// import 'package:muyipork/app/data/models/req/setting_order_auto.dart';
// import 'package:muyipork/app/data/models/req/setting_order_fee.dart';
//
// class SettingPutReq {
//   //line 訂單設定
//   SettingLineOrder lineOrder;
//   //接單設定
//   SettingOrderAuto auto;
//   //服務費
//   SettingOrderFee fee;
//   int printOrder;
//   int printDetail;
//   int checkoutType;
//
//   SettingPutReq(
//       {this.lineOrder,
//         this.auto,
//         this.fee,
//         this.printOrder,
//         this.printDetail,
//         this.checkoutType});
//
//   SettingPutReq.fromJson(Map<String, dynamic> json) {
//     lineOrder = json['line_order'] != null
//         ? new SettingLineOrder.fromJson(json['line_order'])
//         : null;
//     auto = json['auto'] != null ? new SettingOrderAuto.fromJson(json['auto']) : null;
//     fee = json['fee'] != null ? new SettingOrderFee.fromJson(json['fee']) : null;
//     printOrder = json['print_order'];
//     printDetail = json['print_detail'];
//     checkoutType = json['checkout_type'];
//   }
//
//   Map<String, dynamic> toBody() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     if (this.lineOrder != null) {
//       data['line_order'] = this.lineOrder.toJson();
//     }
//     if (this.auto != null) {
//       data['auto'] = this.auto.toJson();
//     }
//     if (this.fee != null) {
//       data['fee'] = this.fee.toJson();
//     }
//     data['print_order'] = this.printOrder;
//     data['print_detail'] = this.printDetail;
//     data['checkout_type'] = this.checkoutType;
//     return data;
//   }
// }
//
// class SettingPutOther {
//   //line 訂單設定
//   SettingLineOrder lineOrder;
//   //接單設定
//   SettingOrderAuto auto;
//   //服務費
//   SettingOrderFee fee;
//   int printOrder;
//   int printDetail;
//   int checkoutType;
//
//   SettingPutOther(
//       {this.lineOrder,
//         this.auto,
//         this.fee,
//         this.printOrder,
//         this.printDetail,
//         this.checkoutType});
//
//   SettingPutOther.fromJson(Map<String, dynamic> json) {
//     lineOrder = json['line_order'] != null
//         ? new SettingLineOrder.fromJson(json['line_order'])
//         : null;
//     auto = json['auto'] != null ? new SettingOrderAuto.fromJson(json['auto']) : null;
//     fee = json['fee'] != null ? new SettingOrderFee.fromJson(json['fee']) : null;
//     printOrder = json['print_order'];
//     printDetail = json['print_detail'];
//     checkoutType = json['checkout_type'];
//   }
//
//   Map<String, dynamic> toBody() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     if (this.lineOrder != null) {
//       data['line_order'] = this.lineOrder.toJson();
//     }
//     if (this.auto != null) {
//       data['auto'] = this.auto.toJson();
//     }
//     if (this.fee != null) {
//       data['fee'] = this.fee.toJson();
//     }
//     data['print_order'] = this.printOrder;
//     data['print_detail'] = this.printDetail;
//     data['checkout_type'] = this.checkoutType;
//     return data;
//   }
// }
