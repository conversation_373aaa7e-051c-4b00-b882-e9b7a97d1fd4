// To parse this JSON data, do
//
//     final ordersOrderIdInvoicePostReq = ordersOrderIdInvoicePostReqFromJson(jsonString);

import 'dart:convert';

class OrdersOrderIdInvoicePostReq {
  OrdersOrderIdInvoicePostReq({
    this.invoiceNumber,
    this.randomNumber,
    this.invoicePaper,
    this.vatNumber,
    this.carrierType,
    this.carrierId,
    this.npoBan,
  });

  //發票號碼
  String invoiceNumber;
  //發票隨機碼
  String randomNumber;
  //是否印出紙本發票
  bool invoicePaper;
  //統編
  String vatNumber;
  //載具類別
  // 0 :悠遊卡
  // 1 :一卡通
  // 2 :icash
  // 3 :手機
  // 4 :自然人憑證
  // 5 :金融卡
  // 6 :公用事業
  // 7 :信用卡
  // 8 :會員
  num carrierType;
  //信用卡: 當次刷 卡日期+刷卡金額。 手機: 必須以 / 為起始作為判斷,
  // 目前總長度共為 8 碼， 條碼內容物除第 1 碼外只會有
  // 0123456789 ABCDEFGHIJKLMNOPQRSTUVWXYZ + - . 這39個字元。
  // 自然人憑證:2位 大寫字母+14 位數字。
  String carrierId;
  //愛心碼
  String npoBan;

  OrdersOrderIdInvoicePostReq copyWith({
    String invoiceNumber,
    String randomNumber,
    bool invoicePaper,
    String vatNumber,
    num carrierType,
    String carrierId,
    String npoBan,
  }) =>
      OrdersOrderIdInvoicePostReq(
        invoiceNumber: invoiceNumber ?? this.invoiceNumber,
        randomNumber: randomNumber ?? this.randomNumber,
        invoicePaper: invoicePaper ?? this.invoicePaper,
        vatNumber: vatNumber ?? this.vatNumber,
        carrierType: carrierType ?? this.carrierType,
        carrierId: carrierId ?? this.carrierId,
        npoBan: npoBan ?? this.npoBan,
      );

  factory OrdersOrderIdInvoicePostReq.fromRawJson(String str) =>
      OrdersOrderIdInvoicePostReq.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OrdersOrderIdInvoicePostReq.fromJson(Map<String, dynamic> json) =>
      OrdersOrderIdInvoicePostReq(
        invoiceNumber:
            json["invoice_number"] == null ? null : json["invoice_number"],
        randomNumber:
            json["random_number"] == null ? null : json["random_number"],
        invoicePaper:
            json["invoice_paper"] == null ? null : json["invoice_paper"],
        vatNumber: json["vat_number"] == null ? null : json["vat_number"],
        carrierType: json["carrier_type"] == null ? null : json["carrier_type"],
        carrierId: json["carrier_id"] == null ? null : json["carrier_id"],
        npoBan: json["npo_ban"] == null ? null : json["npo_ban"],
      );

  Map<String, dynamic> toJson() => {
        "invoice_number": invoiceNumber == null ? null : invoiceNumber,
        "random_number": randomNumber == null ? null : randomNumber,
        "invoice_paper": invoicePaper == null ? null : invoicePaper,
        "vat_number": vatNumber == null ? null : vatNumber,
        "carrier_type": carrierType == null ? null : carrierType,
        "carrier_id": carrierId == null ? null : carrierId,
        "npo_ban": npoBan == null ? null : npoBan,
      };

  Map<String, dynamic> toBody() => toJson();
}
