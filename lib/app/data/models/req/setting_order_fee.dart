// To parse this JSON data, do
//
//     final settingOrderFee = settingOrderFeeFromJson(jsonString);

import 'dart:convert';

class SettingOrderFee {
  SettingOrderFee({
    this.type = 0,
    this.percent = 0,
  });

  //類型
  // 0: 關閉
  // 1: 以原價計算
  // 2: 以折扣後價格算
  num type;

  //帳單總金額 %
  num percent;

  SettingOrderFee copyWith({
    num type,
    num percent,
  }) =>
      SettingOrderFee(
        type: type ?? this.type,
        percent: percent ?? this.percent,
      );

  factory SettingOrderFee.fromRawJson(String str) =>
      SettingOrderFee.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SettingOrderFee.fromJson(Map<String, dynamic> json) =>
      SettingOrderFee(
        type: json["type"] == null ? null : json["type"],
        percent: json["percent"] == null ? null : json["percent"],
      );

  Map<String, dynamic> toJson() => {
        "type": type == null ? null : type,
        "percent": percent == null ? null : percent,
      };
}
