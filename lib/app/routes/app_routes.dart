part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  static const HOME = _Paths.HOME;
  static const LOGIN = _Paths.LOGIN;
  static const SETTINGS = _Paths.SETTINGS;
  static const ORDERS = _Paths.ORDERS;
  static const ACCOUNTS = _Paths.ACCOUNTS;
  static const PASSWORD = _Paths.PASSWORD;
  static const ORDER_DETAIL = _Paths.ORDER_DETAIL;
  static const ACCOUNT_DETAIL = _Paths.ACCOUNT_DETAIL;
  static const BLANK = _Paths.BLANK;
  static const PARTITION_SETUP = _Paths.PARTITION_SETUP;
  static const TABLE_SETUP = _Paths.TABLE_SETUP;
  static const CATEGORY_SETUP = _Paths.CATEGORY_SETUP;
  static const ADDITION_CATEGORY_SETUP = _Paths.ADDITION_CATEGORY_SETUP;
  static const PRODUCT_SETUP = _Paths.PRODUCT_SETUP;
  static const PRODUCT_EDITING = _Paths.PRODUCT_EDITING;
  static const GODEX_PRINTER_SETUP = _Paths.GODEX_PRINTER_SETUP;
  static const REVENUE = _Paths.REVENUE;
  static const ORDERS_SETUP = _Paths.ORDERS_SETUP;
  static const ORDERS_ADJUST = _Paths.ORDERS_ADJUST;
  static const ORDERS_CONFIRM = _Paths.ORDERS_CONFIRM;
  static const ORDER_EDITING = _Paths.ORDER_EDITING;
  static const TABLES_SELECTION = _Paths.TABLES_SELECTION;
  static const ORDER_RECEIPT = _Paths.ORDER_RECEIPT;
  static const GODEX_PRINTER_CATEGORY_SELECTION =
      _Paths.GODEX_PRINTER_CATEGORY_SELECTION;
  static const RECEIPT_STICKER_PRINTING = _Paths.RECEIPT_STICKER_PRINTING;
  static const ORDERS_SUM_UP = _Paths.ORDERS_SUM_UP;
  static const ORDERS_SELECTOR = _Paths.ORDERS_SELECTOR;
  static const LINE_AT_SETTINGS = _Paths.LINE_AT_SETTINGS;
  static const MEMBERS = _Paths.MEMBERS;
  static const ONLINE_ORDER_SETTINGS = _Paths.ONLINE_ORDER_SETTINGS;
  static const BUSINESS_HOURS_SETUP = _Paths.BUSINESS_HOURS_SETUP;
  static const ORDERS_PICKER = _Paths.ORDERS_PICKER;
  static const RETAIL_TAKEOUT_SETTINGS = _Paths.RETAIL_TAKEOUT_SETTINGS;
  static const RETAIL_DELIVERY_SETTINGS = _Paths.RETAIL_DELIVERY_SETTINGS;
  static const PAYMENT_BANK = _Paths.PAYMENT_BANK;
  static const PAYMENT_INSTORE = _Paths.PAYMENT_INSTORE;
  static const PAYMENT_COD = _Paths.PAYMENT_COD;
  static const INVOICE_SETTINGS = _Paths.INVOICE_SETTINGS;
  static const BRANDS_BASIC = _Paths.BRANDS_BASIC;
  static const BRANDS_NEWS = _Paths.BRANDS_NEWS;
  static const ECPAY = _Paths.ECPAY;
  static const BRANDS_BANNERS = _Paths.BRANDS_BANNERS;
  static const PUSH_SETTINGS = _Paths.PUSH_SETTINGS;
  static const SETTING_PAY = _Paths.SETTING_PAY;
  static const SETTING_PAY_EDITOR = _Paths.SETTING_PAY_EDITOR;
  static const SETTING_PAY_ADD = _Paths.SETTING_PAY_ADD;
  static const MULTIPLE_PAYMENT = _Paths.MULTIPLE_PAYMENT;
  static const REPORTS_STATEMENTS = _Paths.REPORTS_STATEMENTS;
  static const REPORTS_SALES = _Paths.REPORTS_SALES;
  static const MEMBER_PROFILE = _Paths.MEMBER_PROFILE;
  static const MEMBER_DETAIL = _Paths.MEMBER_DETAIL;
  static const MEMBER_MEMO = _Paths.MEMBER_MEMO;
  static const POINTS_HISTORY = _Paths.POINTS_HISTORY;
  static const SETTING_POINTS = _Paths.SETTING_POINTS;
  static const SETTING_SERVICE = _Paths.SETTING_SERVICE;
  static const MEMBER_REPORT = _Paths.MEMBER_REPORT;
  static const ALBUM = _Paths.ALBUM;
  static const COUPON_DETAIL = _Paths.COUPON_DETAIL;
  static const COUPONS = _Paths.COUPONS;
  static const MEMBER_COUPONS = _Paths.MEMBER_COUPONS;
  static const SETTING_AUTO_PRINT = _Paths.SETTING_AUTO_PRINT;
  static const SETTING_DM = _Paths.SETTING_DM;
  static const ORDER_FILTER_PICKER = _Paths.ORDER_FILTER_PICKER;
  static const ORDER_LIST = _Paths.ORDER_LIST;
  static const SETTING_PRINTER = _Paths.SETTING_PRINTER;
  static const PRINTER_PICKER = _Paths.PRINTER_PICKER;
  static const PRINTER_DETAIL = _Paths.PRINTER_DETAIL;
  static const SETTING_DISCOUNT = _Paths.SETTING_DISCOUNT;
  static const PROMOTION_PICKER = _Paths.PROMOTION_PICKER;
  static const SETTING_DINNER_ONLINE_ORDER = _Paths.SETTING_DINNER_ONLINE_ORDER;
  static const SETTING_DINNER_ONLINE_HERE = _Paths.SETTING_DINNER_ONLINE_HERE;
  static const SETTING_DINNER_ONLINE_TOGO = _Paths.SETTING_DINNER_ONLINE_TOGO;
  static const SETTING_DINNER_ONLINE_DELIVERY =
      _Paths.SETTING_DINNER_ONLINE_DELIVERY;
  static const PAYMENT_LINE_PAY = _Paths.PAYMENT_LINE_PAY;
  static const REPORTS_REALTIME_STATEMENTS = _Paths.REPORTS_REALTIME_STATEMENTS;
}

abstract class _Paths {
  static const HOME = '/home';
  static const LOGIN = '/login';
  static const SETTINGS = '/settings';
  static const ORDERS = '/orders';
  static const ACCOUNTS = '/accounts';
  static const PASSWORD = '/password';
  static const ORDER_DETAIL = '/order-detail';
  static const ACCOUNT_DETAIL = '/account-detail';
  static const BLANK = '/blank';
  static const PARTITION_SETUP = '/partition-setup';
  static const TABLE_SETUP = '/partition-setup/table-setup';
  static const CATEGORY_SETUP = '/category-setup';
  static const ADDITION_CATEGORY_SETUP = '/addition-category-setup';
  static const PRODUCT_SETUP = '/product-setup';
  static const PRODUCT_EDITING = '/product-editing';
  static const GODEX_PRINTER_SETUP = '/godex-printer-setup';
  static const REVENUE = '/revenue';
  static const ORDERS_SETUP = '/orders-setup';
  static const ORDERS_ADJUST = '/orders-adjust';
  static const ORDERS_CONFIRM = '/orders-confirm';
  static const ORDER_EDITING = '/order-editing';
  static const TABLES_SELECTION = '/tables-selection';
  static const ORDER_RECEIPT = '/order-receipt';
  static const GODEX_PRINTER_CATEGORY_SELECTION =
      '/godex-printer-category-selection';
  static const RECEIPT_STICKER_PRINTING = '/receipt-sticker-printing';
  static const ORDERS_SUM_UP = '/orders-sum-up';
  static const ORDERS_SELECTOR = '/orders-selector';
  static const LINE_AT_SETTINGS = '/line-at-settings';
  static const MEMBERS = '/members';
  static const ONLINE_ORDER_SETTINGS = '/online-order-settings';
  static const BUSINESS_HOURS_SETUP = '/business-hours-setup';
  static const ORDERS_PICKER = '/orders-picker';
  static const RETAIL_TAKEOUT_SETTINGS = '/retail-takeout-settings';
  static const RETAIL_DELIVERY_SETTINGS = '/retail-delivery-settings';
  static const PAYMENT_BANK = '/payment-bank';
  static const PAYMENT_INSTORE = '/payment-instore';
  static const PAYMENT_COD = '/payment-cod';
  static const INVOICE_SETTINGS = '/invoice-settings';
  static const BRANDS_BASIC = '/brands-basic';
  static const BRANDS_NEWS = '/brands-news';
  static const ECPAY = '/ecpay';
  static const BRANDS_BANNERS = '/brands-banners';
  static const PUSH_SETTINGS = '/push-settings';
  static const SETTING_PAY = '/setting-pay';
  static const SETTING_PAY_EDITOR = '/setting-pay-editor';
  static const SETTING_PAY_ADD = '/setting-pay-add';
  static const MULTIPLE_PAYMENT = '/multiple-payment';
  static const REPORTS_STATEMENTS = '/reports-statements';
  static const REPORTS_SALES = '/reports-sales';
  static const MEMBER_PROFILE = '/member-profile';
  static const MEMBER_DETAIL = '/member-detail';
  static const MEMBER_MEMO = '/member-memo';
  static const POINTS_HISTORY = '/points-history';
  static const SETTING_POINTS = '/setting-points';
  static const SETTING_SERVICE = '/setting-service';
  static const MEMBER_REPORT = '/member-report';
  static const ALBUM = '/album';
  static const COUPON_DETAIL = '/coupon-detail';
  static const COUPONS = '/coupons';
  static const MEMBER_COUPONS = '/member-coupons';
  static const SETTING_AUTO_PRINT = '/setting-auto-print';
  static const SETTING_DM = '/setting-dm';
  static const ORDER_FILTER_PICKER = '/order-filter-picker';
  static const ORDER_LIST = '/order-list';
  static const SETTING_PRINTER = '/setting-printer';
  static const PRINTER_PICKER = '/printer-picker';
  static const PRINTER_DETAIL = '/printer-detail';
  static const SETTING_DISCOUNT = '/setting-discount';
  static const PROMOTION_PICKER = '/promotion-picker';
  static const SETTING_DINNER_ONLINE_ORDER = '/setting-dinner-online-order';
  static const SETTING_DINNER_ONLINE_HERE = '/setting-dinner-online-here';
  static const SETTING_DINNER_ONLINE_TOGO = '/setting-dinner-online-togo';
  static const SETTING_DINNER_ONLINE_DELIVERY =
      '/setting-dinner-online-delivery';
  static const PAYMENT_LINE_PAY = '/payment-line-pay';
  static const REPORTS_REALTIME_STATEMENTS = '/reports-realtime-statements';
}
