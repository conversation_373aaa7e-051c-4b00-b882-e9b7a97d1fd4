import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_obx.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/message_page.dart';
import 'package:muyipork/app/components/save_page.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/data/models/other/city.dart';
import 'package:muyipork/app/data/models/other/district.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/brands_basic_controller.dart';

class BrandsBasicView extends GetView<BrandsBasicController> {
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '基本資料',
      child: controller.obx(
        (state) {
          return SavePage(
            onPressed: _submit,
            child: Form(
              key: _formKey,
              child: _body(),
            ),
          );
        },
        onError: (message) {
          return MessagePage(
            icon: DialogContentIcon.Alert,
            message: message,
          );
        },
      ),
    );
  }

  Widget _body() {
    return ListView(
      padding: EdgeInsets.only(bottom: kBottomButtonPadding),
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    // 基本資料
    yield* _page1();
    // 預設連結
    yield* _page2();
    // 自訂連結
    yield* _page3();
    // 訂訂優惠訊息顯示設定
    // yield* _page4();
  }

  Iterable<Widget> _page4() sync* {
    yield SettingsWidget.title(
      titleText: '訂訂優惠訊息顯示設定',
    );
    yield SettingsWidget.switcher(
      titleText: '餐飲類別',
    );
    yield SettingsWidget.input(
      labelText: '臉書優惠文章網址',
      hintText: '請輸入網址',
    );
    yield SettingsWidget.switcher(
      titleText: '購物類別',
    );
    yield SettingsWidget.input(
      labelText: '臉書優惠文章網址',
      hintText: '請輸入網址',
    );
  }

  Iterable<Widget> _page3() sync* {
    yield SettingsWidget.title(
      titleText: '自訂連結',
    );
    yield SettingsWidget.input(
      labelText: '1.自訂連結名稱',
      hintText: '請輸入名稱',
      initialValue: controller.draft.getCustomUrlAt(0).name,
      onChanged: (value) {
        controller.draft.getCustomUrlAt(0).name = value;
      },
    );
    yield SettingsWidget.input(
      contentPadding: EdgeInsets.only(
        left: kPadding * 2.0,
        right: kPadding,
      ),
      labelText: '自訂連結網址',
      hintText: '請輸入網址',
      initialValue: controller.draft.getCustomUrlAt(0).url,
      onChanged: (value) {
        controller.draft.getCustomUrlAt(0).url = value;
      },
    );
    yield SettingsWidget.input(
      labelText: '2.自訂連結名稱',
      hintText: '請輸入名稱',
      initialValue: controller.draft.getCustomUrlAt(1).name,
      onChanged: (value) {
        controller.draft.getCustomUrlAt(1).name = value;
      },
    );
    yield SettingsWidget.input(
      labelText: '自訂連結網址',
      hintText: '請輸入網址',
      contentPadding: EdgeInsets.only(
        left: kPadding * 2.0,
        right: kPadding,
      ),
      initialValue: controller.draft.getCustomUrlAt(1).url,
      onChanged: (value) {
        controller.draft.getCustomUrlAt(1).url = value;
      },
    );
    yield SettingsWidget.input(
      labelText: '3.自訂連結名稱',
      hintText: '請輸入名稱',
      initialValue: controller.draft.getCustomUrlAt(2).name,
      onChanged: (value) {
        controller.draft.getCustomUrlAt(2).name = value;
      },
    );
    yield SettingsWidget.input(
      labelText: '自訂連結網址',
      hintText: '請輸入網址',
      contentPadding: EdgeInsets.only(
        left: kPadding * 2.0,
        right: kPadding,
      ),
      initialValue: controller.draft.getCustomUrlAt(2).url,
      onChanged: (value) {
        controller.draft.getCustomUrlAt(2).url = value;
      },
    );
  }

  Iterable<Widget> _page2() sync* {
    yield SettingsWidget.title(
      titleText: '預設連結',
    );
    yield SettingsWidget.input(
      labelText: 'Website(官網)',
      hintText: '請輸入網址',
      initialValue: controller.draft.website,
      onChanged: (value) {
        controller.draft.website = value;
      },
    );
    yield SettingsWidget.input(
      labelText: 'Facebook',
      hintText: '請輸入網址',
      initialValue: controller.draft.facebook,
      onChanged: (value) {
        controller.draft.facebook = value;
      },
    );
    yield SettingsWidget.input(
      labelText: 'Instagram',
      hintText: '請輸入網址',
      initialValue: controller.draft.instagram,
      onChanged: (value) {
        controller.draft.instagram = value;
      },
    );
    yield SettingsWidget.input(
      labelText: 'YouTube',
      hintText: '請輸入網址',
      initialValue: controller.draft.youtube,
      onChanged: (value) {
        controller.draft.youtube = value;
      },
    );
    yield SettingsWidget.input(
      labelText: 'E-mail',
      hintText: '請輸入E-mail',
      initialValue: controller.draft.email,
      onChanged: (value) {
        controller.draft.email = value;
      },
      validator: (value) {
        if (value.isEmpty) {
          return null;
        }
        if (value.isEmail) {
          return null;
        }
        return 'E-mail 格式錯誤';
      },
    );
  }

  Iterable<Widget> _page1() sync* {
    yield SettingsWidget.title(
      titleText: '基本資料',
    ).paddingOnly(
      top: 8.0,
    );
    yield SettingsWidget.input(
      labelText: '營業人名稱',
      hintText: '請輸入營業人名稱',
      initialValue: controller.draft.taxName ?? '',
      onChanged: (value) {
        controller.draft.taxName = value;
      },
    );
    yield SettingsWidget.input(
      labelText: '統一編號',
      hintText: '請輸入統一編號',
      initialValue: controller.draft.taxId ?? '',
      readonly: true,
    );
    yield ColoredBox(
      color: Colors.white,
      child: SizedBox(
        width: double.infinity,
        child: Text(
          '地址',
          style: const TextStyle(
            fontSize: 16.0,
            color: Colors.black,
          ),
        ).paddingSymmetric(
          vertical: 4.0,
          horizontal: kPadding,
        ),
      ),
    );
    yield Row(
      children: [
        Expanded(
          // child: DropdownButton<num>(
          //   items: controller.prefProvider.cities.map((e) {
          //     return DropdownMenuItem<int>(
          //       value: e.id,
          //       child: Text(e.name),
          //     );
          //   }).toList(),
          //   underline: SizedBox.shrink(),
          //   value: controller.data.cityId,
          //   onChanged: (value) {
          //     //
          //   },
          // ),
          child: TextFormField(
            controller: controller.cityEditing,
            readOnly: true,
            decoration: InputDecoration(
              hintText: '縣市',
              suffixIcon: Icon(Icons.expand_more),
              // border: InputBorder.none,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2.0),
                borderSide: BorderSide.none,
              ),
              fillColor: Colors.white,
              filled: true,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: kPadding,
              ),
            ),
            onTap: this._showCityPicker,
            validator: (value) {
              if (value.isEmpty) {
                return '必填項目';
              }
              return null;
            },
          ),
        ),
        Expanded(
          child: TextFormField(
            controller: controller.districtEditing,
            readOnly: true,
            decoration: InputDecoration(
              hintText: '鄉鎮市區',
              suffixIcon: Icon(Icons.expand_more),
              // border: InputBorder.none,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2.0),
                borderSide: BorderSide.none,
              ),
              fillColor: Colors.white,
              filled: true,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: kPadding,
              ),
            ),
            onTap: this._showDistrictPicker,
            validator: (value) {
              if (value.isEmpty) {
                return '必填項目';
              }
              return null;
            },
          ),
        ),
      ],
    );
    yield SettingsWidget.input(
      labelText: '詳細地址',
      initialValue: controller.draft.address,
      onChanged: (value) {
        controller.draft.address = value;
      },
    );
    yield SettingsWidget.input(
      labelText: '電話',
      initialValue: controller.draft.phone,
      onChanged: (value) {
        controller.draft.phone = value;
      },
    );
  }

  void _showDistrictPicker() {
    final districts = controller.draft.districts;
    ListView.builder(
      itemCount: districts.length,
      itemBuilder: (context, index) {
        final data = districts.elementAt(index);
        return ListTile(
          tileColor: Colors.white,
          onTap: () {
            Get.back(result: data);
          },
          title: Text(data.name ?? ''),
        );
      },
    )
        .dialog<District>(
      insetPadding: kInsetPadding,
      barrierDismissible: true,
    )
        .then(
      (value) {
        if (value != null) {
          controller.draft.cityareaId = value.id;
          controller.refreshDraft();
        }
      },
    );
  }

  void _showCityPicker() {
    ListView.builder(
      itemCount: controller.prefProvider.cities.length,
      itemBuilder: (context, index) {
        final data = controller.prefProvider.cities.elementAt(index);
        return ListTile(
          tileColor: Colors.white,
          onTap: () {
            Get.back(result: data);
          },
          title: Text(data.name ?? ''),
        );
      },
    )
        .dialog<City>(
      insetPadding: kInsetPadding,
      barrierDismissible: true,
    )
        .then(
      (value) {
        if (value != null) {
          controller.draft.cityId = value.id;
          controller.refreshDraft();
        }
      },
    );
  }

  void _submit() {
    if (_formKey.currentState.validate()) {
      FutureProgress(
        future: controller.submit(),
      ).dialog(barrierDismissible: false).then((value) {
        if (value is num && value > 0) {
          Get.back();
        }
      });
    }
  }
}
