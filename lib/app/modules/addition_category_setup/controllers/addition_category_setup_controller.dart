import 'dart:async';
import 'dart:math';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:muyipork/app/data/models/req/id_sort.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/product_provider.dart';

class AdditionCategorySetupArgs {
  final Category category;
  final int kind;

  const AdditionCategorySetupArgs({
    this.category,
    this.kind,
  });
}

class AdditionCategorySetupController extends GetxController
    with StateMixin<String> {
  final _disposable = Completer();
  final _deleting = <num>[].obs;
  final _updating = <num>[].obs;
  final ProductProvider productProvider;
  ApiProvider get apiProvider => productProvider.apiProvider;
  BoxProvider get boxProvider => productProvider.boxProvider;

  AdditionCategorySetupArgs get args => _args.value;

  final _draft = Category().obs;
  Category get draft => _draft.value;

  final _creating = AdditionProduct().obs;
  AdditionProduct get creating => _creating.value;

  final _args = Rx<AdditionCategorySetupArgs>(null);
  final data = <AdditionProduct>[].obs;

  AdditionCategorySetupController({
    @required this.productProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _args.value = Get.arguments;
    _draft.value = args.category;

    // 監聽快取，變動時更新顯示
    final box = boxProvider.getGsBox(kBoxAdditionProduct);
    box
        .watch()
        .debounce(1.seconds)
        .takeUntil(_disposable.future)
        .listen((event) {
      final it = List.from(box.getValues(), growable: false)
          .map((e) => AdditionProduct.fromJson(e))
          .where((element) => element.additionCategoryId == args.category.id);
      data.assignAll(it);
    });
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    try {
      final items = await productProvider.getAdditionProducts(
        args.kind.productKind,
        additionCategoryId: args.category.id,
      );
      data.assignAll(items);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<bool> submit() async {
    try {
      await _applyAdditionCategory();
      // 更新
      await _applyUpdating();
      // 刪除
      await _applyDeleting();
      // 新增
      await applyCreating();
      // 排序
      await _applySorting();
      return true;
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> _applyAdditionCategory() async {
    try {
      final ret = await productProvider.putAdditionCategories(draft);
      return ret is num && ret > 0;
    } catch (e) {
      rethrow;
    }
  }

  // 新增一格
  Future<bool> applyCreating() async {
    if (creating.name == null || creating.name.isEmpty) {
      // throw '選項名稱與金額是必填項目';
      return false;
    }
    // 取得數值最大 sort
    final sort = data.fold<num>(0, (previousValue, element) {
      element.sort ??= 0;
      return max(previousValue, element.sort);
    });
    creating.additionCategoryId = args.category.id;
    creating.kind = args.kind;
    creating.sort = sort + 1;
    creating.price ??= 0;
    final ret = await productProvider.postAdditionProduct(creating);
    if (ret != null && ret > 0) {
      // 產生新的草稿;
      _creating.value = AdditionProduct();
    }
    return true;
  }

  Future<bool> deleting(num id) async {
    // TODO: 檢查商品使用此規格
    try {
      _deleting.add(id);
      data.removeWhere((element) => element.id == id);
      return true;
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> _applyDeleting() async {
    if (_deleting.isEmpty) {
      return true;
    }
    final ids = _deleting.toSet();
    _deleting.clear();
    final futures = ids.map((e) => productProvider.deleteAdditionProduct(e));
    try {
      final list = await Future.wait(futures);
      return list.every((element) => element);
    } catch (e) {
      rethrow;
    }
  }

  void updating(num id) {
    _updating.addIf(!_updating.contains(id), id);
  }

  Future<bool> _applyUpdating() async {
    if (_updating.isEmpty) {
      return true;
    }
    final ids = _updating.toSet();
    _updating.clear();
    for (var id in ids) {
      final index = data.indexWhere((element) => element.id == id);
      if (index >= 0) {
        final element = data.elementAt(index);
        try {
          await productProvider.putAdditionProduct(element);
        } catch (e) {
          rethrow;
        }
      }
    }
    return true;
  }

  // 移動某格至某格
  void sorting(int srcIndex, int destIndex) {
    final list = data;
    if (srcIndex < destIndex) {
      // 排序不超過新增
      if (destIndex <= list.length) {
        final element = list.elementAt(srcIndex);
        list.insert(destIndex, element);
        list.removeAt(srcIndex);
      }
    } else {
      final element = list.removeAt(srcIndex);
      list.insert(destIndex, element);
    }
    for (var i = 0; i < list.length; i++) {
      final element = list.elementAt(i);
      element.sort = i;
    }
  }

  Future<bool> _applySorting() {
    final list = data;
    final it =
        list.map((element) => IDSort(id: element.id, sort: element.sort));
    return productProvider.sortAdditionProducts(it);
  }
}
