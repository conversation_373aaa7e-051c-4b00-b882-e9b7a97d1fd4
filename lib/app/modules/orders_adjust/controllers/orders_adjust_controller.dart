import 'dart:async';

import 'package:bpscm/bpscm.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_sunmi_printer/flutter_sunmi_printer.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/setting_point.dart';
import 'package:muyipork/app/data/models/res/order_root.dart';
import 'package:muyipork/app/modules/receipt_sticker_printing/controllers/receipt_sticker_printing_controller.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:okshop_esc_pos/okshop_esc_pos.dart';
import 'package:screenshot/screenshot.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/other/coupon.dart';
import 'package:muyipork/app/data/models/req/orders_post_req.dart';
import 'package:muyipork/app/data/models/req/products_get_qry.dart';
import 'package:muyipork/app/data/models/res/products_get_res.dart';
import 'package:muyipork/app/data/models/res/tables_get_res.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/coupon_provider.dart';
import 'package:muyipork/app/providers/member_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/product_provider.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/static_methods.dart';

enum OrdersAdjustMode {
  CheckOut, //結帳用
  Edit, //只做編輯用 (訂單詳情過來的)
}

class OrdersAdjustArgs {
  OrdersAdjustArgs({
    this.mode,
    this.ordersPostReq,
    this.kind,
  });
  final OrdersAdjustMode mode;
  final OrdersPostReq ordersPostReq;

  // 類型
  // 0: 餐飲店內
  // 1: 餐飲線上 (這個在此處應該是不會有)
  // 2: 零售
  final int kind;
}

class OrdersAdjustController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final OrderProvider orderProvider;
  final ProductProvider productProvider;
  final CouponProvider couponProvider;
  final InvoiceProvider invoiceProvider;
  final PrinterProvider printerProvider;
  MemberProvider get memberProvider => orderProvider.memberProvider;
  ApiProvider get apiProvider => memberProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => memberProvider.boxProvider;

  // 優惠券
  final _coupon = Rx<Coupon>(null);
  Coupon get coupon => _coupon.value;

  //Partition 桌號清單
  final tablesGetRes = Rx<TablesGetRes>(null);

  //比對更新價格用
  final productsGetRes = Rx<ProductsGetRes>(null);

  final mode = Rx<OrdersAdjustMode>(OrdersAdjustMode.CheckOut);

  final _draft = Rx<OrdersPostReq>(null);
  OrdersPostReq get draft => _draft.value;

  //正在編輯當中準備要送出的 Orders 結構
  OrdersAdjustArgs args;

  //記錄下是否有變動過任何數字
  bool hasDataChanged = false;

  final vip = false.obs;
  // 發票截圖
  final invoiceScreenshotController = ScreenshotController();
  final widgetUpdater = Completer();
  final _invoice = Rx<Invoice>(null);
  Invoice get invoice => _invoice.value;

  OrdersAdjustController({
    @required this.productProvider,
    @required this.orderProvider,
    @required this.couponProvider,
    @required this.invoiceProvider,
    @required this.printerProvider,
  });

  @override
  void onInit() async {
    super.onInit();

    final draftStream = _draft.stream.asBroadcastStream();

    draftStream
        .map((event) => event.memberCouponId)
        .distinct()
        .asyncMap((event) {
          if (event != null) {
            return couponProvider.getMemberCoupon(
                draft.memberId, draft.memberCouponId);
          } else {
            resetCoupon();
          }
        })
        .takeUntil(_disposable.future)
        .listen((event) {
          if (event == null || event.id == null) {
            resetCoupon();
          } else {
            _coupon.value = event;
            // FIXME: 計算優惠
          }
        });

    //變數必須是 ordersPostReq
    args = Get.arguments;
    _draft.value = args.ordersPostReq;
    mode.value = args.mode;
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  //取得當前選取桌面按鈕上的顯示文字
  String selectedTableDisplayName() {
    String returnStr = '桌號';
    if (draft != null &&
        draft.table1Id != null &&
        draft.table2Id != null &&
        tablesGetRes.value != null) {
      return tablesGetRes.value.getDisplayName(draft.table1Id, draft.table2Id);
    }
    //在無資料或者沒有選擇好的狀況下自然只會回預設值。
    return returnStr;
  }

  //是否已經選擇了合法的桌號
  bool hasSelectTable() {
    if (draft != null) {
      return draft.hasSelectLegalTable();
    }
    return false;
  }

  //Get the Partition/Table tree.
  Future<void> _reFetchTables() async {
    TablesGetRes result = await apiProvider.getTables();
    if (result != null) {
      if (result.hasError()) {
        handleGeneralResponseError(result);
      } else {
        tablesGetRes.value = result;
      }
    }
  }

  //更新資料
  Future<void> _reFetchProducts() async {
    productsGetRes.value = null;
    ProductsGetRes result =
        await apiProvider.getProducts(ProductsGetQry(kind: args.kind));
    if (result != null) {
      if (result.hasError()) {
        handleGeneralResponseError(result);
      } else {
        productsGetRes.value = result;
      }
    }
  }

  void refreshDraft() {
    draft.ensureNoZeroQuantityItem();
    _draft.refresh();
  }

  void _refreshPage() {
    change('', status: RxStatus.success());
  }

  Future<void> _fetchMember() async {
    if (draft.memberId != null && draft.memberId > 0) {
      await memberProvider.getMember(draft.memberId).then(
        (value) {
          vip.value = value?.isVip?.switcher?.isOn ?? false;
        },
      );
    }
  }

  Future<void> onRefresh() async {
    await _reFetchTables().then(
      (value) {
        return _reFetchProducts();
      },
    ).then(
      (value) {
        return _fetchMember();
      },
    ).then(
      (value) {
        _refreshPage();
      },
    ).catchError(
      (error) {
        change('', status: RxStatus.error('$error'));
      },
    );
  }

  ///
  /// 清除優惠券
  ///
  void resetCoupon() {
    draft.memberCouponId = null;
    draft.memberCouponDiscount = null;
    draft.memberCouponExtraPrice = null;
    _coupon.value = null;
    refreshDraft();
  }

  String get couponMessage {
    if (coupon != null) {
      if (coupon.message != null && coupon.message.isNotEmpty) {
        return coupon.message;
      }
      // 檢查線上或線下
      if (coupon.isOnline != draft.source) {
        if (coupon.isOnline.switcher.isOn) {
          return '線上優惠券只能於 line 下單使用';
        } else {
          return '線下優惠券只能於現場使用';
        }
      }
      // 檢查餐飲或零售
      // 特殊:
      // 1: 餐飲
      // 2: 零售
      // FIXME:
      // if (coupon.storeType != draft.orderType.storeType) {
      //   if (coupon.storeType.isDinner) {
      //     return '餐飲優惠券只適用於餐飲訂單';
      //   } else {
      //     return '零售優惠券只適用於零售訂單';
      //   }
      // }
      // 檢查最低金額
      final minPrice = coupon.minPrice ?? 0;
      if (draft.itemsTotal < minPrice) {
        return '提醒：未達最低消費金額$minPrice元，不得使用本券!';
      }
    }
    return '';
  }

  ///
  /// 現金快結
  /// 1. 只有前結帳版本需要
  /// 2. 參數外帶、現金、無桌號、當下時間、無服務費
  ///
  Future<num> checkout() async {
    // 餐飲外帶
    OrderType orderType = OrderType.DinnerToGo;
    if (prefProvider.brandsType.containsRetail) {
      // 零售自取
      orderType = OrderType.RetailToGo;
    }
    final orderId = await _createOrder(orderType);
    final order = await orderProvider.getOrderDetail(orderId, cacheFirst: true);
    // print sticker
    await Get.toNamed(
      Routes.RECEIPT_STICKER_PRINTING,
      parameters: {Keys.Tag: '$orderId'},
      arguments: PrintingArgs(
        printingMode: PrintingMode.PrintAllAndLeave,
        printingPageSetting: PrintingPageSetting.StickerOnly,
        orderId: orderId,
      ),
    );
    // 發票
    if (draft.invoice == true && draft.total is num && draft.total > 0) {
      try {
        // 產生發票
        final invoiceNumber = await _postInvoice(order);
        // 有字軌則列印發票
        if (invoiceNumber != null &&
            invoiceNumber.isNotEmpty &&
            draft.isNeedToPrint) {
          // 列印發票
          _invoice.value = await _createInvoice(orderId);
          await _printInvoice(invoice);
        }
      } catch (e) {
        logger.e(e);
      }
    }
    // TODO: 列印
    final receipt = order.asReceipt(
      storeName: prefProvider.brandsInfo?.name ?? '',
      userName: prefProvider.jwt?.name ?? '',
    );
    try {
      await _printReceiptLite(receipt);
    } catch (e) {
      logger.e(e);
    }
    try {
      await _printReceiptItem(receipt);
    } catch (e) {
      logger.e(e);
    }

    return orderId;
  }

  Future<String> _postInvoice(OrderRoot order) async {
    // 發票號碼
    final invoiceNumber = await invoiceProvider.getInvoiceNumber(
      seller: prefProvider.brandsInfo.taxId,
      date: order.checkoutAt ?? order.data.createdAt.localAt,
    );
    if (invoiceNumber != null && invoiceNumber.isNotEmpty) {
      draft.invoiceNumber = invoiceNumber;
      // 隨機碼
      draft.randomNumber = InvoiceProvider.genRandomNumber();
      // 是否列印紙本
      draft.invoicePaper = draft.isNeedToPrint;
      // 載具/愛心碼
      final carrierId = draft.carrierId;
      draft.carrierId = Utils.isCarrierId(carrierId) ? carrierId : '';
      draft.npoBan = Utils.isNpoBan(carrierId) ? carrierId : '';
      // 載具類別
      if (draft.carrierId != null && draft.carrierId.isNotEmpty) {
        draft.carrierType = CarrierType.Mobile.index; // 3: 手機條碼
      }
      final data = draft.toOrdersOrderIdInvoicePostReq();
      // 送出發票訊息到 okshop server
      final orderId = await orderProvider.postOrderInvoice(order.data.id, data);
      if (orderId is num && orderId > 0) {
        try {
          await _syncInvoice(orderId);
        } catch (e) {
          // 上傳發票失敗，加入補上傳佇列
          final box = await boxProvider.getLazyBox(kKeyBoxOrderInvoice);
          final key = '$orderId.${BpscmInvoiceStatus.Invoice.value}';
          await box.put(key, invoiceNumber);
        }
      }
    }
    return invoiceNumber;
  }

  Future<String> _syncInvoice(num orderId) async {
    final order = await orderProvider.getOrderDetail(orderId);
    final brandsInfo = prefProvider.brandsInfo;
    // 上傳金財通物件
    final data = order.asPosInvoiceNewsReq(
      apiKey: prefProvider.invoiceApiKey,
      taxId: prefProvider.brandsInfo.taxId,
      posBan: kPosBAN,
      storeName: brandsInfo.name,
      storeCode: brandsInfo.code,
    );
    // 更新品項稅率
    for (var element in data.invoiceDetails) {
      // 預設應稅
      var taxType = TaxType.TX;
      try {
        // 取得產品
        final product = await productProvider.getProductDetail(
          num.tryParse(element.relateNumber ?? '') ?? 0,
          cacheFirst: true,
        );
        // 取得產品的稅率 (預設應稅)
        taxType = product?.taxTypeEnum ?? TaxType.TX;
      } catch (e) {
        logger.e(e);
      }
      // 儲存稅別
      element.remark = '${taxType.bpscmType}';
    }
    // 計算稅率總和
    data.refresh();
    // 上傳發票
    final ret = await invoiceProvider.postPosInvoiceNewsSingle(data);
    if ('OK' == ret.status) {
      return ret.invoiceNo;
    }
    throw ret.message;
  }

  Future<num> _createOrder([OrderType orderType]) {
    // 預設餐飲外帶
    draft.type = (orderType ?? OrderType.DinnerToGo).index;
    // 訂單完成
    draft.status = OrderStatus.Completed.index;
    // 已付款
    draft.paymentStatus = PaymentStatus.Paid.index;
    // 特殊: 重構多重支付方式
    draft.refactorMultiplePayment();
    draft.paid = draft.total;
    // 積點
    final sp = prefProvider.settingPoint.firstWhere(
      (element) => draft.storeType == element.storeType,
      orElse: () => SettingPoint(),
    );
    if (sp.isAvailable && draft.memberId != null) {
      // 無條件捨去
      final ret = (draft.total / sp.cashRatio).floor();
      logger.d('[OrderAdjustController] pointGet($ret)');
      draft.pointGet = ret;
    }
    return orderProvider.postOrders(draft);
  }

  ///
  /// 列印消費明細
  ///
  Future<void> _printReceiptLite(Receipt receipt) async {
    if (prefProvider.localSettings.nnPrintReceipt.switcher.isOn) {
      final count = prefProvider.localSettings.nnPrintReceiptCount;
      // 找出印表機
      final printers = printerProvider
          .getPrinterWithCategories([PrintType.receiptLite.value]).where(
              (element) => element.status.switcher.isOn);
      for (var i = 0; i < count; i++) {
        // 列印消費明細
        await SunmiUtil.printReceiptLite(receipt);
        // for (var j = 0; j < printers.length; j++) {
        //   final printer = printers.elementAt(j);
        //   await printer.printReceiptLite(receipt);
        // }
        // await Future.wait(printers.map((e) => e.printReceiptLite(receipt)));
        printers
            .where((element) => element.type == PrinterType.net.value)
            .forEach((printer) async {
          await printer.printReceiptLite(receipt);
        });
      }
    }
  }

  ///
  /// 列印商品明細
  ///
  Future<void> _printReceiptItem(Receipt receipt) async {
    if (prefProvider.localSettings.nnPrintItemReceipt.switcher.isOn) {
      final count = prefProvider.localSettings.nnPrintItemReceiptCount;
      // 找出印表機
      final printers = printerProvider
          .getPrinterWithCategories([PrintType.receiptItem.value]).where(
              (element) => element.status.switcher.isOn);
      for (var i = 0; i < count; i++) {
        await SunmiUtil.printReceiptItems(receipt);
        printers
            .where((element) => element.type == PrinterType.net.value)
            .forEach((printer) async {
          // await printer.printReceiptItem(receipt);
          final bytes = await printer.receiptItemTask(receipt);
          printer.pushTask(bytes);
        });
      }
    }
  }

  ///
  /// 列印發票
  ///
  Future<void> _printInvoice(Invoice invoice) async {
    // 使用 V2 列印
    try {
      await SunmiUtil.printInvoice(invoice);
    } catch (e) {
      logger.e(e);
    }
    // 使用商米印表機列印
    try {
      await _printInvoiceByEsc();
    } catch (e) {
      logger.e(e);
    }
  }

  // FIXME: Implement
  Future<void> _printInvoiceByEsc() async {
    try {
      // 等待內容變更完成
      await widgetUpdater.future;
      final image = await invoiceScreenshotController.capture(
        pixelRatio: 1.0,
        delay: 200.milliseconds,
      );
      // 尋找 printers
      final ids = [PrintType.invoice.value];
      final printers = printerProvider.getPrinterWithCategories(ids);
      for (var printer in printers) {
        // 使用圖片方式列印 invoice
        final bytes = await printer.invoiceTask(invoice, image);
        printer.pushTask(bytes);
        // 只印一張
        break;
      }
    } catch (e) {
      logger.e(e);
    }
  }

  ///
  /// 產生發票物件
  ///
  Future<Invoice> _createInvoice(num orderId) async {
    final order = await orderProvider.getOrderDetail(orderId, cacheFirst: true);
    final invoice = order.asInvoice();
    //
    final brandsInfo = prefProvider.brandsInfo;
    // final brandsInvoice = this.prefProvider.brandsInvoice;
    // final hasBuyer = invoice.buyer.isNotEmpty ?? false;
    // final taxType = brandsInvoice.taxTypeForServer;
    // final taxRate = InvoiceProvider.getTaxRate(hasBuyer, taxType);
    // 列印
    invoice.storeName = brandsInfo.name;
    invoice.printMark = Times.First.index; // 首次列印
    invoice.seller = brandsInfo.taxId;
    for (var item in invoice.items) {
      // 預設應稅
      var taxType = TaxType.TX;
      try {
        // 取得產品
        final product = await productProvider.getProductDetail(
          item.productId,
          cacheFirst: true,
        );
        // 取得產品的稅率
        taxType = product?.taxTypeEnum ?? TaxType.TX;
      } catch (e) {
        logger.e(e);
      }
      // 儲存稅別
      item.taxType = taxType.bpscmType;
    }
    invoice.refresh();
    return invoice;
  }
}
