import 'dart:async';
import 'dart:math';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/constants.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/other/coupons_req.dart';
import 'package:muyipork/app/data/models/req/id_sort.dart';
import 'package:muyipork/app/providers/coupon_provider.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:okshop_model/okshop_model.dart';

class SettingDiscountController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final _deleting = <num>[].obs;
  final _updating = <num>[].obs;
  final CouponProvider couponProvider;
  final _promotionType = PromotionType.Off.obs;
  PromotionType get promotionType => _promotionType.value;
  String get titleText => promotionType.title;
  final _filter = CouponsReq().obs;
  CouponsReq get filter => _filter.value;
  final data = <Coupon>[].obs;

  final _creating = Coupon().obs;
  Coupon get creating => _creating.value;

  SettingDiscountController({
    @required this.couponProvider,
  });

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey('type')) {
      final type =
          num.tryParse(Get.parameters['type']) ?? PromotionType.Max.index;
      _promotionType.value = type.promotionType;
      creating.promotionType = promotionType.index;
    }
    couponProvider.couponsLikeCached.stream
        .debounce(1.seconds)
        .map((event) {
          return event.values.where((element) {
            return element.promotionType == promotionType.index;
          });
        })
        .takeUntil(_disposable.future)
        .listen((event) {
          logger.d(
              '[SettingDiscountController] addProdCached changed: length(${event.length})');
          data.assignAll(event);
          data.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
        });
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  Future<void> onRefresh() async {
    try {
      filter.page = 1;
      filter.limit = 500;
      final it = await couponProvider.getCouponsLike(filter);
      final it2 =
          it.where((element) => element.promotionType == promotionType.index);
      data.assignAll(it2);
      data.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  void updating(num id) {
    _updating.addIf(!_updating.contains(id), id);
  }

  Future<bool> deleting(num id) async {
    _deleting.add(id);
    data.removeWhere((element) => element.id == id);
    return true;
  }

  Future<bool> submit() async {
    // 更新
    await _applyUpdating();
    // 刪除
    await _applyDeleting();
    // 新增
    await applyCreating();
    // 排序
    await _applySorting();
    return true;
  }

  Future<bool> _applyUpdating() async {
    if (_updating.isEmpty) {
      return true;
    }
    final ids = _updating.toSet();
    _updating.clear();
    for (var id in ids) {
      final index = data.indexWhere((element) => element.id == id);
      if (index >= 0) {
        final element = data.elementAt(index);
        await couponProvider.putCoupon(element);
      }
    }
    return true;
  }

  Future<bool> _applyDeleting() async {
    if (_deleting.isEmpty) {
      return true;
    }
    final ids = _deleting.toSet();
    _deleting.clear();
    final futures = ids.map((e) => couponProvider.deleteCouponsLikeById(e));
    final list = await Future.wait(futures);
    return list.every((element) => element);
  }

  // 新增一格
  Future<bool> applyCreating() async {
    if (creating.title == null || creating.title.isEmpty) {
      // throw '選項名稱與金額是必填項目';
      return false;
    }
    // 取得數值最大 sort
    final sort = data.fold<num>(0, (previousValue, element) {
      element.sort ??= 0;
      return max(previousValue, element.sort);
    });
    creating.promotionType = promotionType.index;
    creating.sort = sort + 1;
    creating.discount ??= 0;
    final ret = await couponProvider.postCoupon(creating);
    if (ret != null && ret > 0) {
      // 產生新的草稿;
      _creating.value = Coupon();
      creating.promotionType = promotionType.index;
    }
    return true;
  }

  void sorting(int srcIndex, int destIndex) {
    final list = data;
    if (srcIndex < destIndex) {
      // 排序不超過新增
      if (destIndex <= list.length) {
        final element = list.elementAt(srcIndex);
        list.insert(destIndex, element);
        list.removeAt(srcIndex);
      }
    } else {
      final element = list.removeAt(srcIndex);
      list.insert(destIndex, element);
    }
    for (var i = 0; i < list.length; i++) {
      final element = list.elementAt(i);
      element.sort = i;
    }
  }

  Future<bool> _applySorting() {
    final it =
        data.map((element) => IDSort(id: element.id, sort: element.sort));
    return couponProvider.sortCouponLike(it);
  }
}
