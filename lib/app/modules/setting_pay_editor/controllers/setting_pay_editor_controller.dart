import 'dart:async';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/other/setting_pay.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/extension.dart';

class SettingPayEditorController extends GetxController
    with StateMixin<String> {
  final ApiProvider apiProvider;
  final _disposable = Completer();
  final draft = <SettingPay>[].obs;

  PrefProvider get prefProvider => apiProvider.prefProvider;

  SettingPayEditorController({
    @required this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
    // 監聽設定值
    prefProvider.settingPayStream().takeUntil(_disposable.future).listen(
      (event) {
        logger.d('[SettingPayEditorController] pref changed');
        draft.assignAll(event);
        super.change('', status: RxStatus.success());
      },
    );
  }

  @override
  void onReady() {
    super.onReady();
    _onRefresh();
  }

  void _onRefresh() {
    try {
      draft.assignAll(prefProvider.settingPay);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<bool> submit() async {
    draft.forEach((element) {
      element.isCustom ??= true;
      element.name ??= '';
      element.sort ??= 0;
      element.status ??= Switcher.Off.index;
    });
    try {
      final ret = await apiProvider.putSettingPay(draft);
      if (true == ret) {
        // update local data
        prefProvider.settingPay = await apiProvider.getSettingPay();
      }
      return ret;
    } catch (e) {
      rethrow;
    }
  }
}
