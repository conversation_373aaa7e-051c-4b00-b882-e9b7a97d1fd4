import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/brands_news.dart';
import 'package:muyipork/app/data/models/other/brands_news_put.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/extension.dart';

class BrandsNewsController extends GetxController with StateMixin<BrandsNews> {
  final ApiProvider apiProvider;
  final _data = Rx<BrandsNewsPut>(null);

  BrandsNewsPut get data => this._data.value;

  BrandsNewsController({
    this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    append(
      () => () {
        return apiProvider.getBrandsNews().then(
          (value) {
            // 規則: 可編輯使用 req，顯示使用 res
            _data.value = value.asBrandsNewsPut();
            return Future.value(value);
          },
        );
      },
    );
  }

  @override
  void onClose() {}

  Future<bool> submit() => apiProvider.putBrandsNews(data);
}
