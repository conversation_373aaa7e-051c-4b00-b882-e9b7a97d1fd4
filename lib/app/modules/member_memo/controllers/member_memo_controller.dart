import 'dart:async';

import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/image_model.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/keys.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/other/member.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/member_provider.dart';
import 'package:muyipork/app/data/models/other/brands_banners_put.dart'
    as Banners;

class MemberMemoController extends GetxController
    with StateMixin<String>, SingleGetTickerProviderMixin {
  final tabs = ['記事本', '相簿'];
  // final tabs = ['記事本'];
  final MemberProvider memberProvider;
  final _disposable = Completer();
  final _id = 0.obs;
  final _member = Rx<Member>(null);
  final index = 0.obs;
  final tiles = <ImageModel>[].obs;
  final imagesToRemove = <ImageModel>[].obs;
  final editing = false.obs;
  final regexp = RegExp(r'^https?://');
  final _memoModified = false.obs;
  final _albumModified = false.obs;

  bool get memoModified => _memoModified.value;
  set memoModified(bool value) => _memoModified.value = value;
  bool get albumModified => _albumModified.value;

  num get id => _id.value;
  set id(num value) => _id.value = value;
  Member get member => _member.value;

  ApiProvider get apiProvider => memberProvider.apiProvider;

  MemberMemoController({
    this.memberProvider,
  });

  @override
  void onInit() {
    super.onInit();
    // skip first init
    // skip 1: clear
    // skip 2: add
    tiles.stream.skip(2).takeUntil(_disposable.future).listen(
      (event) {
        _albumModified.value = true;
      },
    );
    _id.stream
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.Id)) {
      id = num.tryParse(Get.parameters[Keys.Id]) ?? 0;
    }
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> _fetchMemberAlbum() async {
    try {
      final it = await memberProvider.getMemberImages(id);
      final ls = it.toList();
      ls.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
      final images = ls.map((e) {
        return ImageModel(
          id: e.imageId,
          url: e.imageUrl,
        );
      });
      tiles.clear();
      tiles.addAllIf(true, images);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> onRefresh() async {
    try {
      _member.value = await memberProvider.getMember(id);
      await _fetchMemberAlbum();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
      rethrow;
    }
  }

  Future<bool> submitAll() async {
    try {
      var ret = true;
      if (memoModified) {
        if (false == await submit()) {
          ret = false;
        }
      }
      if (albumModified) {
        if (false == await submitImage()) {
          ret = false;
        }
      }
      return ret;
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> submit() async {
    try {
      final ret = await memberProvider.putMemberMemo(member.id, member.memo);
      if (true == ret) {
        _memoModified.value = false;
      }
      return ret;
    } catch (e) {
      rethrow;
    }
  }

  void saveToLocalStorage() {
    memberProvider.saveMemberToLocalStorage(member);
  }

  Future<bool> submitImage() async {
    try {
      editing.value = false;
      var ret = true;
      // 移除不使用的圖片
      // 刪除雖然回傳成功訊息，實際上並未刪除，
      // 可能資料庫批次處理須等久一點 (不確定)
      await _removeImages();
      final data1 = Banners.BrandsBannersPut(images: [..._sortedImages]);
      if (false == await memberProvider.putMemberImages(id, data1)) {
        ret = false;
      }
      // 上傳圖片
      await _applyImages();
      // 設定圖片編號
      final data2 = Banners.BrandsBannersPut(images: [..._sortedImages]);
      // 承上，因刪除造成更新圖片 api 會回傳錯誤
      // 不過會執行刪除指令有正確的結果，所以不丟出例外
      if (false == await memberProvider.putMemberImages(id, data2)) {
        ret = false;
      }
      if (true == ret) {
        _albumModified.value = false;
      }
      return ret;
    } catch (e) {
      logger.e(e);
      _albumModified.value = false;
      return false;
      // 不丟出例外
      // rethrow;
    }
  }

  // 刪除不需要的圖片
  Future<bool> _removeImages() async {
    final images = [...imagesToRemove.where((e) => regexp.hasMatch(e.url))];
    imagesToRemove.clear();
    var ret = true;
    for (var image in images) {
      try {
        if (false == await apiProvider.deleteImage(image.id)) {
          ret = false;
        }
      } catch (e) {
        // 不需丟出例外
        logger.e(e);
      }
    }
    return ret;
  }

  Iterable<Banners.Image> get _sortedImages {
    final it = tiles.where((e) => e.id != null);
    return List.generate(it.length, (index) {
      final element = it.elementAt(index);
      return Banners.Image(
        imageId: element.id,
        sort: index + 1,
      );
    });
  }

  // 上傳圖片
  Future<Iterable<Banners.Image>> _applyImages() async {
    try {
      // 儲存圖片(不是 http 開頭)
      final files = tiles.where((e) => !regexp.hasMatch(e.url));
      if (files.isEmpty) {
        return Future.value(<Banners.Image>[]);
      }
      final list = List<String>.from(files.map((e) => e.url));
      final images = await apiProvider.postImages(list);
      // 上傳完成後賦予 id
      List.generate(files.length, (index) {
        files.elementAt(index).id = images.elementAt(index).id;
      });
      // 排序
      return List.generate(tiles.length, (index) {
        final element = tiles.elementAt(index);
        return Banners.Image(
          imageId: element.id,
          sort: index + 1,
        );
      });
    } catch (e) {
      rethrow;
    }
  }
}
