import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/coupon.dart';
import 'package:muyipork/app/data/models/other/member.dart';
import 'package:muyipork/app/data/models/other/qr_format.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/member_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/keys.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/providers/coupon_provider.dart';
import 'package:muyipork/extension.dart';

class CouponDetailArguments {
  final ValueChanged<QrFormat> checkoutPressed;
  final ValueChanged<QrFormat> continuePressed;

  CouponDetailArguments({
    this.checkoutPressed,
    this.continuePressed,
  });
}

class CouponDetailController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final CouponProvider couponProvider;
  final MemberProvider memberProvider;
  final _id = RxNum(0);
  final _data = Rx<QrFormat>(null);
  final _coupon = Rx<Coupon>(null);
  final _member = Rx<Member>(null);
  final _buttonBar = true.obs;
  final _arguments = Rx<CouponDetailArguments>(null);

  ApiProvider get apiProvider => couponProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  bool get buttonBar => _buttonBar.value;
  CouponDetailArguments get arguments => _arguments.value;
  QrFormat get data => _data.value;
  ProductKind get productKind {
    if (prefProvider.storeType.isDinner) {
      return ProductKind.DinnerApp;
    }
    if (prefProvider.storeType.isRetail) {
      return ProductKind.Retail;
    }
    return ProductKind.Max;
  }

  //num get _id => _data.value.memberCouponId;
  num get memberId => _data.value.memberId;
  num get memberCouponId => _data.value.memberCouponId;

  Coupon get coupon => _coupon.value;
  Member get member => _member.value;

  CouponDetailController({
    @required this.couponProvider,
    @required this.memberProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _id.stream
        .asBroadcastStream()
        .tap((event) => change('', status: RxStatus.loading()))
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
    _data.stream
        .asBroadcastStream()
        .tap((event) => change('', status: RxStatus.loading()))
        .asyncMap((event) => couponProvider.getMemberCoupon(
            event.memberId, event.memberCouponId))
        .takeUntil(_disposable.future)
        .listen(
      // (event) => _id.value = event.couponId,
      (event) {
        _coupon.value = event;
        change('', status: RxStatus.success());
      },
      onError: (error) {
        change('', status: RxStatus.error('$error'));
      },
    );
  }

  @override
  void onReady() {
    super.onReady();
    // data
    if (Get.parameters.containsKey(Keys.Data)) {
      _data.value = QrFormat.fromRawJson(Get.parameters[Keys.Data]);
    }
    // id
    if (Get.parameters.containsKey(Keys.Id)) {
      _id.value = num.tryParse(Get.parameters[Keys.Id]);
    }
    // button bar visibility
    if (Get.parameters.containsKey('actions')) {
      final visible = num.tryParse(Get.parameters['actions']) ?? 0;
      _buttonBar.value = visible.switcher.isOn;
    }
    // 按鈕事件
    if (Get.arguments is CouponDetailArguments) {
      _arguments.value = Get.arguments as CouponDetailArguments;
    }
  }

  Future<void> onRefresh() async {
    try {
      _coupon.value = await couponProvider.getCoupon(_id.value);
      // _member.value = await memberProvider.getMember(memberId);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  @override
  void onClose() {
    _disposable.complete();
  }
}
