import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sunmi_printer/flutter_sunmi_printer.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/data/models/other/local_settings.dart';
import 'package:muyipork/app/data/models/req/orders_orderid_status_put_qry.dart';
import 'package:muyipork/app/data/models/req/orders_orderid_status_put_req.dart';
import 'package:muyipork/app/modules/receipt_sticker_printing/controllers/receipt_sticker_printing_controller.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:muyipork/app/providers/product_provider.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/keys.dart';
import 'package:okshop_esc_pos/okshop_esc_pos.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/other/orders_collection.dart';
import 'package:muyipork/app/data/models/res/orders_get_res.dart';
import 'package:muyipork/app/data/models/res/setting_get_res.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import 'orders_collection_define.dart';

class OrdersController extends GetxController
    with StateMixin<String>, ScrollMixin, SingleGetTickerProviderMixin {
  // 每個分類的定義數值, 已改為在 init 由 checkoutType 來半動態決定
  final tabDefines = {
    StoreType.Dinner: <Map<String, Object>>[], // 餐飲模式
    StoreType.Retail: <Map<String, Object>>[], // 零售模式
  };

  final _tabController = Rx<TabController>(null);
  TabController get tabController => _tabController.value;

  final _disposable = Completer();
  final OrderProvider orderProvider;
  final PrinterProvider printerProvider;
  final ProductProvider productProvider;
  ApiProvider get apiProvider => orderProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;

  // 時段捷徑
  OrdersBusinessHoursMode get prefOrdersBusinessHoursMode =>
      prefProvider.ordersBusinessHoursMode;

  //For getting business hours in setting.
  final _settingGetRes = Rx<SettingGetRes>(null);
  SettingGetRes get settingGetRes => _settingGetRes.value;

  //Tab index = 當前選擇訂單類型
  //Meal
  // 0：內用
  // 1：自取
  // 2：外送
  // 3：外帶
  //Retail
  // 0：自取
  // 1：宅配
  // 2：超取
  // for: 當前選擇的 OrdersCollection
  final _selectedTabType = 0.obs;
  num get selectedTabType => _selectedTabType.value;
  set selectedTabType(num value) => _selectedTabType.value = value;

  // 很重要的資料整理容器跟過濾器, 已改為在 init 由 checkoutType 來半動態決定
  final _ordersFilters = {
    StoreType.Dinner: <OrdersCollection>[], // 餐飲
    StoreType.Retail: <OrdersCollection>[], // 零售
  }.obs;

  // 營業模式捷徑 (餐飲 or 零售)
  // StoreType.Dinner
  // StoreType.Retail
  StoreType get prefStoreType => prefProvider.storeType;

  // 由營業模式(餐飲、零售)取得列表
  Iterable<OrdersCollection> _getFilters(StoreType value) {
    final contains = _ordersFilters.containsKey(value);
    return true == contains ? _ordersFilters[value] : <OrdersCollection>[];
  }

  // 由目前的營業模式取得列表
  Iterable<OrdersCollection> get prefCollections => _getFilters(prefStoreType);

  OrdersCollection getCollection(num tabIndex) {
    final contains = tabIndex >= 0 && tabIndex < prefCollections.length;
    return contains ? prefCollections.elementAt(tabIndex) : null;
  }

  // 當前選擇的 OrdersCollection
  OrdersCollection get activeCollection => getCollection(selectedTabType);
  Iterable<OrderSummary> get cached {
    // TODO: 過濾
    return activeCollection.cached;
  }

  // 當前使用的過濾訂單狀態，由外部傳入
  final Iterable<OrderStatus> filterStatus;
  // 運作模式: 待處理訂單或者已完成訂單兩種，由 View 傳入
  final OrdersViewMode ordersViewMode;
  final receiptEditing = TextEditingController();
  final itemReceiptEditing = TextEditingController();

  OrdersController({
    @required this.orderProvider,
    @required this.printerProvider,
    @required this.ordersViewMode,
    @required this.filterStatus,
    @required this.productProvider,
  });

  @override
  void onInit() async {
    logger.d('[OrdersController] onInit: ordersViewMode($ordersViewMode)');
    super.onInit();
    _initObservable();
  }

  void _initObservable() {
    // observe mem
    final memStream = boxProvider
        .getGsBox(kBoxOrder)
        .watch()
        .debounce(300.milliseconds)
        .asBroadcastStream();
    memStream.takeUntil(_disposable.future).listen((event) {
      final box = boxProvider.getGsBox(kBoxOrder);
      final list = List.from(box.getKeys(), growable: false);
      logger.d(
          '[OrdersController] _initObservable: mem changed, length: ${list.length}');
      _refreshOrdersCollectionsFilter();
    });
    // observe active orders, 重整本時段單及下時段單列表
    orderProvider
        .observeActiveOrders()
        .debounce(300.milliseconds)
        .takeUntil(_disposable.future)
        .listen((event) {
      logger.d(
          '[OrdersController] _initObservable: active changed, 重整本時段單及下時段單列表');
      orderProvider.refreshOrdersWithTime(
        prefStoreType,
        inHours: settingGetRes.data.hours.getNearestFutureHoursInOneWeek(),
        outOfHours: settingGetRes.data.hours.getNearestFutureHoursInOneWeek(),
      );
    });
    // 監聽時段
    final ordersBusinessHoursModeStream =
        prefProvider.ordersBusinessHoursModeStream().asBroadcastStream();
    ordersBusinessHoursModeStream.takeUntil(_disposable.future).listen((event) {
      // 目前頁面變更時段，過濾器設定新時段並重整目前頁面所有頁籤訂單列表
      _refreshOrdersCollectionsFilter();
    });
    // 監聽營業模式(餐飲 或 零售)
    prefProvider
        .storeTypeStream()
        .takeUntil(_disposable.future)
        .listen(_refreshTabs);
    // 監聽 brands type (前結帳 或 後結帳)
    prefProvider
        .brandsTypeStream()
        .asyncMapSample((event) => _initialize())
        .asyncMapSample((event) => _refreshAllOrders())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  // 餐飲、零售切換事件
  void _refreshTabs(StoreType value) {
    // 這邊要檢查 selectedTabType 做調整
    if (selectedTabType >= tabDefines[value].length) {
      selectedTabType = tabDefines[value].length - 1;
    }
    // 更新 tab controller
    _tabController.value = TabController(
      vsync: this,
      length: tabDefines[value].length,
    );
    if (selectedTabType >= 0) {
      tabController.index = selectedTabType;
    }
  }

  //This will re-initialize all tabs relatives and accordance OrdersCollection.
  Future<void> _initialize() async {
    logger.d('[OrdersController] initial: ordersViewMode($ordersViewMode)');
    _settingGetRes.value = prefProvider.setting;
    settingGetRes.ensureWeekDays();

    // 0: 餐飲-前結帳
    // 1: 餐飲-後結帳
    // 2: 零售
    // 3: 餐飲-前結帳+零售
    // 4: 餐飲-後結帳+零售

    // 1. 根據 checkoutType 組成不同的 tabsDefine
    if (ordersViewMode == OrdersViewMode.ActiveOrders) {
      // 處理中訂單
      // print('got checkoutType: ' + settingGetRes.value.data.other.checkoutType.toString());
      switch (settingGetRes.data.other.getBrandsType()) {
        case BrandsType.BeforeDinner:
          tabDefines[StoreType.Dinner] = TAB_DEFINE_MEAL_PRE_CHECKOUT;
          tabDefines[StoreType.Retail] = <Map<String, Object>>[];
          break;
        case BrandsType.AfterDinner:
          tabDefines[StoreType.Dinner] = TAB_DEFINE_MEAL_POST_CHECKOUT;
          tabDefines[StoreType.Retail] = <Map<String, Object>>[];
          break;
        case BrandsType.Retail:
          tabDefines[StoreType.Dinner] = <Map<String, Object>>[];
          tabDefines[StoreType.Retail] = TAB_DEFINE_ACTIVE_RETAIL;
          break;
        case BrandsType.BeforeDinnerWithRetail:
          tabDefines[StoreType.Dinner] = TAB_DEFINE_MEAL_PRE_CHECKOUT;
          tabDefines[StoreType.Retail] = TAB_DEFINE_ACTIVE_RETAIL;
          break;
        case BrandsType.AfterDinnerWithRetail:
          tabDefines[StoreType.Dinner] = TAB_DEFINE_MEAL_POST_CHECKOUT;
          tabDefines[StoreType.Retail] = TAB_DEFINE_ACTIVE_RETAIL;
          break;
        default:
          break;
      }
    } else {
      //訂單歷史紀錄:
      tabDefines[StoreType.Dinner] = TAB_DEFINE_MEAL_ALL;
      tabDefines[StoreType.Retail] = TAB_DEFINE_RETAIL;
    }

    // 2. 根據上方判斷決定好的 Tabs 定義形成 _ordersCollections
    _ordersFilters[StoreType.Dinner]
        .assignAll(_tabDefinesToOrdersCollection(StoreType.Dinner));
    _ordersFilters[StoreType.Retail]
        .assignAll(_tabDefinesToOrdersCollection(StoreType.Retail));

    // 3. 決定 tabController 長度
    //或許我們要解決切換模式會有不同 tab 數量的問題。
    _tabController.value = TabController(
      vsync: this,
      length: tabDefines[prefStoreType].length,
    );

    change('', status: RxStatus.success());
  }

  Iterable<OrdersCollection> _tabDefinesToOrdersCollection(
      StoreType storeType) {
    return tabDefines[storeType].map((e) {
      final ret = OrdersCollection.fromJson(e);
      ret.orderProvider = Get.find();
      ret.orderStatus = (filterStatus ?? []).map((e) => e.index).toList();
      return ret;
    });
  }

  @override
  void onReady() {
    logger.d('[OrdersController] onReady: ordersViewMode($ordersViewMode)');
    super.onReady();
    _initialize().then((value) => _refreshAllOrders());
  }

  @override
  void onClose() {
    logger.d('[OrdersController] onClose: ordersViewMode($ordersViewMode)');
    _disposable.complete();
    super.onClose();
  }

  ///
  /// 這個要由 OrdersView 初始化 states 後呼叫 (一次更新所有東西, 目前這會呼叫7次API,)
  ///
  Future<void> _refreshAllOrders() async {
    logger.d(
        '[OrdersController] refreshAllOrders: ordersViewMode($ordersViewMode)');
    for (var entry in _ordersFilters.entries) {
      final storeType = entry.key;
      for (var collection in entry.value) {
        logger.d(
            '[OrdersController] refreshAllOrders: storeType($storeType), tab(${collection.name})');
        try {
          await _loadMoreOrders(
            page: 1,
            collection: collection,
          );
          await 1.seconds.delay();
        } catch (e) {
          logger.e('[OrdersController] refreshAllOrders: error: $e');
        }
      }
    }
    logger.d('[OrdersController] refreshAllOrders: end');
  }

  ///
  /// 下拉更新目前頁籤的訂單
  ///
  Future<void> onRefresh() async {
    logger.d('[OrdersController] onRefresh');
    try {
      await _loadMoreOrders(
        page: 1,
        collection: activeCollection,
      );
    } catch (e) {
      change('', status: RxStatus.success());
      // change('', status: RxStatus.error(e.toString()));
    } finally {
      // refreshController.refreshCompleted();
    }
  }

  @override
  Future<void> onTopScroll() async {
    // throw UnimplementedError();
  }

  @override
  Future<void> onEndScroll() async {
    final collection = activeCollection;
    logger.d(
        '[OrdersController] onEndScroll: tab(${collection.name}), ordersViewMode($ordersViewMode)');
    if (collection.nnHasMore) {
      try {
        // 續載更多
        await _loadMoreOrders(
          page: collection.nextPage,
          collection: collection,
        );
      } catch (e) {
        // 例外發生: 續載更多不顯示錯誤
        logger.e(e);
      } finally {
        // refreshController.loadComplete();
        logger.d('[OrdersController] onEndScroll: loadComplete');
      }
    } else {
      logger.d('[OrdersController] onEndScroll: no more data');
    }
  }

  ///
  /// 接續取得訂單
  ///
  Future<void> _loadMoreOrders({
    num page = 1,
    OrdersCollection collection,
  }) async {
    logger.d(
        '[OrdersController] _loadMoreOrders: tab(${collection.name}), ordersViewMode($ordersViewMode)');
    collection.hasMore = false;
    collection.page = page;
    final req = collection.toOrderReq(
      status: filterStatus,
      mode: ordersViewMode,
    );
    final ret = await orderProvider.getOrders(req);
    // 判斷最後一頁
    collection.hasMore = ret.length >= kLimit;
    final box = boxProvider.getGsBox(kBoxOrder);
    for (var order in ret) {
      box.write('${order.id}', order.toJson());
    }
  }

  //-- OrdersCollection relative
  // 取得 filter
  //Form a OrdersCollectionFilter by the current conditions.
  OrdersCollectionFilter formOrdersCollectionFilter(
    StoreType storeType,
    OrdersBusinessHoursMode businessHoursMode,
  ) {
    if (storeType.isRetail) {
      businessHoursMode = OrdersBusinessHoursMode.All;
    }
    if (businessHoursMode.isCurrent) {
      // Current business hours.
      return OrdersCollectionFilter(
        sort: ordersViewMode.isCompletedOrders ? 'DESC' : '',
        inHours: settingGetRes.data.hours.getNearestFutureHoursInOneWeek(),
      );
    }
    if (businessHoursMode.isOther) {
      // Other business hours.
      return OrdersCollectionFilter(
        sort: ordersViewMode.isCompletedOrders ? 'DESC' : '',
        notInHours: settingGetRes.data.hours.getNearestFutureHoursInOneWeek(),
      );
    }
    return OrdersCollectionFilter(
      sort: ordersViewMode.isCompletedOrders ? 'DESC' : '',
    );
  }

  // Set new orders collection filters. (for display mode switching)
  // 本機端重新整理，觸發時機:
  // 1. 切換時段
  // 2. mem changed
  void _refreshOrdersCollectionsFilter() {
    final box = boxProvider.getGsBox(kBoxOrder);
    final orders = List.from(box.getValues(), growable: false)
        .map((e) => OrderSummary.fromJson(e))
        .where((order) => order.isMaster);
    logger.d(
        '[OrdersController] refreshOrdersCollectionsFilter: ordersViewMode($ordersViewMode)');
    for (var entry in _ordersFilters.entries) {
      for (var orderCollection in entry.value) {
        // orderCollection.filter = formOrdersCollectionFilter(
        //   orderCollection.eStoreType,
        //   usingBusinessHoursMode,
        // );
        final filter = formOrdersCollectionFilter(
          orderCollection.eStoreType,
          usingBusinessHoursMode,
        );
        orderCollection.setFilter(filter, orders);
      }
    }
    logger.d('[OrdersController] refreshOrdersCollectionsFilter: end');
  }

  // 當前正在使用的時間過濾模式
  OrdersBusinessHoursMode get usingBusinessHoursMode {
    if (ordersViewMode == OrdersViewMode.ActiveOrders) {
      // 處理中訂單用設定內記下的模式
      if (StoreType.Dinner == prefStoreType) {
        return prefOrdersBusinessHoursMode;
      } else {
        // 零售訂單，無時段
        return OrdersBusinessHoursMode.All;
      }
    }
    // 已完成訂單一律不做時間過濾
    return OrdersBusinessHoursMode.All;
  }

  // 當前營業時段模式主題色
  Color get currentThemeColor {
    // logger.d(
    //     '[OrdersController] currentThemeColor: storeType(${prefProvider.storeType}), ordersBusinessHoursMode(${prefProvider.ordersBusinessHoursMode})');
    if (prefProvider.storeType.isDinner) {
      if (prefProvider.ordersBusinessHoursMode.isOther) {
        return OKColor.Secondary;
      }
    }
    return prefProvider.themeColor;
  }

  // appbar title badge
  num get appBusinessModeTitleNotifyNum {
    switch (prefStoreType) {
      case StoreType.Dinner:
        // 目前是 Dinner，回傳 Retail 內當前未完成訂單數
        return orderProvider.activeRetailOrdersCount;
      case StoreType.Retail:
        // 目前是 Retail，回傳 Dinner 內當前未完成訂單數
        return orderProvider.activeDinnerOrdersCount;
      default:
        return 0;
    }
  }

  num get activeOrderBadgeCount {
    switch (prefOrdersBusinessHoursMode) {
      case OrdersBusinessHoursMode.Current:
        return orderProvider.nextOrdersCount;
        break;
      case OrdersBusinessHoursMode.Other:
        return orderProvider.currentOrdersCount;
        break;
      default:
        return 0;
    }
  }

  // TODO: status -> OrderStatus
  Future<void> changeStatus(OrderSummary data, num status) async {
    await FutureProgress(
      future: orderProvider.putOrderStatus(
        data.id,
        OrdersOrderIdStatusPutQry(isPushMsg: Switcher.On.index),
        OrdersOrderIdStatusPutReq(status: status),
      ),
    ).dialog().then((value) {
      data.status = status;
      // 從列表中更新
      // TODO: db 改變時，會觸發更新列表，以下就不需要
      final box = boxProvider.getGsBox(kBoxOrder);
      final order = OrderSummary.fromJson(box.read('${data.id}'));
      if (order != null) {
        order.status = status;
        box.write('${order.id}', order.toJson());
      }
      this.update(null, true);
    });
  }

  // TODO: remove this when detail button is complete
  Future<void> acceptOrder(OrderSummary orderSummary) async {
    // 訂單狀態從 處理中(0) 換成 已確認(1)
    await changeStatus(orderSummary, ORDER_STATUS_CONFIRMED);
    update(null, true);
    // 由設定決定是否列印
    if (orderSummary.orderFromLine) {
      // 取得本地端設定
      prefProvider.localSettings ??= LocalSettings();
      final autoPrinter =
          prefProvider.localSettings.getAutoPrint(prefProvider.storeType);
      // 明細
      if (autoPrinter.receiptEnabled == true) {
        try {
          await _printReceipt(orderSummary.id);
        } catch (e) {
          logger.e(e);
        }
      }
      // 貼紙
      if (autoPrinter.stickerEnabled == true) {
        try {
          await _printSticker(orderSummary.id);
        } catch (e) {
          logger.e(e);
        }
      }
    }
  }

  Future<void> _printSticker(num id) async {
    await Get.toNamed(
      Routes.RECEIPT_STICKER_PRINTING,
      parameters: {Keys.Tag: '$id'},
      arguments: PrintingArgs(
        printingMode: PrintingMode.PrintAllAndLeave,
        printingPageSetting: PrintingPageSetting.StickerOnly,
        orderId: id,
      ),
    );
  }

  Future<void> _printReceipt(num id) async {
    await orderProvider.getOrderDetail(id).then(
      (value) {
        final fee = prefProvider.setting?.data?.other?.fee;
        num servicePercentage = fee?.percent ?? 0;
        if (ServiceFeeType.None == fee?.type?.serviceFeeType) {
          servicePercentage = 0;
        }
        // 餐飲內用才需服務費，其他歸零
        if (!value.data.type.orderType.isDinnerHere) {
          servicePercentage = 0;
        }
        final brandsInfo = prefProvider.brandsInfo;
        final jwt = this.prefProvider.jwt;
        return value.asReceipt(
          storeName: brandsInfo.name,
          userName: jwt.name,
          servicePercentage: servicePercentage,
        );
      },
    ).then(
      (value) {
        _printReceiptItems(value);
      },
    );
  }

  Future<void> _printReceiptItems(Receipt receipt) async {
    await SunmiUtil.printReceiptItems(receipt);
    // 找出印表機
    final printers = printerProvider
        .getPrinterWithCategories([PrintType.receiptItem.value]).where(
            (element) => element.status.switcher.isOn);
    printers
        .where((element) => element.type == PrinterType.net.value)
        .forEach((printer) async {
      // await printer.printReceiptItem(receipt);
      final bytes = await printer.receiptItemTask(receipt);
      printer.pushTask(bytes);
    });
  }

  // TODO: remove this when detail button is complete
  Future<void> rejectOrder(OrderSummary orderSummary) async {
    // 訂單狀態從 處理中(0) 換成 訂單取消(店家) (3)
    // this.onRejectClicked(data);
    final value = await _showRejectDialog();
    switch (value) {
      case ORDER_STATUS_CANCELLED_BY_SHOP:
        await changeStatus(orderSummary, value);
        break;
      case ORDER_STATUS_CANCELLED_BY_CUSTOMER:
        await changeStatus(orderSummary, value);
        break;
      default:
    }
  }

  final rejectActions = [
    ORDER_STATUS[0],
    ORDER_STATUS[3],
    ORDER_STATUS[6],
    // '訂單確認',
    // '店家取消',
    // '顧客取消',
  ];

  Future<num> _showRejectDialog() {
    final selected = RxNum(this.rejectActions[0][kKeyKey]);
    Completer<num> completer = new Completer<num>();
    DialogGeneral(DialogArgs(
      header: Text(
        '訂單狀態',
        style: const TextStyle(
          fontSize: 20,
          color: const Color(0xff333333),
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ),
      mainButtonText: '確認',
      secondaryButtonText: '取消',
      onMainButtonPress: () {
        completer.complete(selected.value);
      },
      content: ObxValue<RxNum>((value) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(this.rejectActions.length, (index) {
            final data = this.rejectActions.elementAt(index);
            return RadioListTile(
              contentPadding: const EdgeInsets.symmetric(
                horizontal: kPadding,
                vertical: 0.0,
              ),
              title: Text(
                data[kKeyValue] ?? '',
                style: const TextStyle(
                  fontSize: 20,
                  color: const Color(0xff6d7278),
                ),
                textAlign: TextAlign.left,
              ),
              value: data[kKeyKey],
              groupValue: selected.value,
              onChanged: (value) {
                selected.value = value;
              },
            );
          }),
        );
      }, selected),
    )).dialog(
      barrierDismissible: false,
    );
    return completer.future;
  }

  Future<num> archive(OrderSummary orderSummary) async {
    // 取得訂單詳情
    final orderRoot =
        await orderProvider.getOrderDetail(orderSummary.id, cacheFirst: true);
    // 轉換成 draft
    final draft = orderRoot.asOrdersPutReq(productProvider: productProvider);
    // 2：訂單完成
    draft.status = OrderStatus.Completed.index;
    // 特殊: 已使用積點的 line 訂單，移除使用積點資訊
    draft.redeemMemberPoints = null;
    // 由總價計算獲得點數
    draft.refreshPointGet(prefProvider);
    // 送出
    return orderProvider.putOrder(orderSummary.id, draft);
  }
}
