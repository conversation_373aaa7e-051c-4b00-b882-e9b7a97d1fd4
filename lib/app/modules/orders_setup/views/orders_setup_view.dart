import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_button.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/custom_tab.dart';
import 'package:muyipork/app/components/custom_tab_bar.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/member_avatar.dart';
import 'package:muyipork/app/components/menu_column.dart';
import 'package:muyipork/app/components/product_info_item.dart';
import 'package:muyipork/app/components/qrcode_scanner.dart';
import 'package:muyipork/app/components/radio_button.dart';
import 'package:muyipork/app/components/rounded_button.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/data/models/other/member.dart';
import 'package:muyipork/app/data/models/other/order_detail.dart';
import 'package:muyipork/app/data/models/other/qr_format.dart';
import 'package:muyipork/app/data/models/req/orders_orderid_status_put_qry.dart';
import 'package:muyipork/app/data/models/req/orders_orderid_status_put_req.dart';
import 'package:muyipork/app/modules/coupon_detail/controllers/coupon_detail_controller.dart';
import 'package:muyipork/app/modules/order_detail/controllers/order_detail_controller.dart';
import 'package:muyipork/app/modules/order_editing/controllers/order_editing_controller.dart';
import 'package:muyipork/app/modules/order_editing/views/order_editing_view.dart';
import 'package:muyipork/app/modules/orders_adjust/controllers/orders_adjust_controller.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';

import '../controllers/orders_setup_controller.dart';

class OrdersSetupView extends GetView<OrdersSetupController> {
  Iterable<Widget> _actions() sync* {
    yield Obx(() {
      return Visibility(
        visible: controller.member != null,
        child: IconButton(
          icon: MemberAvatar(
            imageUrl: controller.member?.avatar,
            size: 30.0,
          ),
          onPressed: _onAvatarPressed,
        ),
      );
    });
    yield GestureDetector(
      onLongPress: controller.devtool.toggle,
      child: _scan(),
    );
    // HACK: test vip toggle
    // yield _vip();
    // HACK: test coupon
    yield _devtool();
  }

  Widget _devtool() {
    return Obx(() {
      return Visibility(
        visible: controller.devtool.value,
        child: IconButton(
          icon: Icon(
            Icons.build,
            color: Colors.white,
          ),
          onPressed: _showCouponDetail,
        ),
      );
    });
  }

  void _showCouponDetail() {
    Get.toNamed(
      Routes.COUPON_DETAIL,
      parameters: <String, String>{
        Keys.Data: QrFormat(
          memberId: 80,
          memberCouponId: 201,
        ).toRawJson(),
        'actions': '0',
      },
      arguments: CouponDetailArguments(
        continuePressed: (value) {
          Get.back<QrFormat>(result: value);
        },
      ),
    ).then(
      (value) {
        if (value is QrFormat) {
          controller.draft.memberId = value.memberId;
          controller.draft.memberCouponId = value.memberCouponId;
          controller.refreshDraft();
        }
      },
    );
  }

  void _onAvatarPressed() {
    final selection = 0.obs;
    DialogGeneral(DialogArgs(
      header: DialogGeneral.titleText('請選擇'),
      mainButtonText: '確認',
      secondaryButtonText: '取消',
      onMainButtonPress: () {
        switch (selection.value) {
          case 0: // 移除會員
            controller.draft.memberId = null;
            controller.refreshDraft();
            // controller.member = null;
            break;
          case 1: // 至會員主頁
            Get.toNamed(
              Routes.MEMBER_DETAIL,
              parameters: <String, String>{
                Keys.Tag: '${controller.member.id}',
                Keys.Data: QrFormat(memberId: controller.member.id).toRawJson(),
              },
            );
            break;
          default:
        }
        // controller.member = value;
      },
      content: Center(
        child: Obx(() {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              RadioButton(
                groupValue: selection.value,
                value: 0,
                titleText: '登出此會員',
                onChanged: selection,
              ),
              RadioButton(
                groupValue: selection.value,
                value: 1,
                titleText: '至會員主頁',
                onChanged: selection,
              ),
            ],
          );
        }),
      ),
    )).dialog();
  }

  Widget _vip() {
    return IconButton(
      iconSize: 36.0,
      icon: SizedBox.fromSize(
        // size: Size.square(28.0),
        child: SvgPicture.asset(
          'assets/images/icon_crown.svg',
          color: Colors.white,
        ),
      ),
      onPressed: controller.toggleVip,
    );
  }

  Widget _scan() {
    return IconButton(
      // iconSize: 36.0,
      icon: SizedBox.fromSize(
        // size: Size.square(28.0),
        child: SvgPicture.asset(
          'assets/images/icon_scan.svg',
          color: Colors.white,
        ),
      ),
      onPressed: _showBarcodePicker,
    );
  }

  void _parse(String value) {
    try {
      final qr = QrFormat.fromRawJson(value);
      if (qr.isMember) {
        // 會員
        _setMemberId(qr.memberId);
      } else if (qr.isOrder) {
        final id = qr.orderId;
        Get.toNamed(
          Routes.ORDER_DETAIL,
          parameters: {
            Keys.Tag: '$id',
            Keys.Id: '$id',
          },
        ).then(
          (value) {
            switch (value) {
              case OrderDetailViewShortCut.AcceptOrder:
                _acceptOrder(id);
                break;
              case OrderDetailViewShortCut.RejectOrder:
                _rejectOrder(id);
                break;
              case OrderDetailViewShortCut.CheckoutOrder:
                _checkOutOrder(id);
                break;
              default:
            }
          },
        );
      } else if (qr.isCoupon || qr.isCoupon1 || qr.isCoupon2) {
        Get.toNamed(
          Routes.COUPON_DETAIL,
          parameters: <String, String>{
            Keys.Data: qr.toRawJson(),
          },
          arguments: CouponDetailArguments(
            // checkoutPressed: (value) {
            //   //
            // },
            continuePressed: (value) {
              Get.back<QrFormat>(result: value);
            },
          ),
        ).then(
          (value) {
            if (value is QrFormat) {
              controller.draft.memberId = qr.memberId;
              controller.draft.memberCouponId = qr.memberCouponId;
              controller.refreshDraft();
            }
          },
        );
      } else {
        DialogGeneral.alert('不是正確的 QR 碼').dialog();
      }
    } catch (e) {
      // 顯示錯誤訊息
      logger.e(e);
      DialogGeneral.alert('不是正確的 QR 碼').dialog();
    }
  }

  void _acceptOrder(num id) {
    _changeStatus(id, OrderStatus.Accepted);
  }

  void _changeStatus(num id, OrderStatus value) {
    FutureProgress(
      future: controller.orderProvider.putOrderStatus(
        id,
        OrdersOrderIdStatusPutQry(isPushMsg: Switcher.On.index),
        OrdersOrderIdStatusPutReq(status: value.index),
      ),
    ).dialog();
  }

  void _rejectOrder(num id) {
    // _changeStatus(id, OrderStatus.Padding);
    // return;
    // 訂單狀態從 處理中(0) 換成 訂單取消(店家) (3)
    // this.onRejectClicked(data);
    _showRejectDialog().then(
      (value) {
        switch (value) {
          case OrderStatus.CancelByApp:
          case OrderStatus.CancelByLine:
            logger.d('[MemberDetailView] ${value.name}');
            _changeStatus(id, value);
            // _changeStatus(id, OrderStatus.Padding);
            break;
          default:
        }
      },
    );
  }

  Future<OrderStatus> _showRejectDialog() {
    final completer = new Completer<OrderStatus>();
    final selected = Rx<OrderStatus>(OrderStatus.Padding);
    DialogGeneral(
      DialogArgs(
        header: DialogGeneral.titleText('訂單狀態'),
        mainButtonText: '確認',
        secondaryButtonText: '取消',
        onMainButtonPress: () {
          completer.complete(selected.value);
        },
        content: Obx(() {
          final ls = <Widget>[];
          ls.addIf(
            true,
            RadioButton<OrderStatus>(
              // contentPadding: kContentPadding,
              // title: DialogGeneral.centerContentText(OrderStatus.Padding.name),
              titleText: OrderStatus.Padding.name,
              value: OrderStatus.Padding,
              groupValue: selected.value,
              onChanged: selected,
            ),
          );
          ls.addIf(
            true,
            RadioButton<OrderStatus>(
              // contentPadding: kContentPadding,
              // title: DialogGeneral.centerContentText(OrderStatus.CancelByApp.name),
              titleText: OrderStatus.CancelByApp.name,
              value: OrderStatus.CancelByApp,
              groupValue: selected.value,
              onChanged: selected,
            ),
          );
          ls.addIf(
            true,
            RadioButton<OrderStatus>(
              // contentPadding: kContentPadding,
              // title: DialogGeneral.centerContentText(OrderStatus.CancelByLine.name),
              titleText: OrderStatus.CancelByLine.name,
              value: OrderStatus.CancelByLine,
              groupValue: selected.value,
              onChanged: selected,
            ),
          );
          return Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: ls,
            ),
          );
        }),
      ),
    ).dialog();
    return completer.future;
  }

  // TODO: 最後要移到 orders view 內處理
  void _checkOutOrder(num id) {
    controller.orderProvider.getOrderDetail(id).then(
      (value) {
        final orderSummary = value.data.asOrderSummary();
        Get.toNamed(
          Routes.ORDERS_SUM_UP,
          arguments: [orderSummary],
          parameters: <String, String>{
            Keys.Tag: '${DateTime.now().millisecondsSinceEpoch}',
          },
        );
      },
      onError: (error) {
        //
      },
    );
  }

  Widget _avatar(final Member member) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        MemberAvatar(
          size: 60.0,
          imageUrl: member?.avatar,
        ),
        const SizedBox(
          width: 16,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              // '(小金)',
              member?.name ?? '',
              // style: Get.textTheme.headline6.copyWith(color: OKColor.Gray4D),
              style: TextStyle(
                fontSize: 16,
                color: OKColor.Gray4D,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.left,
            ),
            Text(
              // '09*****888',
              member?.mobilePhone ?? '',
              // style: Get.textTheme.subtitle1.copyWith(color: OKColor.Gray4D),
              style: TextStyle(
                fontSize: 14,
                color: OKColor.Gray4D,
              ),
              textAlign: TextAlign.left,
            ),
          ],
        ),
      ],
    );
  }

  void _setMemberId(num id) {
    FutureProgress<Member>(
      future: controller.memberProvider.getMember(id),
    ).dialog().then(
      (value) {
        if (value is Member) {
          // 是否加入此會員
          return DialogGeneral(DialogArgs(
            header: DialogGeneral.titleText('是否加入此會員?'),
            mainButtonText: '加入',
            secondaryButtonText: '取消',
            onMainButtonPress: () {
              controller.draft.memberId = value.id;
              controller.refreshDraft();
              // controller.member = value;
            },
            content: Center(
              child: _avatar(value),
            ),
          )).dialog<Button>();
        }
      },
    );
  }

  void _showBarcodePicker() {
    // HACK: test bar code
    // 不正確字串
    // _parse('/KK2X9NQ');
    // _parse('{type: member, member_id: 1}');
    // 訂單
    // _parse('{"type":"order","order_id":3016}');
    // 不存在會員
    // _parse('{"type":"member","member_id":99999}');
    // 存在一般會員
    // _parse('{"type":"member","member_id":11}');
    // 存在VIP會員
    // _parse('{"type":"member","member_id":12}');
    QrCodeScanner().dialog<String>().then(
      (value) {
        if (value != null && value.isNotEmpty) {
          _parse(value);
        }
      },
    );
  }

  Widget _header() {
    return Container(
      decoration: BoxDecoration(
        color: OKColor.Tab,
        boxShadow: [
          BoxShadow(
            color: const Color(0x29000000),
            offset: Offset(0, 0),
            blurRadius: 6,
          ),
        ],
      ),
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        vertical: 4.0,
      ),
      child: CustomTabBar(
        themeColor: controller.prefProvider.themeColor,
        isScrollable: true,
        tabs: [..._tabs()],
        onTap: controller.index,
      ),
    );
  }

  Iterable<Widget> _tabs() {
    final it = controller.cates ?? <Category>[];
    return it.map((e) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: CustomTab(
          titleText: e.name,
          selected: controller.getCategoryProductCount(e) > 0,
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        //編輯既有模式需要: 回上一頁並且回傳編輯結果
        Get.back(result: controller.draft);
        return false;
      },
      child: StreamBuilder<StoreType>(
        initialData: controller.prefProvider.storeType,
        stream: controller.prefProvider.storeTypeStream(),
        builder: (context, snapshot) {
          return StandardPage(
            backgroundColor: controller.prefProvider.themeColor,
            title: _title(),
            actions: _actions().toList(growable: false),
            child: RefreshIndicator(
              onRefresh: controller.onRefresh,
              child: controller.obx(
                (state) {
                  return BottomWidgetPage(
                    child: _main(),
                    bottom: _bottom(),
                  );
                },
                onError: ListWidget.message,
                onEmpty: ListWidget.blank(),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _title() {
    return TextButton(
      onPressed: () {
        // FIXME: 清空購物車確認
        // 點擊切換零售或餐飲
        // controller.toggleProductType();
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            controller.filter?.kind?.productKind?.titleText ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
          ),
          const Visibility(
            // visible: controller.hasMultipleStoreType,
            visible: false,
            child: Icon(
              Icons.expand_more,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _bottom() {
    return Obx(() {
      if (controller.draft == null) {
        return const SizedBox();
      }
      return BottomButton(
        onPressed: _showOrderAdjust,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.shopping_cart_outlined,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Text(
              // '小計 $0(0) + $0(券)',
              controller.displayTotal ?? '',
              style: const TextStyle(
                fontSize: 15,
                color: Colors.white,
              ),
              textAlign: TextAlign.left,
            ),
          ],
        ),
      );
    });
  }

  Future<void> _showOrderAdjust() async {
    //新增模式: 繼續下一頁至調整頁
    if (controller.args.mode == OrdersSetupMode.NewOrder) {
      //Proceed to ORDERS_ADJUST page.
      await Get.toNamed(
        Routes.ORDERS_ADJUST,
        arguments: OrdersAdjustArgs(
          mode: OrdersAdjustMode.CheckOut,
          ordersPostReq: controller.draft,
          kind: controller.args.kind,
        ),
      );
      controller.refreshVip();
    } else {
      //編輯既有模式: 回上一頁並且回傳編輯結果
      Get.back(result: controller.draft);
    }
  }

  Widget _main() {
    return LayoutBuilder(builder: (context, constraints) {
      final hasDetailPage = constraints.maxWidth > 600;
      return hasDetailPage ? _padPage() : _mobilePage();
    });
  }

  ///
  /// 選擇商品規格
  ///
  void _showOrderEditing(ProductInfo data) async {
    final prod = await controller.productProvider
        .getProductDetail(data.productId, cacheFirst: true);
    if (prod.nnProductAdditionCategories.isEmpty) {
      final orderItem = OrderItem(
        type: ItemType.Normal.index,
        productId: data.productId,
        productName: data.title,
        quantity: 1,
        productSpec1Ids: <num>[],
        productCategoryIds: <num>[],
        productSpec1: '',
        finalPrice: controller.vip ? data.vipPrice : data.price,
      );
      // 直接加入購物車
      controller.addNewOrderItem(orderItem);
      // 加入物件時重新整理價格
      controller.refreshVip();
    } else {
      // 改用 sheet 呈現
      final value = await OrderEditingView(
        arguments: OrderEditingArgs(
          kind: controller.args.kind,
          newProductId: data.productId,
          vip: controller.vip,
        ),
      ).sheet<OrderItem>();
      if (value is OrderItem) {
        // print('Got addNewOrderItem! Try add this product to modal...');
        controller.addNewOrderItem(value);
        // 加入物件時重新整理價格
        controller.refreshVip();
      }
    }
  }

  Widget _productInfoItem(ProductInfo data, {bool large}) {
    return ProductInfoItem(
      themeColor: controller.prefProvider.themeColor,
      title: data.title,
      summary: data.summary,
      price: data.price,
      vip: controller.vip,
      vipPrice: data.vipPrice,
      vipOnly: data.isVip?.switcher,
      large: large,
      selectCount: controller.getProductCount(data.productId),
      onTap: () => _showOrderEditing(data),
    );
  }

  Widget _page() {
    final ls = controller.productWithCurrentCate();
    return ListView.builder(
      padding: EdgeInsets.only(
        bottom: kBottomButtonPadding,
      ),
      physics: const AlwaysScrollableScrollPhysics(),
      controller: controller.scroll,
      itemCount: ls.length + controller.more,
      itemBuilder: (context, index) {
        if (index < ls.length) {
          final data = ls.elementAt(index);
          // 菜單品項
          return _productInfoItem(data);
        }
        logger.d('[OrdersSetupView] bottomProgressing');
        controller.onEndScroll();
        return ListWidget.bottomProgressing();
      },
      // separatorBuilder: (context, index) {
      //   return Divider();
      // },
    );
  }

  // FIXME:
  Widget _mobilePage() {
    final it = controller.cates ?? <Category>[];
    return DefaultTabController(
      length: it.length,
      child: Obx(() {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _header(),
            Expanded(
              child: _page(),
            ),
          ],
        );
      }),
    );
  }

  // FIXME:
  Widget _padPage() {
    final it = controller.cates ?? <Category>[];
    return _PadBg(
      child: ListView.separated(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.only(
          bottom: kBottomButtonPadding,
        ),
        scrollDirection: Axis.horizontal,
        itemCount: it.length,
        itemBuilder: (context, index) {
          final data = it.elementAt(index);
          return SizedBox(
            width: 375.0,
            child: Obx(() {
              return MenuColumn(
                header: MenuHeader(
                  titleText: data.name,
                  indicator: controller.getCategoryProductCount(data) > 0,
                ),
                child: getProductCards(controller.productWithCate(data)),
              );
            }),
          );
        },
        separatorBuilder: (context, index) => const SizedBox(width: 20.0),
      ),
    );
  }

  Widget getProductCards(Iterable<ProductInfo> it) {
    return ListView.builder(
      physics: const AlwaysScrollableScrollPhysics(),
      // padding: EdgeInsets.only(
      //   bottom: kBottomButtonPadding,
      // ),
      itemCount: it.length + 1,
      itemBuilder: (context, index) {
        if (index < it.length) {
          final data = it.elementAt(index);
          return ColoredBox(
            color: Colors.white,
            child: _productInfoItem(data, large: true),
          );
        }
        return MenuBottom();
      },
      // separatorBuilder: (context, index) {
      //   return SettingsWidget.divider();
      // },
    );
  }
}

class _PadBg extends StatelessWidget {
  final Widget child;

  const _PadBg({
    Key key,
    this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Column(
          children: [
            ColoredBox(
              color: kColorPrimary,
              child: SizedBox.fromSize(
                size: Size.fromHeight(90.0),
              ),
            ),
            Expanded(
              child: ColoredBox(
                color: kColorBackground,
                child: SizedBox.expand(),
              ),
            )
          ],
        ),
        child ?? SizedBox.shrink(),
      ],
    );
  }
}

extension _ProductKind on ProductKind {
  String get titleText {
    switch (this) {
      case ProductKind.DinnerApp:
      case ProductKind.DinnerLine:
        return '點餐';
      case ProductKind.Retail:
        return '選擇商品';
      default:
        return '';
    }
  }
}
