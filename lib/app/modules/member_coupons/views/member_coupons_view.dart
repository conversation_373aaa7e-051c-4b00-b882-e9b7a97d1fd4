import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/coupon_item.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/data/models/other/qr_format.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/keys.dart';

import '../controllers/member_coupons_controller.dart';

class MemberCouponsView extends GetView<MemberCouponsController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: controller.title,
      child: RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: _main(),
      ),
    );
  }

  Widget _main() {
    return controller.obx(
      (state) => Obx(() => _list()),
      onError: ListWidget.message,
      onEmpty: ListWidget.message('沒有資料'),
    );
  }

  Widget _list() {
    final data = controller.data;
    return ListView.separated(
      padding: EdgeInsets.only(
        top: 34,
        bottom: kPadding,
      ),
      physics: const AlwaysScrollableScrollPhysics(),
      controller: controller.scroll,
      itemCount: data.length + controller.more,
      itemBuilder: (context, index) {
        if (index < data.length) {
          final element = data.elementAt(index);
          return Center(
            child: CouponItem(
              data: element,
              onPressed: () {
                Get.toNamed(
                  Routes.COUPON_DETAIL,
                  parameters: <String, String>{
                    // Keys.Id: '${element.couponId}',
                    Keys.Data: QrFormat(
                      memberId: controller.id,
                      memberCouponId: element.id,
                    ).toRawJson(),
                    // 線上優惠券關閉操作按鈕
                    // 'actions': element.orderSource.isApp ? '1' : '0',
                  },
                );
              },
            ),
          );
        }
        controller.onEndScroll();
        return ListWidget.bottomProgressing();
      },
      separatorBuilder: (context, index) {
        return SizedBox(height: 16);
      },
    );
    // child: StreamBuilder<Iterable<Coupon>>(
    //   stream: controller.stream,
    //   builder: (context, snapshot) {
    //     if (!snapshot.hasData) {
    //       return Center(child: CircularProgressIndicator());
    //     }
    //     final data = snapshot.data;
    //     return ListView.separated(
    //       padding: EdgeInsets.only(
    //         top: 34,
    //         bottom: kPadding,
    //       ),
    //       physics: const AlwaysScrollableScrollPhysics(),
    //       controller: controller.scroll,
    //       itemCount: data.length + controller.more,
    //       itemBuilder: (context, index) {
    //         if (index < data.length) {
    //           final element = data.elementAt(index);
    //           return Center(
    //             child: Ticket(
    //               data: element,
    //               onPressed: () {
    //                 Get.toNamed(
    //                   Routes.COUPON_DETAIL,
    //                   parameters: <String, String>{
    //                     Keys.Data: QrFormat(
    //                       memberId: controller.id,
    //                       memberCouponId: element.couponId,
    //                     ).toRawJson(),
    //                   },
    //                 );
    //               },
    //             ),
    //           );
    //         }
    //         controller.onEndScroll();
    //         return ListWidget.bottomProgressing();
    //       },
    //       separatorBuilder: (context, index) {
    //         return SizedBox(height: 16);
    //       },
    //     );
    //   },
    // ),
  }
}
