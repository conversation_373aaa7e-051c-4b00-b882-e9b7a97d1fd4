import 'dart:async';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/order_detail.dart';
import 'package:muyipork/app/data/models/req/products_post_req.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/product_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

enum OrderEditingMode {
  AddNew, //正在編輯一個新的
  EditingExist, //正在編輯一個已經存在的
  None, //尚未初始化
}

class OrderEditingArgs {
  OrderEditingArgs({
    @required this.kind,
    this.newProductId,
    this.existOrderItem,
    this.vip,
  });
  //必填，有可能用店內，或者零售 kind
  final int kind;
  //以下二選一
  final int newProductId;
  final OrderItem existOrderItem;
  final bool vip;
}

class OrderEditingController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final _vip = false.obs;
  final ProductProvider productProvider;
  ApiProvider get apiProvider => productProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  bool get vip => _vip.value;

  //方便判斷用的編輯模式變數(介面動態差異看這個變數決定)
  final orderEditingMode = OrderEditingMode.None.obs;
  //主要編輯 Model, 變數傳入或者就初始化做一個新的出來編輯。
  final _orderItem = OrderItem(
    type: ItemType.Normal.index,
    quantity: 1,
    productSpec1Ids: <num>[],
    productCategoryIds: <num>[],
  ).obs;
  OrderItem get orderItem => _orderItem.value;
  final _productId = RxNum(0);
  num get productId => _productId.value;

  //以下為參考用 Modal, 主要用於顯示編輯介面
  //商品資訊
  final Rx<ProductsPostReq> _productsPostReq = Rx<ProductsPostReq>(null);
  ProductsPostReq get productsPostReq => _productsPostReq.value;
  // final isFetchingProduct = false.obs;
  //商品所屬附加品列表 (跟上面是成對使用的，按照 additionCategories 順序)
  // final additionProductSets = <AdditionProductsGetRes>[].obs;
  final additionProductSets = <Iterable<AdditionProduct>>[].obs;

  OrderEditingArgs args;

  OrderEditingController({
    @required this.productProvider,
    this.args,
  });

  @override
  void onInit() async {
    super.onInit();
    //判斷傳入值來決定模式，有兩種，新增或者編輯現有
    args ??= Get.arguments;
    if (args.newProductId != null) {
      //視為新增訂單
      orderEditingMode.value = OrderEditingMode.AddNew;
      _productId.value = args.newProductId;
      //先直接建立空資料，等待 productsPostReq 回來以後填上名稱。
      orderItem.productId = productId;
      _orderItem.value = OrderItem(
        type: ItemType.Normal.index,
        productId: productId,
      );
    } else if (args.existOrderItem != null) {
      //編輯訂單
      orderEditingMode.value = OrderEditingMode.EditingExist;
      _orderItem.value = args.existOrderItem;
      _productId.value = orderItem.productId;
    } else {
      //???? WTF?
      logger.e(
          'OOps! OrderEditingController Cannot get a valid initialize value!');
    }
    _vip.value = args.vip;
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  //嘗試取得某 index 的AdditionProduct清單, 有可能回傳null
  Iterable<AdditionProduct> getAdditionProductSet(int index) {
    if (index >= 0 && index < additionProductSets.length) {
      return additionProductSets.elementAt(index);
    }
    return Iterable<AdditionProduct>.empty();
  }

  //檢查是否某個附加商品已經被選取
  bool isAdditionProductSelected(int additionProductId) {
    return orderItem?.isAdditionProductSelected(additionProductId) ?? false;
  }

  //嘗試加入/移除一個附加商品
  //這邊邏輯比較特殊，要做單選判斷，如果是單選擇要把已有的分類內產品都給先取消掉
  bool setAdditionProduct(
      AdditionProduct additionProduct,
      bool b,
      bool isMultiSelection,
      Iterable<AdditionProduct> categoryAdditionProducts) {
    if (orderItem != null) {
      if (orderItem.setAdditionProduct(
          additionProduct, b, isMultiSelection, categoryAdditionProducts)) {
        _orderItem.refresh();
        return true;
      }
    }
    return false;
  }

  //檢查是否所有必選的分類都有選了東西，如果沒有的話會跳錯誤提示
  bool checkIfAllRequiredCategoryHasProduct() {
    if (orderItem != null && productsPostReq != null) {
      bool hasUnselectedRequireAdditionProduct = false;
      //Loop through all categories.
      for (var i = 0; i < productsPostReq.additionCategories.length; ++i) {
        //Check this category?
        final additionCategorySetting = productsPostReq.additionCategories[i];
        //是必選才做檢查
        // print('additionCategories[' + i.toString() + '] required [' + additionCategorySetting.required.toString() + ']');
        if (additionCategorySetting.required == 1) {
          final additionProductsGetRes = getAdditionProductSet(i);
          if (additionProductsGetRes != null) {
            //是否有選擇任何一個 addition product?
            if (!orderItem.hasOneAdditionProduct(
                additionProductsGetRes.map((e) => e.id).toList())) {
              //顯示錯誤提示。
              throw '必選分類不可留空\n(${additionCategorySetting.title})';
            }
          } else {
            //Oops! Data not match! Check failed!
            return false;
          }
        }
      }
      return !hasUnselectedRequireAdditionProduct;
    }
    return false;
  }

  //檢查是否所有複選選項選擇數字都是合法，遇到不合法就跳提示
  bool checkIfAllMultipleCategoryHasEnoughSelection() {
    if (orderItem != null && productsPostReq != null) {
      var allCorrect = true;
      //Loop through all categories.
      for (var i = 0; i < productsPostReq.additionCategories.length; ++i) {
        //Check this category?
        final additionCategorySetting = productsPostReq.additionCategories[i];
        //是複選才做檢查
        if (additionCategorySetting.option == 1) {
          final additionProductsGetRes = getAdditionProductSet(i);
          if (additionProductsGetRes != null) {
            //選擇了多少數量?
            int selectedQuantity = orderItem.hasAdditionProductQuantity(
                additionProductsGetRes.map((e) => e.id).toList());
            if (selectedQuantity > additionCategorySetting.optionMax ||
                selectedQuantity < additionCategorySetting.optionMin) {
              allCorrect = false;
              //顯示錯誤提示。
              throw '複選分類選擇數量錯誤！\n(' +
                  additionCategorySetting.title +
                  ') (' +
                  additionCategorySetting.optionMin.toString() +
                  '~' +
                  additionCategorySetting.optionMax.toString() +
                  ') 項';
            }
          } else {
            //Oops! Data not match! Check failed!
            return false;
          }
        }
      }
      return allCorrect;
    }

    return false;
  }

  //把 OrderItem 做總結整理，填上該填的東西
  void finalizeOrderItem() {
    if (orderItem != null && productsPostReq != null) {
      orderItem.productName = productsPostReq.title;
      //Spec String
      orderItem.fillSpec();
      //Final price
      orderItem.calFinalPrice(productsPostReq.price);
    }
  }

  Future<void> onRefresh() async {
    try {
      // 為了讓 sheet 呈現完成做的 delay
      await 200.milliseconds.delay();
      final productSingle =
          await productProvider.getProductDetail(productId, cacheFirst: true);
      _productsPostReq.value = productSingle.generateAReqModal(0);
      for (var item in productsPostReq.additionCategories) {
        final it = productProvider.getAdditionProductsFromLocalStorage(
          args.kind.productKind,
          additionCategoryId: item.additionCategoryId,
        );
        additionProductSets.add(it);
      }
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }
}
