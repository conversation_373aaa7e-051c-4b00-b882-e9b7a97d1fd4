import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_sunmi_printer/flutter_sunmi_printer.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/local_settings.dart';
import 'package:muyipork/app/data/models/req/orders_orderid_status_put_qry.dart';
import 'package:muyipork/app/data/models/req/orders_orderid_status_put_req.dart';
import 'package:muyipork/app/modules/receipt_sticker_printing/controllers/receipt_sticker_printing_controller.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:okshop_esc_pos/okshop_esc_pos.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/other/pagination.dart';
import 'package:muyipork/app/data/models/res/orders_get_res.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';

class OrderListController extends GetxController
    with StateMixin<String>, ScrollMixin {
  final _disposable = Completer();
  final OrderProvider orderProvider;
  final PrinterProvider printerProvider;
  ApiProvider get apiProvider => orderProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  final _filter = Rx<OrderReq>(null);
  OrderReq get filter => _filter.value;
  final data = <OrderSummary>[].obs;
  final _hasMore = false.obs;
  bool get hasMore => _hasMore.value;
  final _title = '訂單列表'.obs;
  String get title => _title.value;

  OrderListController({
    @required this.orderProvider,
    @required this.printerProvider,
  });

  @override
  void onInit() {
    super.onInit();
    // 監聽 mem，有變動時，同步到 cache
    final box = boxProvider.getGsBox(kBoxOrderCache);
    box.erase();
    box
        .watch()
        .debounce(300.milliseconds)
        .takeUntil(_disposable.future)
        .listen((event) {
      // logger.d('[OrderListController] mem changed, key($event)');
      final it = List.from(box.getValues(), growable: false)
          .map((e) => OrderSummary.fromJson(e))
          .where((order) {
        // 不顯示子訂單
        if (order.isSlave) return false;
        return true;
      });
      final list = it.toList(growable: false);
      // logger.d('[OrderListController] mem changed, length(${list.length})');
      // sort by id
      list.sort((a, b) => (b.id ?? 0).compareTo(a.id ?? 0));
      data.clear();
      data.addAll(list);
      _refreshPage();
    });
    _filter.stream
        .debounce(700.milliseconds)
        .tap((event) => change('', status: RxStatus.loading()))
        .asyncMapSample((e) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
    final parameters = Get.parameters ?? <String, String>{};
    if (parameters.containsKey(Keys.Title)) {
      _title.value = parameters[Keys.Title];
    }
  }

  @override
  void onReady() {
    super.onReady();
    final parameters = Get.parameters ?? <String, String>{};
    if (parameters.containsKey(Keys.Data)) {
      _filter.value = OrderReq.fromRawJson(parameters[Keys.Data]);
    }
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    try {
      await _loadOrders(1);
      await 500.milliseconds.delay();
      _refreshPage();
    } catch (e) {
      // 首讀可顯示錯誤頁面
      change('', status: RxStatus.error('$e'));
    }
  }

  @override
  Future<void> onEndScroll() async {
    if (hasMore == true) {
      try {
        await _loadOrders(filter.nextPage);
      } catch (e) {
        logger.e('[OrderListController] onEndScroll', e);
      }
    }
  }

  Future<void> _loadOrders(num page) async {
    _hasMore.value = false;
    filter.page = page;
    filter.limit = kLimit;
    final stream = orderProvider.getQueryStream(filter);
    final list = await stream.toList();
    _hasMore.value = list.length >= filter.nnLimit;
    // save to mem
    final box = boxProvider.getGsBox(kBoxOrderCache);
    if (page == 1) {
      box.erase();
    }
    for (var order in list) {
      order.member = order.customer.target;
      box.write('${order.id}', order.toJson());
    }
  }

  @override
  Future<void> onTopScroll() async {
    // TODO: implement onTopScroll
    // throw UnimplementedError();
  }

  void refreshFilter() {
    logger.d('[OrderListController] refreshFilter');
    _filter.refresh();
  }

  void _refreshPage() {
    final status = data.isEmpty ? RxStatus.empty() : RxStatus.success();
    change('', status: status);
  }

  Future<void> changeStatus(OrderSummary data, num status) async {
    final orderId = await orderProvider.putOrderStatus(
      data.id,
      OrdersOrderIdStatusPutQry(isPushMsg: Switcher.On.index),
      OrdersOrderIdStatusPutReq(status: status),
    );
    if (orderId is num && orderId > 0) {
      data.status = status;
      update(null, true);
    }
  }

  Future<void> acceptOrder(OrderSummary orderSummary) async {
    // 訂單狀態從 處理中(0) 換成 已確認(1)
    // this.onAcceptClicked(data);
    changeStatus(orderSummary, ORDER_STATUS_CONFIRMED);
    // controller.update(null, true);
    // 如果是 Line 訂單則直接列印
    // TODO: 由設定決定是否列印
    if (orderSummary.orderFromLine) {
      // 取得本地端設定
      prefProvider.localSettings ??= LocalSettings();
      final autoPrinter =
          prefProvider.localSettings.getAutoPrint(prefProvider.storeType);
      // 明細
      if (autoPrinter.receiptEnabled == true) {
        try {
          await _printReceipt(orderSummary.id);
        } catch (e) {
          logger.e(e);
        }
      }
      // 貼紙
      if (autoPrinter.stickerEnabled == true) {
        try {
          await _printSticker(orderSummary.id);
        } catch (e) {
          logger.e(e);
        }
      }
    }
  }

  Future<void> _printSticker(num id) async {
    await Get.toNamed(
      Routes.RECEIPT_STICKER_PRINTING,
      parameters: {Keys.Tag: '$id'},
      arguments: PrintingArgs(
        printingMode: PrintingMode.PrintAllAndLeave,
        printingPageSetting: PrintingPageSetting.StickerOnly,
        orderId: id,
      ),
    );
  }

  Future<void> _printReceipt(num id) async {
    await orderProvider.getOrderDetail(id).then(
      (value) {
        final fee = prefProvider.setting?.data?.other?.fee;
        num servicePercentage = fee?.percent ?? 0;
        if (ServiceFeeType.None == fee?.type?.serviceFeeType) {
          servicePercentage = 0;
        }
        // 餐飲內用才需服務費，其他歸零
        if (!value.data.type.orderType.isDinnerHere) {
          servicePercentage = 0;
        }
        final brandsInfo = prefProvider.brandsInfo;
        final jwt = prefProvider.jwt;
        return value.asReceipt(
          storeName: brandsInfo.name,
          userName: jwt.name,
          servicePercentage: servicePercentage,
        );
      },
    ).then(
      (value) {
        _printReceiptItems(value);
      },
    );
  }

  Future<void> _printReceiptItems(Receipt receipt) async {
    await SunmiUtil.printReceiptItems(receipt);
    // 找出印表機
    final printers = printerProvider
        .getPrinterWithCategories([PrintType.receiptItem.value]).where(
            (element) => element.status.switcher.isOn);
    printers
        .where((element) => element.type == PrinterType.net.value)
        .forEach((printer) async {
      // await printer.printReceiptItem(receipt);
      final bytes = await printer.receiptItemTask(receipt);
      printer.pushTask(bytes);
    });
  }
}
