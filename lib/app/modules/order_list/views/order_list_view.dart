import 'dart:async';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/order_list_item.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/data/models/res/orders_get_res.dart';
import 'package:muyipork/app/modules/orders_selector/controllers/orders_selector_controller.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/order_list_controller.dart';

class OrderListView extends GetView<OrderListController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      // titleText: '搜尋結果',
      titleText: controller.title,
      child: RefreshIndicator(
        onRefresh: controller.onRefresh,
        child: controller.obx(
          (state) => _list(),
          onError: ListWidget.message,
          onEmpty: ListWidget.message('沒有資料'),
        ),
      ),
    );
  }

  Widget _list() {
    return ListView.separated(
      physics: const AlwaysScrollableScrollPhysics(),
      controller: controller.scroll,
      padding: EdgeInsets.only(
        top: 40,
      ),
      itemCount: controller.data.length + (controller.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < controller.data.length) {
          final data = controller.data.elementAt(index);
          return OrderListItem(
            data: data,
            onDetailPressed: () {
              Get.toNamed(
                Routes.ORDER_DETAIL,
                parameters: <String, String>{
                  Keys.Tag: '${data.id}',
                  Keys.Id: '${data.id}',
                },
              );
            },
            // onAcceptPressed: _onAcceptPressed,
            // onRejectPressed: _onRejectPressed,
            // onCheckoutPressed: _onCheckoutPressed,
            onAcceptPressed: () async {
              controller.acceptOrder(data);
            },
            onRejectPressed: () {
              rejectOrder(data);
            },
            onCheckoutPressed: () {
              checkOutOrder(data);
            },
          );
        }
        controller.onEndScroll();
        return ListWidget.bottomProgressing();
      },
      separatorBuilder: (context, index) {
        return OrderListItem.divider();
      },
    );
  }

  void rejectOrder(OrderSummary orderSummary) {
    // 訂單狀態從 處理中(0) 換成 訂單取消(店家) (3)
    // this.onRejectClicked(data);
    this._showRejectDialog().then(
      (value) {
        switch (value) {
          case ORDER_STATUS_CANCELLED_BY_SHOP:
            controller.changeStatus(orderSummary, value);
            break;
          case ORDER_STATUS_CANCELLED_BY_CUSTOMER:
            controller.changeStatus(orderSummary, value);
            break;
          default:
        }
      },
    );
  }

  void checkOutOrder(OrderSummary orderSummary) async {
    controller.orderProvider.getOrderDetail(orderSummary.id).then(
      (value) {
        if (value != null && value.isCombineAvailable) {
          // 合併結帳
          //傳入整體清單以及過濾條件
          Get.toNamed(
            Routes.ORDERS_SELECTOR,
            parameters: {Keys.Tag: '${orderSummary.id}'},
            arguments: OrdersSelectorArgs(
              filter: OrderSelectorFilter(
                id: orderSummary.id,
                table1Id: orderSummary.table1Id,
                table2Id: orderSummary.table2Id,
                table1Name: orderSummary.table1Name,
                table2Name: orderSummary.table2Name,
                sourceOrKind: orderSummary.source,
              ),
            ),
          );
        } else {
          // 單獨結帳
          Get.toNamed(
            Routes.ORDERS_SUM_UP,
            arguments: [orderSummary],
            parameters: <String, String>{
              Keys.Tag: '${DateTime.now().millisecondsSinceEpoch}',
            },
          );
        }
      },
      onError: (error) {
        //
      },
    );
    // TODO:
    // if (orderSummary.type.orderType.isRetail) {
    //   Get.toNamed(
    //     Routes.ORDERS_SUM_UP,
    //     arguments: [orderSummary],
    //   );
    // } else {
    //   //傳入整體清單以及過濾條件
    //   Get.toNamed(
    //     Routes.ORDERS_SELECTOR,
    //     arguments: OrdersSelectorArgs(
    //       sourceOrders: controller.orders,
    //       filter: OrdersSelectorFilter(
    //         id: orderSummary.id,
    //         table1Id: orderSummary.table1Id,
    //         table2Id: orderSummary.table2Id,
    //         table1Name: orderSummary.table1Name,
    //         table2Name: orderSummary.table2Name,
    //         sourceOrKind: orderSummary.source,
    //       ),
    //     ),
    //   );
    // }
  }

  // TODO: status -> OrderStatus
  // void changeStatus(OrderSummary data, num status) {
  //   FutureProgress(
  //     future: controller.orderProvider.putOrderStatus(
  //       data.id,
  //       OrdersOrderIdStatusPutQry(isPushMsg: Switcher.On.index),
  //       OrdersOrderIdStatusPutReq(status: status),
  //     ),
  //   ).dialog().then((value) {
  //     data.status = status;
  //     controller.update(null, true);
  //   });
  // }

  final rejectActions = [
    ORDER_STATUS[0],
    ORDER_STATUS[3],
    ORDER_STATUS[6],
    // '訂單確認',
    // '店家取消',
    // '顧客取消',
  ];

  Future<num> _showRejectDialog() {
    final selected = RxNum(this.rejectActions[0][kKeyKey]);
    Completer<num> completer = new Completer<num>();
    DialogGeneral(DialogArgs(
      header: Text(
        '訂單狀態',
        style: const TextStyle(
          fontSize: 20,
          color: const Color(0xff333333),
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ),
      mainButtonText: '確認',
      secondaryButtonText: '取消',
      onMainButtonPress: () {
        completer.complete(selected.value);
      },
      content: ObxValue<RxNum>((value) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(this.rejectActions.length, (index) {
            final data = this.rejectActions.elementAt(index);
            return RadioListTile(
              contentPadding: const EdgeInsets.symmetric(
                horizontal: kPadding,
                vertical: 0.0,
              ),
              title: Text(
                data[kKeyValue] ?? '',
                style: const TextStyle(
                  fontSize: 20,
                  color: const Color(0xff6d7278),
                ),
                textAlign: TextAlign.left,
              ),
              value: data[kKeyKey],
              groupValue: selected.value,
              onChanged: (value) {
                selected.value = value;
              },
            );
          }),
        );
      }, selected),
    )).dialog(
      barrierDismissible: false,
    );
    return completer.future;
  }
}
