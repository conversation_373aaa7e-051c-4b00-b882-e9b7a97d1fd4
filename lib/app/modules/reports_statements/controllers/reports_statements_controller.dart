import 'dart:async';
import 'package:flutter_sunmi_printer/flutter_sunmi_printer.dart' as Sunmi;
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:okshop_esc_pos/okshop_esc_pos.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/other/reports_statements.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';

class ReportsStatementsController extends GetxController
    with StateMixin<String> {
  PrinterProvider printerProvider;
  final _disposable = Completer();
  final _dateTime = Rx<DateTime>(null);
  final _storeType = Rx<StoreType>(null);
  final _data = Rx<ReportsStatements>(null);
  final _now = DateTime.now().obs;
  DateTime get now => _now.value;

  ApiProvider get apiProvider => printerProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  Logger get logger => apiProvider.logger;

  ReportsStatements get data => _data.value;

  StoreType get storeType => _storeType.value;

  DateTime get dateTime => _dateTime.value;
  set dateTime(DateTime value) => _dateTime.value = value;

  ReportsStatementsController({
    this.printerProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _dateTime.stream
        .combineLatest(_storeType.stream, (x, y) => '')
        .tap((event) {
          change('', status: RxStatus.loading());
        })
        .asyncMap((event) => _getData())
        .takeUntil(_disposable.future)
        .listen((event) {});
    // 初始時間
    _now.value = DateTime.now();
    dateTime = now.subtract(1.days);
    // 初始商店型態
    if (prefProvider.setting.checkoutType.containsDinner) {
      _storeType.value = StoreType.Dinner;
    } else {
      _storeType.value = StoreType.Retail;
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> _getData() async {
    try {
      _data.value =
          await apiProvider.getReportsStatements(dateTime, _storeType.value);
      _now.value = DateTime.now();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  ///
  /// 重新整理
  ///
  Future<void> onRefresh() async {
    try {
      final res = await apiProvider.getReportsStatementsRefresh(dateTime);
      logger.i(res);
    } finally {
      await _getData();
    }
  }

  void toggleStoreType() {
    if (storeType.isDinner) {
      if (prefProvider.setting.checkoutType.containsRetail) {
        _storeType.value = StoreType.Retail;
      }
    } else {
      if (prefProvider.setting.checkoutType.containsDinner) {
        _storeType.value = StoreType.Dinner;
      }
    }
  }

  bool get hasMultipleStoreType {
    return prefProvider.setting.checkoutType.storeTypeCount > 1;
  }

  Future<bool> print() async {
    try {
      final res = await apiProvider.postReportsStatementsPrint(now);
      logger.i(res);
    } catch (e) {
      logger.e(e);
    }
    final printData = data.asPrintStatements(
      title: '${storeType.name}${dateTime.yMd}',
      date: now.yMdHms,
      storeName: prefProvider.brandsInfo.name,
      user: prefProvider.jwt.name,
    );
    // 商米V2印表機
    await Sunmi.SunmiUtil.printReportsStatements(printData);
    // 找出包含商品統計分類的印表機
    final printers = printerProvider.getPrinterWithCategories(
        [PrintType.reportStatements.value]).where((element) {
      return element.status.switcher.isOn &&
          element.type == PrinterType.net.value;
    });
    // 列印當日營收統計
    for (var printer in printers) {
      await printer.printReportsStatements(printData);
    }
    return true;
  }
}

extension ExtensionReportsStatements on ReportsStatements {
  PrintStatements asPrintStatements({
    String title,
    String date,
    String storeName,
    String user,
  }) {
    return PrintStatements(
      title: title,
      subject: '營收統計',
      date: date,
      storeName: storeName,
      user: user,
      orderAmount: orderAmount,
      orderTax: orderTax,
      orderTotal: orderTotal,
      payment: payment.map((e) => Payment.fromJson(e.toJson())).toList(),
      orderNumberStart: app?.orderNumberStart ?? '-',
      orderNumberEnd: app?.orderNumberEnd ?? '-',
      appOrderQuantity: app?.orderQuantity ?? 0,
      appOrderTotal: app?.orderTotal ?? 0,
      lineOrderQuantity: online?.orderQuantity ?? 0,
      lineOrderTotal: online?.orderTotal ?? 0,
      onSiteDiscount: onSiteDiscount ?? 0,
      extraCharges: extraCharges ?? 0,
      refundOrders:
          refundOrders.map((e) => RefundOrder.fromJson(e.toJson())).toList(),
      refundQuantity: refundQuantity ?? 0,
      refundAmount: refundAmount ?? 0,
      invoiceNumberStart: app?.invoiceNumberStart ?? '-',
      invoiceNumberEnd: app?.invoiceNumberEnd ?? '-',
      invoiceQuantity: invoiceQuantity ?? 0,
      invoiceAmount: invoiceAmount ?? 0,
      invoiceVoidQuantity: invoiceVoidQuantity ?? 0,
      invoiceVoidAmount: invoiceVoidAmount ?? 0,
      invoiceVoidNumber: invoiceVoidNumber,
    );
  }
}
