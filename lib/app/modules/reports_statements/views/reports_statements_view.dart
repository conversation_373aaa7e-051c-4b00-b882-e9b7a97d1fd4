import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/custom_divider.dart';
import 'package:muyipork/app/components/date_banner.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/label_value_text.dart';
import 'package:muyipork/app/components/print_header.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/data/models/other/reports_statements.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/enums.dart';
import 'package:okshop_model/okshop_model.dart';

import '../controllers/reports_statements_controller.dart';

class ReportsStatementsView extends GetView<ReportsStatementsController> {
  // Future<void> _print() async {
  //   final data = controller.data.asPrintStatements(
  //     title: '${controller.storeType.name}${controller.dateTime.yMd}',
  //     date: controller.now.yMdHms,
  //     storeName: controller.prefProvider.brandsInfo.name,
  //     user: controller.prefProvider.jwt.name,
  //   );
  //   await SunmiUtil.printReportsStatements(data);
  // }

  void _onPrintPressed() {
    FutureProgress(future: controller.print()).dialog();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return StandardPage(
        backgroundColor: controller.storeType.backgroundColor,
        actions: _actions().toList(growable: false),
        title: _title(),
        child: BottomWidgetPage(
          // bottom: Container(
          //   decoration: const BoxDecoration(
          //     gradient: const LinearGradient(
          //       begin: const Alignment(0.0, -1.0),
          //       end: const Alignment(0.0, -0.45),
          //       colors: const [
          //         const Color(0x00FFFFFF),
          //         Colors.white,
          //       ],
          //       stops: const [0.0, 1.0],
          //     ),
          //   ),
          //   padding: const EdgeInsets.symmetric(
          //     vertical: 8.0,
          //     horizontal: kPadding,
          //   ),
          //   child: Text(
          //     '營收統計需至少60秒計算系統內所有訂單，點選後請耐心等待',
          //     style: TextStyle(
          //       fontSize: 16,
          //       color: Colors.black,
          //       fontWeight: FontWeight.bold,
          //     ),
          //   ),
          // ),
          child: _main(),
        ),
      );
    });
  }

  Iterable<Widget> _actions() sync* {
    yield TextButton(
      onPressed: _onPrintPressed,
      child: Text(
        '列印',
        style: const TextStyle(
          fontSize: 16,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _title() {
    return TextButton(
      onPressed: controller.toggleStoreType,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            controller.storeType.title ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
          ),
          Visibility(
            visible: controller.hasMultipleStoreType,
            child: Icon(
              Icons.expand_more,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.addIf(
      true,
      DateBanner(
        initialDate: controller.dateTime,
        dateChanged: (value) {
          controller.dateTime = value;
        },
      ),
    );
    children.addIf(
      true,
      Expanded(
        child: controller.obx((state) {
          return RefreshIndicator(
            onRefresh: controller.onRefresh,
            child: Container(
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Colors.white,
              ),
              constraints: BoxConstraints(
                maxWidth: 370,
              ),
              child: _list(),
            ),
          );
        }),
      ),
    );
    children.addIf(false, SizedBox(height: kBottomButtonPadding));
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: children,
    );
  }

  Widget _list() {
    final children = <Widget>[];
    children.add(SizedBox(height: kPadding));
    children.add(Text(
      // '餐飲2021/08/12',
      '${controller.storeType.name}${controller.dateTime.yMd}',
      style: TextStyle(
        fontSize: 16,
        color: Colors.black,
        fontWeight: FontWeight.w700,
      ),
      textAlign: TextAlign.center,
    ));
    children.add(SizedBox(height: 4));
    children.add(Text(
      '營收統計',
      style: TextStyle(
        fontSize: 24,
        color: Colors.black,
        fontWeight: FontWeight.w700,
      ),
      textAlign: TextAlign.center,
    ));
    children.add(SizedBox(height: 8));
    children.add(Row(
      children: [
        Text(
          '列印時間：',
          style: TextStyle(
            fontSize: 18,
            color: Colors.black,
          ),
          textAlign: TextAlign.left,
        ),
        Expanded(
          child: Text(
            // '2020/12/10 00:00:00',
            controller.now.yMdHms,
            style: TextStyle(
              fontSize: 18,
              color: Colors.black,
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    ));
    children.add(CustomDivider.print(height: 20));
    children.add(Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '商店名稱',
          style: TextStyle(
            fontSize: 19,
            color: Colors.black,
          ),
          textHeightBehavior:
              TextHeightBehavior(applyHeightToFirstAscent: false),
          textAlign: TextAlign.left,
        ),
        Expanded(
          child: Text(
            // '好事成雙編織小舖好事成雙編織小舖好事成雙編織小舖小舖好事成雙編織小舖小舖好事成最多三十六個字',
            controller.prefProvider.brandsInfo.name,
            style: TextStyle(
              fontSize: 19,
              color: Colors.black,
              fontWeight: FontWeight.w700,
            ),
            maxLines: 3,
            softWrap: false,
            overflow: TextOverflow.ellipsis,
            textHeightBehavior:
                TextHeightBehavior(applyHeightToFirstAscent: false),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    ));
    children.add(SizedBox(height: 4));
    children.add(Row(
      children: [
        Text(
          '人員',
          style: TextStyle(
            fontSize: 19,
            color: Colors.black,
          ),
          textHeightBehavior:
              TextHeightBehavior(applyHeightToFirstAscent: false),
          textAlign: TextAlign.left,
        ),
        Expanded(
          child: Text(
            // '彌豆子彌豆子彌豆子彌豆子彌豆子彌豆子彌豆子彌豆子彌豆子彌豆子彌豆子',
            controller.prefProvider.jwt.name,
            style: TextStyle(
              fontSize: 19,
              color: Colors.black,
              fontWeight: FontWeight.w700,
            ),
            textHeightBehavior:
                TextHeightBehavior(applyHeightToFirstAscent: false),
            textAlign: TextAlign.right,
            maxLines: 1,
            softWrap: false,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    ));
    children.add(SizedBox(height: 4));
    children.add(PrintHeader('銷售總額'));
    children.add(SizedBox(height: 4));
    children.add(LabelValueText(
      left: TextArgs.left('銷售淨額'),
      right: TextArgs.right('\$${controller.data.orderAmount.decimalStyle}'),
    ));
    children.add(SizedBox(
      height: 4,
    ));
    children.add(LabelValueText(
      left: TextArgs.left('銷售稅金'),
      right: TextArgs.right('\$${controller.data.orderTax.decimalStyle}'),
    ));
    children.add(SizedBox(height: 4));
    children.add(LabelValueText(
      left: TextArgs.left('銷售總額'),
      right: TextArgs.right('\$${controller.data.orderTotal.decimalStyle}'),
    ));
    children.add(SizedBox(
      height: 4,
    ));
    children.add(PrintHeader('各支付總額明細'));
    children.addAll(_payment());
    children.add(SizedBox(height: 4));
    children.add(PrintHeader('銷售訂單（不計算取消與退款）'));
    children.add(SizedBox(height: 4));
    children.add(LabelValueText(
      left: TextArgs.left('起始訂單編號'),
      right: TextArgs.right(controller.data?.app?.orderNumberStart ?? '-'),
    ));
    children.add(LabelValueText(
      left: TextArgs.left('結束訂單編號'),
      right: TextArgs.right(controller.data?.app?.orderNumberEnd ?? '-'),
    ));
    children.add(LabelValueText(
      left: TextArgs.left('門市訂單數'),
      right: TextArgs.right('${controller.data?.app?.orderQuantity ?? 0}'),
    ));
    children.add(LabelValueText(
      left: TextArgs.left('門市收入'),
      right: TextArgs.right(
          '\$${controller.data?.app?.orderTotal?.decimalStyle ?? 0}'),
    ));
    children.add(LabelValueText(
      left: TextArgs.left('LINE訂單數'),
      right: TextArgs.right('${controller.data?.online?.orderQuantity ?? 0}'),
    ));
    children.add(LabelValueText(
      left: TextArgs.left('LINE收入'),
      right: TextArgs.right(
          '\$${controller.data?.online?.orderTotal?.decimalStyle ?? 0}'),
    ));
    children.add(LabelValueText(
      left: TextArgs.left('現場減價'),
      right: TextArgs.right(
          '\$${controller.data?.onSiteDiscount?.decimalStyle ?? 0}'),
    ));
    children.add(LabelValueText(
      left: TextArgs.left('額外費用收入'),
      right: TextArgs.right(
          '\$${controller.data?.extraCharges?.decimalStyle ?? 0}'),
    ));
    children.add(SizedBox(height: 4));
    children.add(PrintHeader('銷售退款'));
    children.add(SizedBox(height: 4));
    children.addAll(_refundOrders());
    children.add(LabelValueText(
      left: TextArgs.right('${controller.data?.refundQuantity ?? 0}'),
      right: TextArgs.right(
          '\$${controller.data?.refundAmount?.decimalStyle ?? 0}'),
    ));
    children.add(LabelValueText(
      left: TextArgs.right('加總數量'),
      right: TextArgs.right('加總金額'),
    ));
    children.add(Row(
      children: [
        Expanded(
          child: CustomDivider.print(),
        ),
        SizedBox(
          width: 12,
        ),
        Expanded(
          child: CustomDivider.print(),
        ),
      ],
    ));
    children.add(SizedBox(height: 4));
    children.add(PrintHeader('發票'));
    children.add(SizedBox(height: 4));
    children.add(LabelValueText(
        left: TextArgs.left('起始發票號'),
        right:
            TextArgs.right(controller.data?.app?.invoiceNumberStart ?? '-')));
    children.add(LabelValueText(
        left: TextArgs.left('結束發票號'),
        right: TextArgs.right(controller.data?.app?.invoiceNumberEnd ?? '-')));
    children.add(LabelValueText(
        left: TextArgs.left('發票張數'),
        right: TextArgs.right('${controller.data?.invoiceQuantity ?? 0}')));
    children.add(LabelValueText(
        left: TextArgs.left('開立發票總金額'),
        right: TextArgs.right(
            '\$${controller.data?.invoiceAmount?.decimalStyle ?? 0}')));
    children.add(LabelValueText(
        left: TextArgs.left('作廢發票張數'),
        right: TextArgs.right('${controller.data?.invoiceVoidQuantity ?? 0}')));
    children.add(LabelValueText(
        left: TextArgs.left('作廢發票總金額'),
        right: TextArgs.right(
            '\$${controller.data?.invoiceVoidAmount?.decimalStyle ?? 0}')));
    children.add(CustomDivider.print(height: 20));
    children.add(LabelValueText(
      left: TextArgs.left('作廢發票'),
    ));
    children.addAll(_invoiceVoidNumber());
    children.add(SizedBox(height: kBottomButtonPadding));
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: children,
    );
  }

  Iterable<Widget> _invoiceVoidNumber() {
    return controller.data.invoiceVoidNumber.fold<List<Widget>>(<Widget>[],
        (previousValue, element) {
      previousValue.add(LabelValueText(
        right: TextArgs.right(element),
      ));
      return previousValue;
    });
  }

  Iterable<Widget> _refundOrders() {
    final list = controller.data.refundOrders ?? [];
    return list.fold<List<Widget>>(<Widget>[], (previousValue, element) {
      previousValue.addAll(_refundOrder(element));
      return previousValue;
    });
  }

  Iterable<Widget> _refundOrder(RefundOrder order) {
    final list = <Widget>[];
    list.add(LabelValueText(
      left: TextArgs.left('訂單編號'),
      right: TextArgs.right(order.orderNumber ?? '-'),
    ));
    list.add(LabelValueText(
      left: TextArgs.left('發票號碼'),
      right: TextArgs.right(order.invoiceNumber ?? '-'),
    ));
    list.add(LabelValueText(
      left: TextArgs.left('訂單金額'),
      right: TextArgs.right('\$${order.orderTotal?.decimalStyle ?? 0}'),
    ));
    list.add(LabelValueText(
      left: TextArgs.left('原支付方式'),
      right: TextArgs.right(order.paymentName ?? '-'),
    ));
    list.add(LabelValueText(
      left: TextArgs.left('退款者'),
      right: TextArgs.right(order.memberName ?? '-'),
    ));
    list.add(LabelValueText(
      left: TextArgs.left('處理者'),
      right: TextArgs.right(order.refundStoreAccountName ?? '-'),
    ));
    list.add(CustomDivider.print(height: 12));
    return list;
  }

  ///
  /// 支付方式
  ///
  Iterable<Widget> _payment() {
    final ls = controller.data.payment.fold<List<Widget>>(
      <Widget>[],
      (previousValue, element) {
        previousValue.add(LabelValueText(
          left: TextArgs.left('${element.channelPayMethodName} 收入'),
          right: TextArgs.right('\$${element.income?.decimalStyle}'),
        ));
        previousValue.add(LabelValueText(
          left: TextArgs.left('${element.channelPayMethodName} 支出'),
          right: TextArgs.right('\$${element.expenses?.decimalStyle}'),
        ));
        return previousValue;
      },
    );
    if (ls.isEmpty) {
      return [
        Text(
          '無資料',
          style: TextStyle(
            fontSize: 19,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          softWrap: false,
          overflow: TextOverflow.ellipsis,
        ),
      ];
    }
    return ls;
  }
}

extension ExtensionStoreType on StoreType {
  String get title {
    switch (this) {
      case StoreType.Dinner:
        return '餐飲營收統計';
      case StoreType.Retail:
        return '零售營收統計';
      default:
        return '';
    }
  }

  Color get backgroundColor {
    switch (this) {
      case StoreType.Dinner:
        return kColorPrimary;
      case StoreType.Retail:
        return kColorRetailMode;
      default:
        return kColorPrimary;
    }
  }
}
