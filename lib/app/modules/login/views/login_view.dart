import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/background.dart';
import 'package:muyipork/app/components/custom_editor.dart';
import 'package:muyipork/app/components/rounded_button.dart';
import 'package:muyipork/app/data/models/req/login_req.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:url_launcher/url_launcher.dart';

import '../controllers/login_controller.dart';

class LoginView extends GetView<LoginController> {
  const LoginView({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light,
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        bottomNavigationBar: Padding(
          padding: const EdgeInsets.symmetric(vertical: 20.0),
          child: Text(
            controller.packageInfo?.displayVersion ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: OKColor.GrayB9,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        body: Background(
          background: SvgPicture.asset(
            'assets/images/bg_login.svg',
            fit: BoxFit.fitWidth,
            width: double.infinity,
            alignment: Alignment.topCenter,
          ),
          child: GestureDetector(
            onTap: () => Get.focusScope.unfocus(),
            child: SizedBox.expand(
              child: Center(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: _children().toList(growable: false),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(height: 32.0);
    yield SizedBox(
      width: 171.dw,
      height: 52.dh,
      child: SvgPicture.asset(
        'assets/images/app_logo.svg',
      ),
    );
    yield const SizedBox(height: 32.0);
    yield _form();
    yield const SizedBox(height: 8);
    // ls.addIf(true, Text('headline1', style: Get.textTheme.headline1));
    // ls.addIf(true, Text('headline2', style: Get.textTheme.headline2));
    // ls.addIf(true, Text('headline3', style: Get.textTheme.headline3));
    // ls.addIf(true, Text('headline4', style: Get.textTheme.headline4));
    // ls.addIf(true, Text('headline5', style: Get.textTheme.headline5));
    // ls.addIf(true, Text('headline6', style: Get.textTheme.headline6));
    // ls.addIf(true, Text('subtitle1', style: Get.textTheme.subtitle1));
    // ls.addIf(true, Text('subtitle2', style: Get.textTheme.subtitle2));
    // ls.addIf(true, Text('bodyText1', style: Get.textTheme.bodyText1));
    // ls.addIf(true, Text('bodyText2', style: Get.textTheme.bodyText2));
  }

  Widget _form() {
    Iterable<Widget> children() sync* {
      yield const SizedBox(height: 24.0);
      yield _code();
      yield const SizedBox(height: 8.0);
      yield StreamBuilder(
        stream: controller.userDefault.watch(key: kKeyRememberMe),
        builder: (context, snapshot) {
          return _rememberMe();
        },
      );
      yield const SizedBox(height: 4.0);
      yield SvgPicture.asset(
        'assets/images/dot_line.svg',
        fit: BoxFit.fitWidth,
      );
      yield const SizedBox(height: 4.0);
      yield _username();
      yield const SizedBox(height: 8.0);
      yield _password();
      yield const SizedBox(height: 16.0);
      yield RoundedButton(
        onPressed: _submit,
        buttonText: '登入',
      ).paddingSymmetric(horizontal: kPadding);
      yield const SizedBox(height: 24.0);
      yield Center(
        child: TextButton.icon(
          icon: SizedBox.fromSize(
            size: Size.square(30),
            child: SvgPicture.asset('assets/images/icon_line.svg'),
          ),
          onPressed: () {
            canLaunch(kOkShopServiceUrl).then((value) {
              if (true == value) {
                launch(kOkShopServiceUrl);
              }
            });
          },
          label: Text(
            GetPlatform.isIOS ? 'OKSHOP客服' : 'OKSHOP客服與購買',
            style: TextStyle(
              fontSize: 16,
              color: OKColor.Primary,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
      yield const SizedBox(height: 28.0);
    }

    return Container(
      width: 300.dw,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(
          Radius.circular(20.0),
        ),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x29000000),
            offset: Offset(0, 0),
            blurRadius: 6.0,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _password() {
    return ListTile(
      contentPadding: kContentPadding,
      title: Obx(() {
        return CustomEditor(
          initialValue: controller.draft.password,
          validator: (value) {
            final password = controller.draft.password;
            if (password == null || password.isEmpty) {
              return '必填項目';
            }
            return null;
          },
          onChanged: (value) => controller.draft.password = value,
          obscureText: controller.obscureText.value,
          labelText: '密碼',
          hintText: '請輸入密碼',
          suffixIcon: IconButton(
            icon: Icon(controller.obscureText.value
                ? Icons.visibility
                : Icons.visibility_off),
            onPressed: controller.obscureText.toggle,
          ),
        );
      }),
      // trailing: IconButton(
      //   icon: Obx(() => Icon(
      //       controller.obscu.value ? Icons.visibility : Icons.visibility_off)),
      //   onPressed: controller.obscu.toggle,
      // ),
    );
  }

  Widget _username() {
    return ListTile(
      contentPadding: kContentPadding,
      title: CustomEditor(
        initialValue: controller.draft.username,
        validator: (value) {
          final username = controller.draft.username;
          if (username == null || username.isEmpty) {
            return '必填項目';
          }
          return null;
        },
        onChanged: (value) => controller.draft.username = value,
        labelText: '帳號',
        hintText: '請輸入帳號',
      ),
    );
  }

  Widget _code() {
    return ListTile(
      contentPadding: kContentPadding,
      title: CustomEditor(
        initialValue: controller.draft.code,
        validator: (value) {
          final code = controller.draft.code;
          if (code == null || code.isEmpty) {
            return '必填項目';
          }
          return null;
        },
        onChanged: (value) => controller.draft.code = value,
        labelColor: kColorPrimary,
        labelText: '商店代號',
        hintText: '請輸入商店代號',
      ),
    );
  }

  Widget _rememberMe() {
    final checked =
        controller.userDefault.get(kKeyRememberMe, defaultValue: false);
    if (!checked) {
      controller.resetRememberMe();
    }
    return TextButton.icon(
      onPressed: () {
        controller.userDefault.put(kKeyRememberMe, !checked);
      },
      icon: SizedBox.fromSize(
        size: const Size.square(20),
        child: Checkbox(
          value: checked,
          onChanged: (value) {
            controller.userDefault.put(kKeyRememberMe, value);
          },
        ),
      ),
      label: const Expanded(
        child: Text(
          '記住商店代號及帳號',
          style: TextStyle(
            fontSize: 14,
            color: OKColor.Gray33,
          ),
          overflow: TextOverflow.ellipsis,
          softWrap: false,
          maxLines: 1,
          textAlign: TextAlign.left,
        ),
      ),
    ).paddingSymmetric(horizontal: 8.0);
  }

  Future<void> _submit() async {
    Get.showLoading();
    try {
      controller.draft.validate();
      final ret = await controller.submit();
      Get.back();
      if (ret != null) {
        if (ret.alreadyLogin == true) {
          // 強制登入確認
          _overriding();
        } else {
          // 登入成功
          controller.onLogin();
        }
      }
    } catch (e) {
      Get.back();
      Get.showAlert(e.toString());
    }
  }

  void _overriding() {
    Get.showConfirm(
      '',
      '該帳號已登入，確定要登入\n會踢掉前一個登入者',
      textConfirm: '登入',
      textCancel: '取消',
      onConfirm: _override,
      onCancel: () {
        // 取消強制登入，清空暫存 token
        controller.prefProvider.token = '';
      },
    );
  }

  Future<void> _override() async {
    Get.showLoading();
    try {
      final res = await controller.apiProvider.getRenew();
      // 隱藏 loading
      Get.back();
      if (res?.token != null && res.token.isNotEmpty) {
        // 登入成功
        controller.onLogin();
      }
    } catch (e) {
      Get.back();
      Get.showAlert(e.toString());
    }
    // try {
    //   final value = await FutureProgress(
    //     future: controller.apiProvider.getRenew(),
    //   ).dialog<LoginRes>();
    //   final token = value?.token;
    //   if (token != null && token.isNotEmpty) {
    //     // 登入成功
    //     controller.onLogin();
    //   }
    // } catch (e) {
    //   DialogGeneral.alert('$e').dialog();
    // }
  }
}

extension _LoginReqX on LoginReq {
  void validate() {
    if (code == null || code.isEmpty) {
      throw '商店代號為必填項目';
    }
    if (username == null || username.isEmpty) {
      throw '帳號為必填項目';
    }
    // 暫時打開密碼為空值
    // if (password == null || password.isEmpty) {
    //   throw '密碼為必填項目';
    // }
  }
}
