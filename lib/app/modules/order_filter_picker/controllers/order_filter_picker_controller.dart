import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/res/tables_get_res.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/keys.dart';
import 'package:okshop_model/okshop_model.dart';

class OrderFilterPickerController extends GetxController
    with StateMixin<String> {
  final ApiProvider apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  final data = <Partition>[].obs;

  List<TableV1> getTableList(num partitionId) {
    final partition = data.firstWhere(
      (element) => element.id == partitionId,
      orElse: () => Partition(),
    );
    return partition?.child ?? <TableV1>[];
  }

  List<TableV1> get currentTableList => getTableList(draft.table1Id);

  void trySelectTheFirstTable() {
    final ls = currentTableList;
    final table = ls.isNotEmpty ? ls.elementAt(0) : TableV1(id: 0);
    draft.table2Id = table.id;
  }

  final _draft = OrderReq().obs;
  OrderReq get draft => _draft.value;
  final name = ''.obs;
  final mobilePhone = ''.obs;

  Function get refreshDraft => _draft.refresh;

  final _titleText = ''.obs;
  String get titleText => _titleText.value;

  OrderFilterPickerController({
    @required this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
    final parameters = Get.parameters ?? <String, String>{};
    if (parameters.containsKey(Keys.Title)) {
      _titleText.value = parameters[Keys.Title];
    }
    if (parameters.containsKey(Keys.Data)) {
      _draft.value = OrderReq.fromRawJson(parameters[Keys.Data]);
    }
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {}

  Future<void> onRefresh() async {
    try {
      TablesGetRes result = await apiProvider.getTables();
      // FIXME: 有問題
      if (result != null && result.hasError()) {
        change('', status: RxStatus.error(result.formattedErrorStr()));
      } else {
        data.clear();
        data.addAll(result.data ?? <Partition>[]);
        change('', status: RxStatus.success());
      }
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }
}
