import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/other/pagination.dart';
import 'package:muyipork/app/data/models/req/products_get_qry.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/product_provider.dart';
import 'package:muyipork/extension.dart';

class ProductSetupArgs {
  //  設定 API 使用類型
  //  0: 店內
  //  1: 線上
  //  2: 零售
  final int kind;
  ProductSetupArgs({
    @required this.kind,
  });
}

class ProductSetupController extends GetxController
    with StateMixin<String>, ScrollMixin {
  final _disposable = Completer();
  final ProductProvider productProvider;
  ApiProvider get apiProvider => productProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;

  final _selectedIndex = RxNum(0);
  num get selectedIndex => _selectedIndex.value;
  set selectedIndex(num value) => _selectedIndex.value = value;

  final _args = Rx<ProductSetupArgs>(null);
  ProductSetupArgs get args => _args.value;

  final products = <ProductInfo>[].obs;
  final _filter = ProductsGetQry().obs;
  final _pagi = Pagination().obs;
  Pagination get pagi => _pagi.value;
  ProductsGetQry get filter => _filter.value;

  num get more => _pagi.value.needToLoadMore ? 1 : 0;

  Iterable<ProductInfo> productWithCurrentCate() {
    final cate = categories.elementAt(selectedIndex);
    return _productWithCate(cate);
  }

  Iterable<ProductInfo> _productWithCate(Category cate) {
    return products.where((e) => e.categoryId == cate.id);
  }

  Iterable<Category> get categories {
    return productProvider.getCategoriesFromStorage(filter.kind.productKind);
  }

  Iterable<ProductInfo> get productsV2 {
    final cate = categories.elementAt(selectedIndex);
    return productProvider.getProductsFromStorageWithCategory(cate);
  }

  ProductSetupController({
    this.productProvider,
  });

  @override
  void onInit() async {
    super.onInit();

    _filter.stream
        .asBroadcastStream()
        .tap((event) => change('', status: RxStatus.loading()))
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});

    //存下頁面指定參數
    _args.value = Get.arguments;
    _filter.value.kind = args.kind;
  }

  @override
  void onReady() {
    super.onReady();
    _filter.refresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    try {
      // 取得最新分類列表
      await productProvider.getCategories(filter.kind.productKind);
      filter.page = 1;
      filter.limit ??= 500;
      final value = await productProvider.getProducts(filter);
      products.assignAll(value);
      pagi.currentPage = filter.page;
      // 供不應求，終點
      final endOfPage = value.length < filter.limit;
      pagi.lastPage = endOfPage ? pagi.currentPage : null;
      _refreshPage();
    } catch (e) {
      pagi.setLastPage();
      // 首讀可顯示錯誤頁面
      change('', status: RxStatus.error('$e'));
    }
  }

  @override
  Future<void> onEndScroll() async {
    if (pagi.needToLoadMore) {
      try {
        filter.page = pagi.nextPage;
        filter.limit ??= 500;
        final value = await productProvider.getProducts(filter);
        products.addAll(value);
        pagi.currentPage = filter.page;
        final endOfPage = value.length < filter.limit;
        pagi.lastPage = endOfPage ? pagi.currentPage : null;
      } catch (e) {
        pagi.setLastPage();
        logger.e(e);
      } finally {
        _refreshPage();
      }
    }
  }

  @override
  Future<void> onTopScroll() async {
    // nothing...
  }

  ///
  /// 更新頁面
  ///
  void _refreshPage() {
    // 特殊: cache 有東西，設定成功，觸發 onEndScroll 繼續讀取
    final isEmpty =
        pagi.needToLoadMore ? productProvider.isEmpty : products.isEmpty;
    final status = isEmpty ? RxStatus.empty() : RxStatus.success();
    change('', status: status);
  }

  Future<bool> sold(num productId) async {
    // 先更新本機端，再送 API
    products
        .where((e) => e.productId == productId)
        .forEach((element) => element.isSoldOut = false);
    products.refresh();
    try {
      final ret = await productProvider.sold(productId);
      return ret is num && ret > 0;
    } catch (e) {
      // 失敗則復原本機資料
      products
          .where((e) => e.productId == productId)
          .forEach((element) => element.isSoldOut = true);
      products.refresh();
      rethrow;
    }
  }

  Future<bool> soldOut(num productId) async {
    // 先更新本機端，再送 API
    products
        .where((e) => e.productId == productId)
        .forEach((element) => element.isSoldOut = true);
    products.refresh();
    try {
      final ret = await productProvider.soldOut(productId);
      return ret is num && ret > 0;
    } catch (e) {
      // 失敗則復原本機資料
      products
          .where((e) => e.productId == productId)
          .forEach((element) => element.isSoldOut = false);
      products.refresh();
      rethrow;
    }
  }
}
