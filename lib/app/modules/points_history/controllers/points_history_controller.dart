import 'dart:async';

import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/pagination.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/member_provider.dart';
import 'package:muyipork/keys.dart';

class PointsHistoryController extends GetxController
    with StateMixin<String>, ScrollMixin {
  final _disposable = Completer();
  final MemberProvider memberProvider;
  ApiProvider get apiProvider => memberProvider.apiProvider;

  final _id = RxNum(0);
  num get id => _id.value;
  set id(num value) => _id.value = value;

  final _cached = <num, MemberPoint>{}.obs;
  final data = <MemberPoint>[].obs;

  final _page = 1.obs;
  num get page => _page.value;

  final _hasMore = false.obs;
  bool get hasMore => _hasMore.value;

  final _total = 0.obs;
  num get total => _total.value;

  PointsHistoryController({
    this.memberProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _cached.stream.takeUntil(_disposable.future).listen((event) {
      // logger.d('[PointsHistoryController] _cached changed');
      final list = event.values.toList(growable: false);
      // sort by id
      list.sort((a, b) => (b.id ?? 0).compareTo(a.id ?? 0));
      data.assignAll(list);
      _refreshPage();
    });
    _id.stream
        .asBroadcastStream()
        .asyncMapSample((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.Id)) {
      id = num.tryParse(Get.parameters[Keys.Id]);
    }
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  ///
  /// 重新讀取一定是第一頁，成功後清除目前資料再加入新資料
  ///
  Future<void> onRefresh() async {
    logger.d('[PointsHistoryController] onRefresh: id($id)');
    _hasMore.value = false;
    try {
      _total.value = await memberProvider.getMemberPointTotal(id);
      // 重新整理: 一定是第一頁
      final it = await memberProvider.getMemberPoints(id, 1);
      _hasMore.value = it.length >= kLimit;
      _page.value = 1;
      final entries = it.map((e) => MapEntry(e.id, e));
      _cached.clear();
      _cached.addEntries(entries);
      _refreshPage();
    } catch (e) {
      // 首讀可顯示錯誤頁面
      change('', status: RxStatus.error('$e'));
    }
  }

  void _refreshPage() {
    change('', status: data.isEmpty ? RxStatus.empty() : RxStatus.success());
  }

  @override
  Future<void> onEndScroll() async {
    if (hasMore == true) {
      _hasMore.value = false;
      final nextPage = page + 1;
      logger.d('[PointsHistoryView] onEndScroll: id($id) nextPage:($nextPage)');
      try {
        final it = await memberProvider.getMemberPoints(id, nextPage);
        _page.value = nextPage;
        _hasMore.value = it.length >= kLimit;
        final entries = it.map((e) => MapEntry(e.id, e));
        _cached.addEntries(entries);
      } catch (e) {
        // on end scroll 不顯示錯誤
        // change('', status: RxStatus.error('$error'));
      }
    } else {
      logger.d('[PointsHistoryView] onEndScroll: no more');
    }
  }

  @override
  Future<void> onTopScroll() async {
    logger.d('[PointsHistoryView] onTopScroll');
  }
}
