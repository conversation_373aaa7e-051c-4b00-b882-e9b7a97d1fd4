import 'dart:async';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/setting_pay.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';

class SettingPayAddController extends GetxController with StateMixin<String> {
  final ApiProvider apiProvider;
  final _id = 0.obs;
  // final _disposable = Completer();
  final _draft = <SettingPay>[].obs;
  final _selectedDraft = Rx<SettingPay>(null);

  SettingPay get selectedDraft => _selectedDraft.value;
  num get id => _id.value;

  SettingPayAddController({
    @required this.apiProvider,
  });

  PrefProvider get prefProvider => apiProvider.prefProvider;

  String get titleText {
    if (id != null && id > 0) {
      return '編輯支付方式';
    }
    return '新增支付方式';
  }

  @override
  void onInit() {
    super.onInit();
    _draft.addAll(prefProvider.settingPay);
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.Id)) {
      _id.value = num.tryParse(Get.parameters[Keys.Id]);
    }
    _onRefresh();
  }

  void _onRefresh() {
    try {
      _selectedDraft.value = _draft.firstWhere(
        (element) => element.id == id,
        orElse: () => SettingPay(
          isCustom: true,
          status: Switcher.On.index,
        ),
      );
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  @override
  void onClose() {
    // _disposable.complete();
  }

  Future<bool> submit() async {
    if (selectedDraft.id == null || selectedDraft.id <= 0) {
      // 新增
      selectedDraft.id = 0;
      selectedDraft.payMethodId = 0;
      selectedDraft.isCustom = true;
      selectedDraft.sort = 0;
      _draft.add(selectedDraft);
    }
    try {
      final ret = await apiProvider.putSettingPay(_draft);
      if (true == ret) {
        // update local data
        prefProvider.settingPay = await apiProvider.getSettingPay();
      }
      return ret;
    } catch (e) {
      rethrow;
    }
  }
}
