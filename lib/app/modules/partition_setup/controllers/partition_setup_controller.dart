import 'dart:async';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/req/id_sort.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/table_provider.dart';
import 'package:okshop_model/okshop_model.dart';

class PartitionSetupController extends GetxController with StateMixin<String> {
  final TableProvider tableProvider;
  final data = <Table>[].obs;
  final _req = TableReq(parentId: 0).obs;
  final _disposable = Completer();
  final _deleting = <num>[].obs;
  final _needReload = false.obs;

  TableReq get req => _req.value;
  ApiProvider get apiProvider => tableProvider.apiProvider;

  PartitionSetupController({
    @required this.tableProvider,
  });

  @override
  void onInit() {
    super.onInit();
    // 監聽 table cache
    tableProvider.cached.stream
        .debounce(100.milliseconds)
        .takeUntil(_disposable.future)
        .listen(
      (event) {
        final list = [...event.values];
        list.sort((x, y) => (x.sort ?? 0).compareTo(y.sort ?? 0));
        data.assignAll(list);
      },
    );
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    if (true == _needReload.value) {
      tableProvider.getTables();
    }
    // TODO: remove me when apiProvider.getTables could be removed.
    apiProvider.getTables(updateCache: true);
  }

  Future<void> onRefresh() async {
    try {
      final list = await tableProvider.getTables();
      data.assignAll(list);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  void sort(int srcIndex, int destIndex) {
    final list = [...data];
    if (srcIndex < destIndex) {
      // 排序不超過新增
      if (destIndex <= list.length) {
        final element = list.elementAt(srcIndex);
        list.insert(destIndex, element);
        list.removeAt(srcIndex);
      }
    } else {
      final element = list.removeAt(srcIndex);
      list.insert(destIndex, element);
    }
    for (var i = 0; i < list.length; i++) {
      final element = list.elementAt(i);
      element.sort = i;
    }
    data.assignAll(list);
  }

  Future<bool> addNewTable() async {
    if (req.name == null || req.name.isEmpty) {
      return false;
    }
    try {
      req.sort = data.length;
      final ret = await tableProvider.post(req);
      if (ret != null && ret != 0) {
        data.add(req.asTable(id: ret));
        await _sort();
        _req.value = TableReq(
          parentId: 0,
          sort: data.length,
        );
        // 重新抓
        _needReload.value = true;
        return true;
      }
      return false;
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> _sort() {
    final list = data ?? <Table>[];
    final it = Iterable.generate(list.length, (index) {
      final element = list.elementAt(index);
      return IDSort(id: element.id, sort: index);
    });
    return tableProvider.sort(it);
  }

  void deleting(num id) {
    final index = data.indexWhere((element) => element.id == id);
    if (index >= 0) {
      final table = data.removeAt(index);
      _deleting.add(table.id);
      table.child ??= <Table>[];
      for (var child in table.child) {
        _deleting.add(child.id);
      }
    }
  }

  Future<bool> _delete() async {
    if (_deleting.isEmpty) {
      return true;
    }
    final ids = _deleting.toSet();
    _deleting.clear();
    for (var id in ids) {
      try {
        await tableProvider.delete(id);
      } catch (e) {
        rethrow;
      }
    }
    return true;
  }

  Future<bool> submit() async {
    try {
      // 刪除
      await _delete();
      // 新增 (包含排序)
      if (false == await addNewTable()) {
        // 新增失敗，手動排序
        await _sort();
      }
      _needReload.value = true;
      return true;
    } catch (e) {
      rethrow;
    }
  }
}
