import 'package:flutter/foundation.dart';
import 'package:flutter_sunmi_printer/flutter_sunmi_printer.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/res/order_root.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:screenshot/screenshot.dart';

class OrderReceiptController extends GetxController {
  final _screenshotController = ScreenshotController();
  final ApiProvider apiProvider;
  final _id = ''.obs;
  // final data = Rx<OrdersOrderIdGetRes>(null);
  final data = Value<OrderRoot>(null);

  ScreenshotController get screenshotController {
    return this._screenshotController;
  }

  PrefProvider get prefProvider {
    return this.apiProvider.prefProvider;
  }

  String get storeName {
    final info = this.prefProvider.brandsInfo;
    return info?.name ?? '';
  }

  String get accountName {
    final jwt = this.prefProvider.jwt;
    return jwt.name ?? '';
  }

  OrderReceiptController({
    @required this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(kKeyId)) {
      this._id.value = Get.parameters[kKeyId];
    }
  }

  @override
  void onClose() {}

  Future print() {
    return Future(() {
      return this._screenshotController.capture(
            pixelRatio: 1.0,
            // pixelRatio: 1.25,
          );
    }).then(
      (value) {
        final uri = Uri.dataFromBytes(value);
        return SunmiPrinter.image(uri.data.contentText, align: SunmiAlign.left);
      },
    ).then(
      (value) {
        return SunmiPrinter.emptyLines(3);
      },
    );
  }
}
