import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/data/models/res/setting_put_res.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/setting_service_controller.dart';

class SettingServiceView extends GetView<SettingServiceController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '服務費設定',
      child: controller.obx((state) {
        return BottomWidgetPage.save(
          onPressed: _submit,
          child: _main(),
        );
      }),
    );
  }

  Widget _main() {
    final children = <Widget>[];
    children.add(
      Center(
        child: Text(
          '下列功能請2擇1',
          style: TextStyle(
            fontSize: 16,
            color: OKColor.Primary,
            fontWeight: FontWeight.w700,
          ),
          textAlign: TextAlign.center,
        ).paddingSymmetric(vertical: 12),
      ),
    );
    children.add(Expanded(child: _slave()));
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  Widget _slave() {
    final children = <Widget>[];
    children.add(SettingsWidget.title(titleText: '服務費以原價計算'));
    children.add(Obx(() => _sector1()));
    children.add(SettingsWidget.title(titleText: '服務費以折扣後價格計算'));
    children.add(Obx(() => _sector2()));
    return ListView(
      padding: EdgeInsets.only(
        // top: 20,
        bottom: kBottomButtonPadding,
      ),
      physics: AlwaysScrollableScrollPhysics(),
      children: children,
    );
  }

  Widget _sector2() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用',
        value: controller.draft.type.serviceFeeType.isDiscount,
        onChanged: (value) {
          final type = value ? ServiceFeeType.Discount : ServiceFeeType.None;
          controller.draft.type = type.index;
          controller.refreshData();
        },
      ),
    );
    children.addIf(
      controller.draft.type.serviceFeeType.isDiscount,
      _input(),
    );
    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  Widget _input() {
    return SettingsWidget.input(
      // initialValue: '${controller.draft.percent}',
      onTap: controller.editing.selectAll,
      controller: controller.editing,
      labelText: '設定帳單總金額%',
      hintText: '請輸入數字',
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        FilteringTextInputFormatter.singleLineFormatter,
      ],
      keyboardType: TextInputType.number,
      onChanged: (value) {
        controller.draft.percent = num.tryParse(value) ?? 0;
      },
      validator: (value) {
        value ??= '';
        if (value.isEmpty) {
          return null;
        }
        if (false == value.isNum) {
          return '請輸入數字';
        }
        final n = num.tryParse(value) ?? 0;
        if (!(n >= 0 && n <= 100)) {
          return '請輸入 0-100';
        }
        return null;
      },
    ).paddingOnly(
      left: kPadding,
    );
  }

  Widget _sector1() {
    final children = <Widget>[];
    children.addIf(
      true,
      SettingsWidget.switcher(
        titleText: '啟用',
        // value: controller.type.isOrigin,
        value: controller.draft.type.serviceFeeType.isOrigin,
        onChanged: (value) {
          final type = value ? ServiceFeeType.Origin : ServiceFeeType.None;
          controller.draft.type = type.index;
          controller.refreshData();
        },
      ),
    );
    children.addIf(
      controller.draft.type.serviceFeeType.isOrigin,
      _input(),
    );
    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  Future<void> _submit() async {
    try {
      if (true == controller.validate()) {
        // final value = await FutureObx(controller.submit(), (state) {
        //   Future(() => Get.back(result: state));
        //   return SizedBox.shrink();
        // }).dialog();
        final value = await FutureProgress<SettingPutRes>(
          future: controller.submit(),
        ).dialog<SettingPutRes>();
        if (value != null) {
          Get.back();
        }
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }
}
