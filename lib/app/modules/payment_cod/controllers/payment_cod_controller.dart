import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/payment_cod.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/enums.dart';

class PaymentCodController extends GetxController with StateMixin<String> {
  final ApiProvider apiProvider;
  final _data = Rx<PaymentCod>(null);
  PaymentCod get data => _data.value;
  void refreshData() => _data.refresh();

  PaymentCodController({
    @required this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  Future<void> onRefresh() async {
    await apiProvider.getPaymentCod().then(
      (value) {
        value.init();
        _data.value = value;
        change('', status: RxStatus.success());
      },
      onError: (error) {
        change('', status: RxStatus.error('$error'));
      },
    );
  }

  @override
  void onClose() {}

  Future<bool> submit() {
    data.init();
    return apiProvider.putPaymentCod(data);
  }
}

extension _ExtensionPaymentCod on PaymentCod {
  void init() {
    description ??= '';
    fee = fee?.round() ?? 0;
    free = free?.round() ?? 0;
    status ??= Switcher.Off.index;
  }
}
