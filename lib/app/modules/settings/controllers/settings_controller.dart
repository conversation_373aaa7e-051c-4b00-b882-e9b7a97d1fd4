import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/setting_firebase_post.dart';
import 'package:muyipork/app/data/models/res/setting_put_res.dart';
import 'package:muyipork/enums.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/data/models/other/app_settings.dart';
import 'package:muyipork/app/data/models/other/brands_info.dart';
import 'package:muyipork/app/data/models/res/setting_get_res.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:package_info/package_info.dart';

class SettingsController extends GetxController {
  static const _COUNT_OF_TAP = 5;

  final ApiProvider apiProvider;
  final PackageInfo packageInfo;
  final _draft = Rx<SettingGetRes>(null);
  final developerOptionsVisibility = false.obs;
  final count = 0.obs;
  final _checkingUpdate = false.obs;
  final _progressingValue = (0.0).obs;
  final _brandsInfo = Rx<BrandsInfo>(null);
  final _disposable = Completer();

  StoreRole get storeRole => prefProvider.jwt.storeRole;

  PrefProvider get prefProvider => apiProvider.prefProvider;

  SettingGetRes get draft => _draft.value;
  set draft(SettingGetRes value) => _draft.value = value;

  String get displayInfo {
    final code = _brandsInfo.value?.code ?? '';
    final desc = draft?.data?.other?.checkoutType?.brandsType?.name ?? '';
    return '$code: $desc';
  }

  String get storeName => _brandsInfo.value?.name ?? '-';

  double get progressingValue => _progressingValue.value * 0.01;

  bool get progressingVisible {
    return (_checkingUpdate.value == true) || (progressingValue > 0.0);
  }

  Stream<BrandsType> get checkoutTypeStream => prefProvider.brandsTypeStream();

  SettingsController({
    @required this.apiProvider,
    @required this.packageInfo,
  });

  void _initObservable() {
    // 監聽點擊次數開啟隱藏選項
    this
        .count
        .stream
        .timeout(
          1.seconds,
          onTimeout: (event) {
            logger.d('onTimeout($event)');
            this.count.value = 0;
          },
        )
        .takeUntil(this._disposable.future)
        .listen(
          (event) {
            logger.d('click count($event)');
            if (event >= _COUNT_OF_TAP) {
              this.developerOptionsVisibility.toggle();
            }
          },
          // onError: (error, stackTrace) {
          //   logger.d('timeout($error)');
          //   this.count.value = 0;
          // },
        );

    // 本地端設定檔有變更時，重新讀取
    prefProvider.settingStream.takeUntil(_disposable.future).listen(
      (event) {
        _draft.value = event;
      },
    );
    // 本地端品牌資料有變更時，重新讀取
    prefProvider.brandsInfoStream.takeUntil(_disposable.future).listen(
      (event) {
        _brandsInfo.value = event;
      },
    );
  }

  @override
  void onInit() {
    logger.d('SettingsController - onInit');
    super.onInit();
    _initObservable();
    _draft.value = prefProvider.setting;
    _brandsInfo.value = prefProvider.brandsInfo;
  }

  @override
  void onReady() {
    logger.d('[SettingsController] onReady');
  }

  @override
  void onClose() {
    logger.d('SettingsController - onClose');
    _disposable.complete();
  }

  ///
  /// 登出
  ///
  Future<void> logout() async {
    try {
      await apiProvider.postSettingFirebase(
        SettingFirebasePost(
          machineNo: prefProvider.userDefault.get(kMachineNumber),
          token: prefProvider.userDefault.get(kFcmToken),
          status: Switcher.Off.index,
        ),
      );
      final res = await apiProvider.getLogout();
      logger.d('logout: $res');
    } catch (e) {
      logger.e(e);
    } finally {
      prefProvider.token = '';
    }
  }

  ///
  /// 檢查新版本
  ///
  void checkUpdate() {
    final uri = Uri.parse(kSettingsUrl);
    final dio = Get.find<Dio>();
    _checkingUpdate.value = true;
    dio.getUri<String>(uri).then(
      (value) {
        final jsonObject = jsonDecode(value.data);
        final appSettings = AppSettings.fromJson(jsonObject);
        final packageInfo = Get.find<PackageInfo>();
        String buildNumber = packageInfo.buildNumber;
        final currentNumber = num.parse(buildNumber);
        if (appSettings.buildNumber > currentNumber) {
          final url = prefProvider.isDevelopment
              ? appSettings.development
              : appSettings.production;
          _showUpdateDialog(url);
        }
      },
    ).whenComplete(() {
      _checkingUpdate.value = false;
    });
  }

  void _showUpdateDialog(String updateUrl) {
    DialogGeneral(DialogArgs(
        contentIcon: DialogContentIcon.Alert,
        content: Center(
          child: Text(
            '有新版本',
            style: TextStyle(
              fontSize: 16.0,
            ),
          ),
        ),
        mainButtonText: '更新',
        secondaryButtonText: '取消',
        onMainButtonPress: () {
          this._doAndroidUpdate(updateUrl);
        })).dialog();
  }

  void _doAndroidUpdate(String updateUrl) {
    // try {
    //   this._checkingUpdate.value = true;
    //   OtaUpdate().execute(updateUrl).listen(
    //     (event) {
    //       this._onOtaEvent(event);
    //     },
    //     onDone: () {
    //       logger.d('OTA - execute:onDone');
    //       this._checkingUpdate.value = false;
    //     },
    //   );
    // } catch (e) {
    //   logger.d('Failed to make OTA update. Details: $e');
    // }
  }

  // void _onOtaEvent(OtaEvent event) {
  //   switch (event.status) {
  //     case OtaStatus.DOWNLOADING:
  //       // logger.d('DOWNLOADING value(${event.value})');
  //       this._progressingValue.value = double.parse(event.value);
  //       break;
  //     case OtaStatus.INSTALLING:
  //       logger.d('OTA - INSTALLING value(${event.value})');
  //       this._progressingValue.value = 0.0;
  //       break;
  //     case OtaStatus.ALREADY_RUNNING_ERROR:
  //       logger.d('OTA - ALREADY_RUNNING_ERROR value(${event.value})');
  //       break;
  //     case OtaStatus.PERMISSION_NOT_GRANTED_ERROR:
  //       logger.d('OTA - PERMISSION_NOT_GRANTED_ERROR value(${event.value})');
  //       break;
  //     case OtaStatus.INTERNAL_ERROR:
  //       logger.d('OTA - INTERNAL_ERROR value(${event.value})');
  //       break;
  //     case OtaStatus.DOWNLOAD_ERROR:
  //       logger.d('OTA - DOWNLOAD_ERROR value(${event.value})');
  //       break;
  //     case OtaStatus.CHECKSUM_ERROR:
  //       logger.d('OTA - CHECKSUM_ERROR value(${event.value})');
  //       break;
  //     default:
  //   }
  // }

  // 特殊，避免 Pref 內的 StoreType 停在錯誤的模式底下。
  // 在開發者選項設定過 checkoutType 後要做這個動作
  void verifyPrefSettings() {
    prefProvider.verifySettings(draft);
  }

  Future<SettingPutRes> submit() {
    return apiProvider.putSetting(draft.data);
  }

  void revertData() {
    draft = prefProvider.setting;
  }

  void refreshData() {
    _draft.refresh();
  }
}
