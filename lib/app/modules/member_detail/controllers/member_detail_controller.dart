import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/qr_format.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/other/member.dart';
import 'package:muyipork/app/data/models/req/member_point_req.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/member_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/keys.dart';

class MemberDetailController extends GetxController with StateMixin<String> {
  final _data = Rx<Member>(null);
  final OrderProvider orderProvider;
  final _delta = 0.obs;
  final _id = RxNum(0);
  final _disposable = Completer();

  MemberProvider get memberProvider => orderProvider.memberProvider;
  ApiProvider get apiProvider => memberProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;

  num get id => _id.value;
  set id(num value) => _id.value = value;
  Member get data => _data.value;
  num get delta => _delta.value;
  set delta(num value) => _delta.value = value;

  MemberDetailController({
    @required this.orderProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _id.stream
        .distinct()
        // 移除使用快取，避免點數錯誤
        // .where((event) {
        //   if (memberProvider.cached.containsKey(event)) {
        //     final member = memberProvider.cached[event];
        //     if (member.points != null) {
        //       // 使用點數判斷 cache
        //       logger.d('[MemberDetailController] use cache: id($event)');
        //       _data.value = memberProvider.cached[event];
        //       change('', status: RxStatus.success());
        //       return false;
        //     }
        //   }
        //   return true;
        // })
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.Data)) {
      final qr = QrFormat.fromRawJson(Get.parameters[Keys.Data]);
      id = qr.memberId;
    }
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future sendMessage(String text) {
    return memberProvider.pushMessage(data.id, text);
  }

  Future<void> onRefresh() async {
    try {
      logger.d('[MemberDetailController] onRefresh: id($id)');
      _data.value = await memberProvider.getMember(id);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
      // 不需要: 這裡是最後處理
      // rethrow;
    }
  }

  Future<num> postMemberPoint() {
    return apiProvider.postMemberPoint(
      data.id,
      MemberPointReq(
        points: delta,
        expiryDate: '2100-1-1',
        type: delta > 0 ? PointType.Income.index : PointType.Expense.index,
        comment: delta > 0 ? '店家贈點' : '店家扣點',
      ),
    );
  }

  Future<num> getMemberVip() async {
    final res = await memberProvider.getMemberVip(id);
    if (res is num && res > 0) {
      data.isVip = Switcher.On.index;
      final box = boxProvider.getGsBox(kBoxMember);
      box.write('${data.id}', data.toJson());
    }
    return res;
  }

  Future<num> getMemberUnvip() async {
    final res = await memberProvider.getMemberUnvip(id);
    if (res is num && res > 0) {
      data.isVip = Switcher.Off.index;
      final box = boxProvider.getGsBox(kBoxMember);
      box.write('${data.id}', data.toJson());
    }
    return res;
  }

  Future<num> getMemberBlock() async {
    final res = await memberProvider.getMemberBlock(id);
    if (res is num && res > 0) {
      data.status = Switcher.Off.index;
      final box = boxProvider.getGsBox(kBoxMember);
      box.write('${data.id}', data.toJson());
    }
    return res;
  }

  Future<num> getMemberUnblock() async {
    final res = await memberProvider.getMemberUnblock(id);
    if (res is num && res > 0) {
      data.status = Switcher.On.index;
      final box = boxProvider.getGsBox(kBoxMember);
      box.write('${data.id}', data.toJson());
    }
    return res;
  }

  void refreshData() {
    _data.refresh();
  }
}
