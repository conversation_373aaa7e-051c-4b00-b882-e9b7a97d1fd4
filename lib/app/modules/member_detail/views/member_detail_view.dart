import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:muyipork/app/components/background.dart';
import 'package:muyipork/app/modules/orders_setup/controllers/orders_setup_controller.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:muyipork/app/components/list_widget.dart';
import 'package:muyipork/app/components/qrcode_scanner.dart';
import 'package:muyipork/app/components/radio_button.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/stadium_button.dart';
import 'package:muyipork/app/components/underline_divider.dart';
import 'package:muyipork/app/data/models/other/qr_format.dart';
import 'package:muyipork/app/data/models/req/orders_orderid_status_put_qry.dart';
import 'package:muyipork/app/data/models/req/orders_orderid_status_put_req.dart';
import 'package:muyipork/app/modules/order_detail/controllers/order_detail_controller.dart';
import 'package:muyipork/enums.dart';
import 'package:timeago/timeago.dart' as timeago;

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/line_alert.dart';
import 'package:muyipork/app/components/member_avatar.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';
import 'package:url_launcher/url_launcher.dart';

import '../controllers/member_detail_controller.dart';
import 'point_dialog.dart';

class MemberDetailView extends GetView<MemberDetailController> {
  const MemberDetailView({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '會員主頁',
      child: DecoratedBox(
        decoration: const BoxDecoration(
          color: OKColor.Tab,
          boxShadow: [
            BoxShadow(
              color: OKColor.Shadow,
              offset: Offset(0, 0),
              blurRadius: 6,
            ),
          ],
        ),
        child: RefreshIndicator(
          onRefresh: controller.onRefresh,
          child: controller.obx(
            (state) => _body(),
            onError: _error,
          ),
        ),
      ),
    );
  }

  Widget _error(String message) {
    return ListWidget.message(
      message,
      buttonText: '重試',
      onPressed: controller.onRefresh,
    );
  }

  Widget _body() {
    return BottomWidgetPage(
      bottom: SafeArea(child: _bottomBar().paddingSymmetric(vertical: 8)),
      // child: ListView(
      //   physics: const AlwaysScrollableScrollPhysics(),
      //   children: _children().toList(growable: false),
      // ),
      child: Background(
        background: _Avatar(
          avatar: controller.data.avatar,
          name: controller.data.nicknameAndName,
          mobilePhone: controller.data.mobilePhone,
        ).paddingOnly(top: 12),
        child: ListView(
          padding: const EdgeInsets.only(top: 84),
          physics: const AlwaysScrollableScrollPhysics(),
          children: _children().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    // yield const SizedBox(height: 12);
    // yield _Avatar(
    //   avatar: controller.data.avatar,
    //   name: controller.data.nicknameAndName,
    //   mobilePhone: controller.data.mobilePhone,
    // );
    // yield const SizedBox(height: 12);
    yield DecoratedBox(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(30.0),
        ),
        color: const Color(0xffeeeef3),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: _childrenElements().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _childrenElements() sync* {
    yield const SizedBox(height: 16);
    yield Center(
      child: Obx(() {
        return _VipSwitchListTile(
          value: controller.data.isVip.switcher.isOn,
          onChanged: _setVipEnabled,
        );
      }),
    );
    yield const SizedBox(height: 16);
    yield Obx(() {
      return _TicketGroup(
        couponCount: controller.data?.allCouponCount ?? 0,
        pointCount: controller.data?.points ?? 0,
        orderCount: controller.data?.orderCount ?? 0,
      );
    });
    yield const SizedBox(height: 16);
    yield Obx(() {
      return UnderlineDivider(
        backgroundColor: Colors.white,
        insets: kContentPadding,
        child: SwitchListTile(
          dense: true,
          contentPadding: kContentPadding,
          // tileColor: Colors.white,
          value: controller.data.status.switcher.isOff,
          onChanged: (value) => _setEnabled(!value),
          title: Text(
            '封鎖會員',
            style: TextStyle(
              fontSize: 16,
              color: OKColor.Error,
            ),
            textAlign: TextAlign.left,
          ),
        ),
      );
    });
    yield SettingsWidget.item(
      titleText: '基本資料',
      onPressed: () {
        Get.toNamed(
          Routes.MEMBER_PROFILE,
          parameters: {
            Keys.Tag: '${controller.data.id}',
            Keys.Id: '${controller.data.id}',
          },
        );
      },
    );
    yield SettingsWidget.item(
      titleText: '店家備註',
      onPressed: () {
        Get.toNamed(
          Routes.MEMBER_MEMO,
          parameters: <String, String>{
            Keys.Id: '${controller.data.id}',
          },
        );
      },
    );
    yield SettingsWidget.item(
      titleText: '消費分析',
      onPressed: () {
        // 會員封鎖時不可進入
        if (controller.data.status.switcher.isOn) {
          // FIXME:
          Get.showSnackbar(
            GetBar(
              message: '敬請期待',
              duration: 1500.milliseconds,
            ),
          );
          // Get.toNamed(
          //   Routes.MEMBER_REPORT,
          //   parameters: <String, String>{
          //     Keys.Id: '${controller.member.id}',
          //   },
          // );
        } else {
          DialogGeneral.alert('會員已封鎖').dialog();
        }
      },
    );
    if (controller.prefProvider.brandsType.containsDinner == true) {
      yield SettingsWidget.item(
        titleText: '餐飲訂單紀錄',
        onPressed: () {
          final req = OrderReq(
            memberId: controller.data.id,
            sort: 'desc',
            withSubOrder: Switcher.Off.index,
            type: ORDER_TYPE_DINNER.map((e) => e.index).toList(),
          );
          Get.toNamed(
            Routes.ORDER_LIST,
            parameters: <String, String>{
              Keys.Data: req.toRawJson(),
              Keys.Title: '餐飲訂單紀錄',
            },
          );
        },
      );
    }
    if (controller.prefProvider.brandsType.containsRetail == true) {
      yield SettingsWidget.item(
        titleText: '零售訂單紀錄',
        onPressed: () {
          final req = OrderReq(
            memberId: controller.data.id,
            sort: 'desc',
            withSubOrder: Switcher.Off.index,
            type: ORDER_TYPE_RETAIL.map((e) => e.index).toList(),
          );
          Get.toNamed(
            Routes.ORDER_LIST,
            parameters: <String, String>{
              Keys.Data: req.toRawJson(),
              Keys.Title: '零售訂單紀錄',
            },
          );
        },
      );
    }
    yield SettingsWidget.space();
    yield Row(children: _pointTitleElements().toList(growable: false));
    yield SettingsWidget.item(
      titleText: '積點紀錄',
      onPressed: () {
        // 特殊: 會員封鎖時，取得會員目前點數及點數紀錄會出現錯誤
        // 伺服器返回: 會員不存在
        if (controller.data.status.switcher.isOn) {
          Get.toNamed(
            Routes.POINTS_HISTORY,
            parameters: <String, String>{
              Keys.Id: '${controller.data.id}',
            },
          );
        } else {
          DialogGeneral.alert('會員已封鎖').dialog();
        }
      },
    );
    yield SettingsWidget.space();
    yield SettingsWidget.title(
      titleText: '優惠券',
    );
    if (controller.prefProvider.brandsType.containsDinner == true) {
      yield _TicketTile(
        count:
            controller.data.getCouponCount(StoreType.Dinner, OrderSource.App),
        titleText: '餐飲門市券',
        onTap: () {
          Get.showSnackbar(
            GetBar(
              message: '敬請期待',
              duration: 1500.milliseconds,
            ),
          );
          // final parameters = {
          //   Keys.Id: controller.id,
          //   Keys.Title: '餐飲門市券',
          //   'type': StoreType.Dinner.index,
          //   'source': OrderSource.App.index,
          // };
          // Get.toNamed(
          //   Routes.MEMBER_COUPONS,
          //   parameters: parameters.toStringMap(),
          // );
        },
        onPressed: () {
          Get.showSnackbar(
            GetBar(
              message: '敬請期待',
              duration: 1500.milliseconds,
            ),
          );
          // final parameters = {
          //   Keys.Id: controller.id,
          //   Keys.Title: '餐飲門市券',
          //   'type': StoreType.Dinner.index,
          //   'source': OrderSource.App.index,
          // };
          // Get.toNamed(
          //   Routes.COUPONS,
          //   parameters: parameters.toStringMap(),
          // ).then((value) => controller.onRefresh());
        },
      );
    }
    if (controller.prefProvider.brandsType.containsDinner == true) {
      yield _TicketTile(
        count:
            controller.data.getCouponCount(StoreType.Dinner, OrderSource.Line),
        titleText: '餐飲線上券',
        onTap: () {
          Get.showSnackbar(
            GetBar(
              message: '敬請期待',
              duration: 1500.milliseconds,
            ),
          );
          // final parameters = {
          //   Keys.Id: controller.id,
          //   Keys.Title: '餐飲線上券',
          //   'type': StoreType.Dinner.index,
          //   'source': OrderSource.Line.index,
          // };
          // Get.toNamed(
          //   Routes.MEMBER_COUPONS,
          //   parameters: parameters.toStringMap(),
          // );
        },
        onPressed: () {
          Get.showSnackbar(
            GetBar(
              message: '敬請期待',
              duration: 1500.milliseconds,
            ),
          );
          // final parameters = {
          //   Keys.Id: controller.id,
          //   Keys.Title: '餐飲線上券',
          //   'type': StoreType.Dinner.index,
          //   'source': OrderSource.Line.index,
          // };
          // Get.toNamed(
          //   Routes.COUPONS,
          //   parameters: parameters.toStringMap(),
          // ).then((value) => controller.onRefresh());
        },
      );
      if (controller.prefProvider.brandsType.containsRetail == true) {
        yield _TicketTile(
          count:
              controller.data.getCouponCount(StoreType.Retail, OrderSource.App),
          titleText: '零售門市券',
          onTap: () {
            Get.showSnackbar(
              GetBar(
                message: '敬請期待',
                duration: 1500.milliseconds,
              ),
            );
            // final parameters = {
            //   Keys.Id: controller.id,
            //   Keys.Title: '零售門市券',
            //   'type': StoreType.Retail.index,
            //   'source': OrderSource.App.index,
            // };
            // Get.toNamed(
            //   Routes.MEMBER_COUPONS,
            //   parameters: parameters.toStringMap(),
            // );
          },
          onPressed: () {
            Get.showSnackbar(
              GetBar(
                message: '敬請期待',
                duration: 1500.milliseconds,
              ),
            );
            // final parameters = {
            //   Keys.Id: controller.id,
            //   Keys.Title: '零售門市券',
            //   'type': StoreType.Retail.index,
            //   'source': OrderSource.App.index,
            // };
            // Get.toNamed(
            //   Routes.COUPONS,
            //   parameters: parameters.toStringMap(),
            // ).then((value) => controller.onRefresh());
          },
        );
      }
    }
    if (controller.prefProvider.brandsType.containsRetail == true) {
      yield _TicketTile(
        count:
            controller.data.getCouponCount(StoreType.Retail, OrderSource.Line),
        titleText: '零售線上券',
        onTap: () {
          Get.showSnackbar(
            GetBar(
              message: '敬請期待',
              duration: 1500.milliseconds,
            ),
          );
          // final parameters = {
          //   Keys.Id: controller.id,
          //   Keys.Title: '零售線上券',
          //   'type': StoreType.Retail.index,
          //   'source': OrderSource.Line.index,
          // };
          // Get.toNamed(
          //   Routes.MEMBER_COUPONS,
          //   parameters: parameters.toStringMap(),
          // );
        },
        onPressed: () {
          Get.showSnackbar(
            GetBar(
              message: '敬請期待',
              duration: 1500.milliseconds,
            ),
          );
          // final parameters = {
          //   Keys.Id: controller.id,
          //   Keys.Title: '零售線上券',
          //   'type': StoreType.Retail.index,
          //   'source': OrderSource.Line.index,
          // };
          // Get.toNamed(
          //   Routes.COUPONS,
          //   parameters: parameters.toStringMap(),
          // ).then((value) => controller.onRefresh());
        },
      );
    }
    yield SettingsWidget.space();
    yield const SizedBox(height: 72);
  }

  Widget _bottomBar() {
    Iterable<Widget> children() sync* {
      yield const SizedBox(width: 48);
      yield IconButton(
        padding: EdgeInsets.zero,
        iconSize: 38,
        onPressed: _call,
        icon: SvgPicture.asset('assets/images/icon_action_call.svg'),
      );
      yield const SizedBox(width: 40);
      yield IconButton(
        padding: EdgeInsets.zero,
        iconSize: 38,
        onPressed: _buy,
        icon: Container(
          width: 38,
          height: 38,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: OKColor.Primary,
          ),
          padding: const EdgeInsets.all(4),
          child: SvgPicture.asset(
            'assets/images/icon_order.svg',
            width: 24,
            height: 24,
          ),
        ),
      );
      yield const SizedBox(width: 40);
      yield IconButton(
        padding: EdgeInsets.zero,
        iconSize: 38,
        onPressed: _line,
        icon: SvgPicture.asset('assets/images/icon_action_line.svg'),
      );
      yield const SizedBox(width: 48);
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      decoration: const ShapeDecoration(
        shape: StadiumBorder(),
        color: OKColor.Tab,
        shadows: [
          BoxShadow(
            color: Color(0x29000000),
            offset: Offset(0, 3),
            blurRadius: 12,
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  void _buy() {
    Get.toNamed(
      Routes.ORDERS_SETUP,
      arguments: OrdersSetupArgs(
        kind: controller.prefProvider.storeType.productKind.index,
      ),
      parameters: {
        // 加入會員參數
        Keys.Data: QrFormat(
          memberId: controller.id,
        ).toRawJson(),
      },
    );
  }

  void _setEnabled(bool value) {
    final backup = controller.data.status;
    controller.data.status = value.switcher.index;
    controller.refreshData();
    // call api
    final future =
        value ? controller.getMemberUnblock : controller.getMemberBlock;
    future().then(
      (event) {
        if (event is num && event > 0) {
          // 成功
        } else {
          // 失敗，復原
          controller.data.status = backup;
          controller.refreshData();
        }
      },
      onError: (value) {
        // show dialog
        DialogGeneral.alert(value).dialog().then(
          (value) {
            // 失敗，復原
            controller.data.status = backup;
            controller.refreshData();
          },
        );
      },
    );
  }

  void _setVipEnabled(bool value) {
    // 備份原始值
    final backup = controller.data.isVip;
    // 先作用
    controller.data.isVip = value.switcher.index;
    controller.refreshData();
    // call api
    final future = value ? controller.getMemberVip : controller.getMemberUnvip;
    future().then(
      (event) {
        if (event is num && event > 0) {
          controller.memberProvider.addVipCount(value ? 1 : -1);
        } else {
          // 失敗，復原
          controller.data.isVip = backup;
          controller.refreshData();
        }
      },
      onError: (value) {
        // show dialog
        DialogGeneral.alert('$value').dialog().then(
          (value) {
            // 失敗，復原
            controller.data.isVip = backup;
            controller.refreshData();
          },
        );
      },
    );
  }

  ///
  /// 增點，會員封鎖時無法執行 (伺服器規則: 會員不存在)
  ///
  void _addPoint() {
    if (controller.data.status.switcher.isOn) {
      _showPoint(PointAction.Add).then(
        (value) {
          if (controller.delta != 0) {
            return _showPoint(PointAction.AddInfo);
          }
        },
      ).then(
        (value) {
          if (controller.delta != 0) {
            // 送出 api
            return FutureProgress<num>(
              future: controller.postMemberPoint(),
            ).dialog();
          }
        },
      ).then(
        (value) {
          if (value is num) {
            controller.data.points = value;
            controller.refreshData();
          }
        },
      );
    } else {
      DialogGeneral.alert('會員已封鎖').dialog();
    }
  }

  ///
  /// 扣點，會員封鎖時無法執行 (伺服器規則)
  ///
  void _removePoint() {
    if (controller.data.status.switcher.isOn) {
      _showPoint(PointAction.Remove).then(
        (value) {
          if (controller.delta != 0) {
            // 要扣除的點數 (-1)
            if (controller.delta.abs() > controller.data.points) {
              return Future.error('輸入數值請等於或小於現有點數');
            } else {
              return _showPoint(PointAction.RemoveInfo);
            }
          }
        },
      ).then(
        (value) {
          // 送出 api
          if (controller.delta != 0) {
            return FutureProgress<num>(
              future: controller.postMemberPoint(),
            ).dialog();
          }
        },
      ).then(
        (value) {
          if (value is num) {
            controller.data.points = value;
            controller.refreshData();
          }
        },
      ).onError(
        (error, stackTrace) {
          DialogGeneral.alert('$error').dialog();
        },
      );
    } else {
      DialogGeneral.alert('會員已封鎖').dialog();
    }
  }

  Iterable<Widget> _pointTitleElements() sync* {
    yield SizedBox(width: kPadding);
    yield Expanded(
      child: Text(
        '積點',
        style: TextStyle(
          fontSize: 14,
          color: const Color(0xff666666),
        ),
        textAlign: TextAlign.left,
      ).paddingSymmetric(
        vertical: 16,
      ),
    );
    yield StadiumButton(
      buttonText: '扣點',
      backgroundColor: OKColor.Tab,
      onPressed: () {
        if (controller.data.points is num && controller.data.points > 0) {
          _removePoint();
        } else {
          final points = controller.data.points ?? 0;
          DialogGeneral.alert('目前點數為 $points 無法扣除').dialog();
        }
      },
    );
    yield SizedBox(width: 8);
    yield StadiumButton(
      buttonText: '增點',
      onPressed: _addPoint,
    );
    yield SizedBox(width: kPadding);
  }

  ///
  /// 撥打電話
  ///
  void _call() {
    // FIXME:
    if (controller.data.mobilePhone != null &&
        controller.data.mobilePhone.isNotEmpty) {
      final url = 'tel:${controller.data.mobilePhone}';
      canLaunch(url).then((value) {
        if (true == value) {
          launch(url);
        }
      });
    } else {
      DialogGeneral.alert('會員未提供電話號碼').dialog();
    }
  }

  ///
  /// 開啟掃描
  ///
  void _scan() {
    // 存在一般會員
    // _parse('{"type":"member","member_id":77}');
    // 訂單
    // _parse('{"type":"order","order_id":3016}');
    QrCodeScanner().dialog<String>().then(
      (value) {
        if (value != null && value.isNotEmpty) {
          _parse(value);
        }
      },
    );
  }

  void _acceptOrder(num id) {
    _changeStatus(id, OrderStatus.Accepted);
  }

  void _rejectOrder(num id) {
    // 訂單狀態從 處理中(0) 換成 訂單取消(店家) (3)
    // this.onRejectClicked(data);
    _showRejectDialog().then(
      (value) {
        switch (value) {
          case OrderStatus.CancelByApp:
          case OrderStatus.CancelByLine:
            logger.d('[MemberDetailView] ${value.name}');
            _changeStatus(id, value);
            // _changeStatus(id, OrderStatus.Padding);
            break;
          default:
        }
      },
    );
  }

  void _changeStatus(num id, OrderStatus value) {
    FutureProgress(
      future: controller.orderProvider.putOrderStatus(
        id,
        OrdersOrderIdStatusPutQry(isPushMsg: Switcher.On.index),
        OrdersOrderIdStatusPutReq(status: value.index),
      ),
    ).dialog();
  }

  Future<OrderStatus> _showRejectDialog() {
    final completer = new Completer<OrderStatus>();
    final selected = Rx<OrderStatus>(OrderStatus.Padding);
    DialogGeneral(
      DialogArgs(
        header: DialogGeneral.titleText('訂單狀態'),
        mainButtonText: '確認',
        secondaryButtonText: '取消',
        onMainButtonPress: () {
          completer.complete(selected.value);
        },
        content: Obx(() {
          final children = <Widget>[];
          children.addIf(
            true,
            RadioButton<OrderStatus>(
              // contentPadding: kContentPadding,
              // title: DialogGeneral.centerContentText(OrderStatus.Padding.name),
              titleText: OrderStatus.Padding.name,
              value: OrderStatus.Padding,
              groupValue: selected.value,
              onChanged: selected,
            ),
          );
          children.addIf(
            true,
            RadioButton<OrderStatus>(
              // contentPadding: kContentPadding,
              // title: DialogGeneral.centerContentText(OrderStatus.CancelByApp.name),
              titleText: OrderStatus.CancelByApp.name,
              value: OrderStatus.CancelByApp,
              groupValue: selected.value,
              onChanged: selected,
            ),
          );
          children.addIf(
            true,
            RadioButton<OrderStatus>(
              // contentPadding: kContentPadding,
              // title: DialogGeneral.centerContentText(OrderStatus.CancelByLine.name),
              titleText: OrderStatus.CancelByLine.name,
              value: OrderStatus.CancelByLine,
              groupValue: selected.value,
              onChanged: selected,
            ),
          );
          return Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: children,
            ),
          );
        }),
      ),
    ).dialog();
    return completer.future;
  }

  // TODO: 最後要移到 orders view 內處理
  void _checkOutOrder(num id) {
    controller.orderProvider.getOrderDetail(id).then(
      (value) {
        final orderSummary = value.data.asOrderSummary();
        Get.toNamed(
          Routes.ORDERS_SUM_UP,
          arguments: [orderSummary],
          parameters: <String, String>{
            Keys.Tag: '${DateTime.now().millisecondsSinceEpoch}',
          },
        );
      },
      onError: (error) {
        //
      },
    );
  }

  /// 分析字串
  void _parse(String value) {
    try {
      final qr = QrFormat.fromRawJson(value);
      if (qr.isMember) {
        // 會員
        controller.id = qr.memberId;
      } else if (qr.isOrder) {
        final id = qr.orderId;
        // 訂單
        // Get.offAndToNamed(
        // Get.offNamed(
        Get.toNamed(
          Routes.ORDER_DETAIL,
          parameters: {
            Keys.Tag: '$id',
            Keys.Id: '$id',
          },
        ).then(
          (value) {
            switch (value) {
              case OrderDetailViewShortCut.AcceptOrder:
                _acceptOrder(id);
                break;
              case OrderDetailViewShortCut.RejectOrder:
                _rejectOrder(id);
                break;
              case OrderDetailViewShortCut.CheckoutOrder:
                _checkOutOrder(id);
                break;
              default:
            }
          },
        );
      } else if (qr.isCoupon || qr.isCoupon1 || qr.isCoupon2) {
        Get.toNamed(
          Routes.COUPON_DETAIL,
          parameters: <String, String>{
            Keys.Data: qr.toRawJson(),
          },
          // arguments: CouponDetailArguments(
          //   checkoutPressed: (value) {
          //     //
          //   },
          //   continuePressed: (value) {
          //     //
          //   },
          // ),
        ).then(
          (value) {
            //
          },
        );
      } else {
        DialogGeneral.alert('不是正確的 QR 碼').dialog();
      }
    } catch (e) {
      // 顯示錯誤訊息
      logger.e(e);
      DialogGeneral.alert('不是正確的 QR 碼').dialog();
    }
  }

  ///
  /// line 會員
  ///
  void _line() {
    _showLineSheet();
  }

  void _showLineSheet() {
    _Page().sheet<String>().then(
      (value) {
        if (value != null && value.isNotEmpty) {
          FutureProgress<Map>(
            future: controller.sendMessage(value),
            // HACK: test error
            // future: controller.memberProvider.pushMessage('', value),
            // TODO: refactor
            onData: _showDialogOkay,
            // TODO: refactor
            onError: (Object error) => DialogGeneral.alert('$error'),
          ).dialog();
        }
      },
    );
  }

  Widget _showDialogOkay(Map data) {
    return DialogGeneral(
      DialogArgs(
        header: Text.rich(
          TextSpan(
            style: const TextStyle(
              fontSize: 16,
              color: kColorLine,
            ),
            children: [
              TextSpan(
                text: 'LINE ',
                style: const TextStyle(
                  fontWeight: FontWeight.w700,
                ),
              ),
              TextSpan(
                text: '留言已傳送',
                style: const TextStyle(
                  color: OKColor.Gray33,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          textAlign: TextAlign.center,
        ),
        content:
            DialogGeneral.centerContentText('會員如果回覆您的Line留言，請至LINE官方帳號APP上查閱。'),
        mainButtonText: '確認',
      ),
    );
  }

  Future<void> _showPoint(PointAction action) {
    return PointDialog(
      point: controller.data.points,
      delta: controller.delta,
      action: action,
      onValueChanged: (value) {
        controller.delta = value;
      },
    ).dialog();
  }
}

class _LastOrderInfo extends StatelessWidget {
  final String orderNumber;
  final DateTime orderDate;

  const _LastOrderInfo({
    Key key,
    this.orderNumber,
    this.orderDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 16,
      ),
      color: Colors.white,
      alignment: Alignment.center,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset('assets/images/icon_receipt.svg'),
          SizedBox(width: 8),
          Text(
            '上次消費',
            style: TextStyle(
              fontSize: 14,
              color: OKColor.Gray33,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(
            width: 36,
            height: 40,
            child: VerticalDivider(),
          ),
          _orderInfo(),
        ],
      ),
    );
  }

  String get _howLongAgo {
    if (orderDate != null) {
      timeago.setLocaleMessages('zh', timeago.ZhMessages());
      timeago.setDefaultLocale('zh');
      return timeago.format(orderDate);
    }
    return '';
  }

  Widget _orderInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text.rich(
          TextSpan(
            style: TextStyle(
              fontSize: 14,
              color: OKColor.Gray66,
            ),
            children: [
              TextSpan(
                text: '日期：',
              ),
              TextSpan(
                text: orderDate?.yMd ?? '-',
                style: TextStyle(
                  color: OKColor.Gray33,
                ),
              ),
              TextSpan(
                text: ' ',
              ),
              TextSpan(
                text: _howLongAgo ?? '',
                style: TextStyle(
                  color: OKColor.Primary,
                ),
              ),
            ],
          ),
          textAlign: TextAlign.left,
        ),
        SizedBox(
          height: 6,
        ),
        Text.rich(
          TextSpan(
            style: TextStyle(
              fontSize: 14,
              color: OKColor.Gray66,
            ),
            children: [
              TextSpan(
                text: '訂單編號：',
              ),
              TextSpan(
                text: orderNumber ?? '-',
                style: TextStyle(
                  color: OKColor.Gray33,
                ),
              ),
            ],
          ),
          textAlign: TextAlign.left,
        ),
      ],
    );
  }
}

class _TicketTile extends StatelessWidget {
  final String titleText;
  final num count;
  final Function onPressed;
  final Function onTap;

  const _TicketTile({
    Key key,
    this.titleText,
    this.count,
    this.onPressed,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: Row(
        children: [
          SizedBox(
            width: kPadding,
          ),
          Expanded(
            child: _listTile(),
          ),
          SizedBox(
            width: 12,
          ),
          StadiumButton(
            buttonText: '發券',
            onPressed: onPressed,
          ),
          SizedBox(
            width: kPadding,
          ),
        ],
      ),
    );
  }

  Widget _listTile() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ListTile(
          onTap: onTap,
          dense: true,
          contentPadding: EdgeInsets.zero,
          tileColor: Colors.white,
          title: Text(
            // '餐飲門市券',
            titleText ?? '',
            style: TextStyle(
              fontSize: 16,
              color: Colors.black,
            ),
            textHeightBehavior:
                TextHeightBehavior(applyHeightToFirstAscent: false),
            textAlign: TextAlign.left,
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${count?.round() ?? 0}張',
                style: TextStyle(
                  fontSize: 16,
                  color: OKColor.Primary,
                ),
                textHeightBehavior:
                    TextHeightBehavior(applyHeightToFirstAscent: false),
                textAlign: TextAlign.right,
              ),
              Icon(Icons.navigate_next),
            ],
          ),
        ),
        Divider(
          height: 1,
        )
      ],
    );
  }
}

///
/// TODO: refactor me
/// Line 留言
///
class _Page extends StatelessWidget {
  final ValueChanged<String> onSendPressed;
  final message = ''.obs;

  _Page({
    Key key,
    this.onSendPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(
          width: 300.0,
          child: const LineAlert(),
        ),
        const SizedBox(
          height: 22.0,
        ),
        ColoredBox(
          color: kColorLine,
          child: Row(
            children: [
              const SizedBox(
                width: 12.0,
              ),
              Expanded(
                child: TextField(
                  onChanged: this.message,
                  autofocus: true,
                  inputFormatters: [
                    // FilteringTextInputFormatter.singleLineFormatter,
                  ],
                  decoration: InputDecoration(
                    border: const OutlineInputBorder(
                      borderSide: BorderSide.none,
                      borderRadius: kBorderRadius,
                    ),
                    fillColor: Colors.white,
                    filled: true,
                    hintText: 'LINE留言給顧客…',
                    hintStyle: const TextStyle(
                      fontSize: 16,
                      color: OKColor.Gray66,
                    ),
                    contentPadding: kContentPadding,
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(
                  Icons.send,
                  color: Colors.white,
                ),
                onPressed: () {
                  if (onSendPressed != null) {
                    onSendPressed.call(message.value);
                  } else {
                    // 相當於 picker
                    Get.back(result: message.value);
                  }
                },
              ),
            ],
          ).paddingSymmetric(
            vertical: 8.0,
          ),
        ),
      ],
    );
  }
}

///
/// 顯示會員頭像、姓名、電話
///
class _Avatar extends StatelessWidget {
  final String avatar;
  final String name;
  final String mobilePhone;

  const _Avatar({
    Key key,
    this.avatar,
    this.name,
    this.mobilePhone,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final children = <Widget>[];
    children.addIf(true, SizedBox(width: kPadding));
    children.addIf(
      true,
      MemberAvatar(
        imageUrl: avatar,
        size: 60.0,
      ),
    );
    children.addIf(true, SizedBox(width: 16));
    children.addIf(true, Expanded(child: _nameAndMobile()));
    children.addIf(true, SizedBox(width: kPadding));
    return Row(children: children);
  }

  Widget _nameAndMobile() {
    final children = <Widget>[];
    children.addIf(
      true,
      Text(
        // '草屯大山金城武',
        name ?? '',
        style: TextStyle(
          fontSize: 16,
          color: Colors.white,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.left,
      ),
    );
    children.addIf(
      true,
      SizedBox(
        height: 8,
      ),
    );
    children.addIf(
      true,
      Text(
        // '09*****888',
        mobilePhone ?? '',
        style: TextStyle(
          fontSize: 14,
          color: Colors.white,
        ),
        textAlign: TextAlign.left,
      ),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
}

///
/// vip switch
///
class _VipSwitchListTile extends StatelessWidget {
  final bool value;
  final ValueChanged<bool> onChanged;

  const _VipSwitchListTile({
    Key key,
    this.value,
    this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: ShapeDecoration(
        shape: StadiumBorder(
          side: BorderSide(
            width: 1.0,
            color: const Color(0xffe9e9f0),
          ),
        ),
        color: Colors.white,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 18,
          ),
          Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
              border: Border.all(
                width: 1.0,
                color: const Color(0xfff8eccf),
              ),
            ),
            child: SvgPicture.asset('assets/images/icon_crown.svg'),
          ),
          SizedBox(
            width: 12,
          ),
          Text.rich(
            TextSpan(
              style: TextStyle(
                fontSize: 17,
                color: Colors.black,
              ),
              children: [
                TextSpan(
                  text: '升級為',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextSpan(
                  text: 'VIP',
                  style: TextStyle(
                    color: OKColor.Primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextSpan(
                  text: '會員',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(
            width: 16,
          ),
          Switch(
            value: value,
            onChanged: onChanged,
          ),
          SizedBox(
            width: 8,
          ),
        ],
      ),
    );
  }
}

///
/// 優惠券、點數、消費次數
///
class _TicketGroup extends StatelessWidget {
  final num couponCount; // 優惠券
  final num pointCount; // 點數
  final num orderCount; // 消費次數

  const _TicketGroup({
    Key key,
    this.couponCount,
    this.pointCount,
    this.orderCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _Ticket(
          icon: SvgPicture.asset('assets/images/icon_ticket.svg'),
          text: '優惠券',
          count: couponCount ?? 0,
          unit: '張',
        ),
        SizedBox(
          height: 55,
          child: VerticalDivider(),
        ),
        _Ticket(
          icon: DecoratedBox(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: OKColor.Gray33,
            ),
            child: Text(
              'P',
              style: TextStyle(
                fontSize: 18,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          text: '現有積點',
          count: pointCount ?? 0,
          unit: '點',
          countColor: OKColor.Primary,
        ),
        SizedBox(
          height: 55,
          child: VerticalDivider(),
        ),
        _Ticket(
          icon: Icon(
            Icons.assignment,
            size: 20,
          ),
          text: '消費次數',
          count: orderCount ?? 0,
          unit: '次',
        ),
      ],
    );
  }
}

///
/// 三格資訊
///
class _Ticket extends StatelessWidget {
  final Widget icon;
  final String text;
  final Color countColor;
  final num count;
  final String unit;

  const _Ticket({
    Key key,
    this.icon,
    this.text,
    this.count,
    this.countColor,
    this.unit,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _label(),
        SizedBox(
          height: 8,
        ),
        Text.rich(
          TextSpan(
            style: const TextStyle(
              fontSize: 28,
              color: Colors.black,
            ),
            children: [
              TextSpan(
                text: '${count?.round() ?? 0}',
                style: TextStyle(
                  fontSize: 26,
                  color: countColor ?? Colors.black,
                ),
              ),
              TextSpan(
                text: ' $unit',
                style: TextStyle(
                  fontSize: 14,
                  color: OKColor.Gray66,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _label() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox.fromSize(
          size: Size.square(20),
          child: icon ?? SizedBox.shrink(),
        ),
        SizedBox(
          width: 4,
        ),
        Text(
          // '優惠券',
          text ?? '',
          style: TextStyle(
            fontSize: 14,
            color: OKColor.Gray33,
            fontWeight: FontWeight.w700,
          ),
          textAlign: TextAlign.left,
        ),
      ],
    );
  }
}
