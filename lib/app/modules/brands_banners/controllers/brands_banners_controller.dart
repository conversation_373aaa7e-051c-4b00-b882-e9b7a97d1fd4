import 'dart:async';

import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:muyipork/app/data/models/other/brands_banners_put.dart'
    as Banners;
import 'package:muyipork/app/data/models/other/brands_banners_put.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/other/image_model.dart';

class BrandsBannersController extends GetxController with StateMixin<bool> {
  static const MAX_TILE = 10;
  final ApiProvider apiProvider;
  final ImagePicker picker = ImagePicker();
  final imageEditing = false.obs;
  final tiles = <ImageModel>[].obs;
  final imagesToRemove = <ImageModel>[].obs;
  final _disposable = Completer();
  final regexp = RegExp(r'^https?://');

  BrandsBannersController({
    this.apiProvider,
  });

  void _initObservable() {
    this
        .tiles
        .stream
        .where((event) => event.isEmpty)
        .takeUntil(_disposable.future)
        .listen((event) {
      this.imageEditing.value = false;
    });
  }

  @override
  void onInit() {
    super.onInit();
    this._initObservable();
  }

  @override
  void onReady() {
    super.onReady();
    // 取得圖片
    append(
      () => () {
        return apiProvider.getBrandsBanners().then(
          (value) {
            final it = value.map((e) {
              return ImageModel(
                id: e.imageId,
                url: e.imageUrl,
              );
            });
            this.tiles.addAll(it);
            return Future.value(true);
          },
        );
      },
    );
  }

  @override
  void onClose() {
    this._disposable.complete();
  }

  // 刪除不需要的圖片
  Future<void> _removeImages() async {
    final it = this.imagesToRemove.where((e) => regexp.hasMatch(e.url));
    this.imagesToRemove.clear();
    await Future.forEach<ImageModel>(
        it, (element) => apiProvider.deleteImage(element.id));
  }

  // 上傳圖片
  Future<Iterable<Banners.Image>> _applyImages() async {
    // 儲存圖片(不是 http 開頭)
    final files = this.tiles.where((e) => !regexp.hasMatch(e.url));
    final images =
        await apiProvider.postImages(List.from(files.map((e) => e.url)));
    // 上傳完成後賦予 id
    for (var i = 0; i < files.length; i++) {
      files.elementAt(i).id = images.elementAt(i).id;
    }

    return List.generate(this.tiles.length, (index) {
      final element = this.tiles.elementAt(index);
      return Banners.Image(
        imageId: element.id,
        sort: this.tiles.length - index,
      );
    });
  }

  Future<bool> submit() {
    // 移除不使用的圖片
    return _removeImages().then(
      (value) {
        // 上傳圖片
        return _applyImages();
      },
    ).then(
      (value) {
        // 設定圖片編號
        final data = BrandsBannersPut(images: value.toList());
        return apiProvider.putBrandsBanners(data);
      },
      onError: (error, stackTrace) {
        return Future<Map>.error(error);
      },
    );
  }
}
