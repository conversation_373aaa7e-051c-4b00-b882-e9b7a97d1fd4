import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/custom_editor.dart';
import 'package:muyipork/app/components/paymethod_item.dart';
import 'package:muyipork/app/components/save_page.dart';
import 'package:muyipork/app/data/models/other/multiple_payment.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/multiple_payment_controller.dart';

class MultiplePaymentView extends GetView<MultiplePaymentController> {
  final _formKey = GlobalKey<FormState>();
  final bool enabled;
  final num total;
  final Iterable<MultiplePayment> multiplePayment;

  MultiplePaymentView({
    this.enabled,
    this.total,
    this.multiplePayment,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MultiplePaymentController>(
      init: MultiplePaymentController(
        prefProvider: Get.find(),
        multiplePayment: multiplePayment,
      ),
      builder: (controller) {
        // 設定總價
        controller.total = total;
        return DraggableScrollableSheet(
          initialChildSize: 1.0,
          builder: (BuildContext context, ScrollController scrollController) {
            return SavePage(
              onPressed: _submit,
              child: Column(
                children: [
                  const SizedBox(
                    height: 12,
                  ),
                  _close(),
                  Expanded(
                    child: _theme(_list(scrollController)),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _submit() {
    if (_formKey.currentState.validate()) {
      final it = controller.list
          .map((element) => element.multiplePayment)
          .where((element) {
        // 特殊: server's rule, must have value
        element.other ??= '';
        return element.money != null && element.money > 0;
      });
      Get.back(result: it);
    }
  }

  Widget _theme(final Widget child) {
    return Theme(
      data: Get.theme.copyWith(
        inputDecorationTheme: const InputDecorationTheme(
          // alignLabelWithHint: true,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 8.0,
            vertical: 16,
          ),
          hintStyle: const TextStyle(
            fontSize: 24.0,
            color: const Color(0xffbfbfbf),
          ),
          labelStyle: const TextStyle(
            fontSize: 16.0,
            color: const Color(0xff333333),
          ),
          filled: true,
          fillColor: Colors.white,
          border: const OutlineInputBorder(
            borderRadius: const BorderRadius.all(const Radius.circular(10.0)),
            borderSide: const BorderSide(
              width: 1.0,
              color: const Color(0xffdddddd),
            ),
          ),
          enabledBorder: const OutlineInputBorder(
            borderRadius: const BorderRadius.all(const Radius.circular(10.0)),
            borderSide: const BorderSide(
              width: 1.0,
              color: const Color(0xffdddddd),
            ),
          ),
          errorBorder: const OutlineInputBorder(
            borderRadius: const BorderRadius.all(const Radius.circular(10.0)),
            borderSide: const BorderSide(
              width: 1.0,
              color: kColorError,
            ),
          ),
          focusedBorder: const OutlineInputBorder(
            borderRadius: const BorderRadius.all(const Radius.circular(10.0)),
            borderSide: const BorderSide(
              width: 1.0,
              color: kColorPrimary,
            ),
          ),
        ),
      ),
      child: child,
    );
  }

  Widget _close() {
    return Align(
      alignment: Alignment.topRight,
      child: IconButton(
        icon: DecoratedBox(
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.close,
            color: Color(0xFF707070),
          ).paddingAll(4),
        ),
        onPressed: () {
          Get.back();
        },
      ).paddingSymmetric(
        vertical: 8,
        horizontal: 16,
      ),
    );
  }

  Widget _list(final ScrollController scrollController) {
    return ClipRRect(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(30.0),
      ),
      child: ColoredBox(
        color: Colors.white,
        child: Form(
          key: _formKey,
          child: CustomScrollView(
            controller: scrollController,
            slivers: [
              SizedBox(
                height: 20,
              ).sliverBox,
              Center(
                child: Text(
                  '下列項目為純標記，方便訂單辨識及統計金額',
                  style: const TextStyle(
                    fontSize: 14.0,
                    color: OKColor.Gray33,
                  ),
                ),
              ).sliverBox,
              SizedBox(
                height: 10,
              ).sliverBox,
              // _header().sliverBox,
              _hint().sliverBox,
              SizedBox(
                height: 12,
              ).sliverBox,
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: kPadding,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '支付方式',
                        style: TextStyle(
                          fontSize: 18,
                          color: OKColor.Gray66,
                          fontWeight: FontWeight.w700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        '支付金額',
                        style: TextStyle(
                          fontSize: 18,
                          color: OKColor.Gray66,
                          fontWeight: FontWeight.w700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ).sliverBox,
              SizedBox(
                height: 12,
              ).sliverBox,
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (content, index) {
                    return Obx(() {
                      final data = controller.list.elementAt(index);
                      return _Item(
                        data: data,
                        onValueChanged: (value) {
                          data.multiplePayment.money = num.tryParse(value) ?? 0;
                          // 排除現金
                          if (!data
                              .settingPay.payMethodId.appPayMethod.isCash) {
                            controller.customRefresh();
                          }
                          // 輸入有變動時更新
                          controller.list.refresh();
                        },
                      );
                    });
                  },
                  childCount: controller.list.length,
                ),
              ),
              SizedBox(
                height: kBottomButtonPadding,
              ).sliverBox,
            ],
          ),
        ),
      ),
    );
  }

  Widget _hint() {
    return Obx(() {
      return Visibility(
        // visible: true,
        visible: controller.needShowHint,
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: kPadding,
          ),
          child: DecoratedBox(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15.0),
              // color: Colors.white,
              border: Border.all(
                width: 1.0,
                color: OKColor.Primary,
              ),
            ),
            child: Text(
              // '須找零：330',
              controller.hintText ?? '',
              style: TextStyle(
                fontSize: 24,
                color: OKColor.Error,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.center,
            ).paddingSymmetric(
              vertical: 8,
            ),
          ),
        ),
      );
    });
  }

  // Widget _header() {
  //   return Padding(
  //     padding: EdgeInsets.symmetric(
  //       horizontal: kPadding,
  //     ),
  //     child: DecoratedBox(
  //       decoration: BoxDecoration(
  //         borderRadius: BorderRadius.circular(15.0),
  //         border: Border.all(
  //           width: 1.0,
  //           color: const Color(0xffe0a471),
  //         ),
  //       ),
  //       child: Column(
  //         mainAxisSize: MainAxisSize.min,
  //         children: [
  //           SizedBox(
  //             height: 18,
  //           ),
  //           Obx(() {
  //             return Text(
  //               // '商品總價：\$1670',
  //               '商品總價：${controller.total.displayCurrency}',
  //               style: TextStyle(
  //                 fontSize: 20,
  //                 color: const Color(0xff666666),
  //               ),
  //               textAlign: TextAlign.center,
  //             );
  //           }),
  //           SizedBox(
  //             height: 16,
  //           ),
  //           Obx(() {
  //             return Text(
  //               // '剩餘金額：\$670',
  //               '剩餘金額：${controller.remain.displayCurrency}',
  //               style: TextStyle(
  //                 fontSize: 24,
  //                 color: const Color(0xffe0a471),
  //                 fontWeight: FontWeight.w700,
  //               ),
  //               textAlign: TextAlign.center,
  //             );
  //           }),
  //           SizedBox(
  //             height: 16,
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }
}

class _Item extends StatelessWidget {
  final Pay data;
  final ValueChanged<String> onValueChanged;
  final moneyEditing = TextEditingController();
  final commentEditing = TextEditingController();

  _Item({
    Key key,
    this.data,
    this.onValueChanged,
  }) : super(key: key) {
    moneyEditing.text = data.multiplePayment.displayMoney;
    // set cursor position at the end of the value
    moneyEditing.selection = TextSelection.fromPosition(
        TextPosition(offset: moneyEditing.text.length));
    commentEditing.text = data.multiplePayment.other;
  }

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: const BoxDecoration(
        border: const Border(
          bottom: const BorderSide(
            width: 1.0,
            color: OKColor.GrayDD,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _input(),
          _comment(),
        ],
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 8,
      ),
    );
  }

  Widget _input() {
    return Row(
      children: [
        PaymethodItem(
          title: data.settingPay.name,
          image: data.settingPay.payMethodId.appPayMethod.icon,
          showTitle: data.settingPay.isCustom ?? false,
        ),
        SizedBox(
          width: 16,
        ),
        Expanded(
          child: CustomEditor.number(
            scrollPadding: EdgeInsets.only(
              bottom: 100,
            ),
            decoration: InputDecoration(
              hintText: '支付金額',
            ),
            controller: moneyEditing,
            onChanged: onValueChanged,
            // onChanged: (value) {
            //   data.multiplePayment.money = num.tryParse(value) ?? 0;
            // },
          ),
        ),
      ],
    );
  }

  Widget _comment() {
    return Visibility(
      visible: data.settingPay.payMethodId.appPayMethod.isCreditCard,
      child: CustomEditor.number(
        scrollPadding: EdgeInsets.only(
          bottom: 100,
        ),
        decoration: InputDecoration(
          hintText: '請輸入信用卡末4碼',
        ),
        controller: commentEditing,
        onChanged: (value) {
          data.multiplePayment.other = value;
        },
        inputFormatters: <TextInputFormatter>[
          LengthLimitingTextInputFormatter(4),
        ],
        validator: (value) {
          if (data.settingPay.payMethodId.appPayMethod.isCreditCard &&
              data.multiplePayment.money != null &&
              data.multiplePayment.money > 0) {
            // 信用卡必填項目
            if (value == null || value.isEmpty) {
              return '請填上信用卡末四碼，以方便財務對帳！';
            }
            if (!GetUtils.hasMatch(value, r'^\d{4}$')) {
              return '格式錯誤，填入四位數字';
            }
            return null;
          }
          if (value == null || value.isEmpty) {
            // 允許空值
            return null;
          }
          if (value != null && value.isNumericOnly) {
            return null;
          }
          return '輸入數字';
        },
      ).paddingOnly(
        top: 8,
      ),
    );
  }
}
