import 'dart:async';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/other/setting_pay.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/extension.dart';

class SettingPayController extends GetxController
    with StateMixin<String>, ScrollMixin {
  final ApiProvider apiProvider;
  final _disposable = Completer();

  PrefProvider get prefProvider => apiProvider.prefProvider;

  final draft = <SettingPay>[].obs;

  SettingPayController({
    @required this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
    // 監聽設定值
    prefProvider.settingPayStream().takeUntil(_disposable.future).listen(
      (event) {
        final list = event
            .where((element) => element.status.switcher.isOn)
            .toList(growable: false);
        draft.assignAll(list);
        super.change('', status: RxStatus.success());
      },
    );
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    try {
      final value = await apiProvider.getSettingPay();
      prefProvider.settingPay = value;
      final list = value
          .where((element) => element.status.switcher.isOn)
          .toList(growable: false);
      draft.assignAll(list);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<bool> submit() async {
    try {
      final ret = await apiProvider.postSettingPaySort(draft);
      if (true == ret) {
        // update local data
        prefProvider.settingPay = await apiProvider.getSettingPay();
      }
      return ret;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> onEndScroll() async {
    // TODO: implement onEndScroll
    // throw UnimplementedError();
  }

  @override
  Future<void> onTopScroll() async {
    // TODO: implement onTopScroll
    // throw UnimplementedError();
  }
}
