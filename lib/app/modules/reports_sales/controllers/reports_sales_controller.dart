import 'dart:async';
import 'package:flutter/foundation.dart' show required;
import 'package:flutter_sunmi_printer/flutter_sunmi_printer.dart' as Sunmi;

import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:okshop_esc_pos/okshop_esc_pos.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/other/reports_sales.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';

class ReportsSalesController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final PrinterProvider printerProvider;
  ApiProvider get apiProvider => printerProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  Logger get logger => apiProvider.logger;

  final _now = DateTime.now().obs;
  DateTime get now => _now.value;

  final _data = ReportsSales().obs;
  ReportsSales get data => _data.value;

  final _storeType = StoreType.Dinner.obs;
  StoreType get storeType => _storeType.value;

  final _dateTime = Rx<DateTime>(null);
  DateTime get dateTime => _dateTime.value;
  set dateTime(DateTime value) => _dateTime.value = value;

  ReportsSalesController({
    @required this.printerProvider,
  });

  @override
  void onInit() {
    super.onInit();
    _dateTime.stream
        .combineLatest(_storeType.stream, (x, y) => '')
        .tap((event) {
          change('', status: RxStatus.loading());
        })
        .asyncMap((event) => _getData())
        .takeUntil(_disposable.future)
        .listen((event) {});
    // 初始時間
    _now.value = DateTime.now();
    dateTime = _now.value;
    // 初始商店型態
    if (prefProvider.setting.checkoutType.containsDinner) {
      _storeType.value = StoreType.Dinner;
    } else {
      _storeType.value = StoreType.Retail;
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> _getData() async {
    try {
      _data.value =
          await apiProvider.getReportsSales(dateTime, _storeType.value);
      _now.value = DateTime.now();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<void> onRefresh() async {
    try {
      await apiProvider.getReportsSalesRefresh(dateTime);
    } finally {
      await _getData();
    }
  }

  void toggleStoreType() {
    if (storeType.isDinner) {
      if (prefProvider.setting.checkoutType.containsRetail) {
        _storeType.value = StoreType.Retail;
      }
    } else {
      if (prefProvider.setting.checkoutType.containsDinner) {
        _storeType.value = StoreType.Dinner;
      }
    }
  }

  bool get hasMultipleStoreType {
    return prefProvider.setting.checkoutType.storeTypeCount > 1;
  }

  Future<bool> print() async {
    try {
      final res = await apiProvider.postReportsSalesPrint(now);
      logger.i(res);
    } catch (e) {
      logger.e(e);
    }
    final printData = data.asPrintSales(
      title: '${storeType.name}${dateTime.yMd}',
      date: now.yMdHms,
      storeName: prefProvider.brandsInfo.name,
      user: prefProvider.jwt.name,
    );
    // 商米V2印表機
    await Sunmi.SunmiUtil.printReportsSales(printData);
    // 找出包含商品統計分類的印表機
    final printers = printerProvider.getPrinterWithCategories(
        [PrintType.reportSales.value]).where((element) {
      return element.status.switcher.isOn &&
          element.type == PrinterType.net.value;
    });
    // 列印當日商品銷售統計
    for (var printer in printers) {
      await printer.printReportsSales(printData);
    }
    return true;
  }
}

extension ExtensionReportsSales on ReportsSales {
  PrintSales asPrintSales({
    String title,
    String date,
    String storeName,
    String user,
  }) {
    return PrintSales(
      title: title,
      subject: '當日商品銷售統計',
      date: date,
      storeName: storeName,
      user: user,
      app: app.map((e) => App.fromJson(e.toJson())).toList(),
      online: online.map((e) => App.fromJson(e.toJson())).toList(),
      appTotal: appTotal,
      appQuantity: appQuantity,
      onlineTotal: onlineTotal,
      onlineQuantity: onlineQuantity,
    );
  }

  num get appCount {
    app ??= [];
    return app.length;
  }

  num get appQuantity {
    app ??= [];
    return app.fold<num>(
        0, (previousValue, element) => previousValue + (element.quantity ?? 0));
  }

  num get appTotal {
    app ??= [];
    return app.fold<num>(
        0, (previousValue, element) => previousValue + (element.total ?? 0));
  }

  num get onlineCount {
    online ??= [];
    return online.length;
  }

  num get onlineQuantity {
    online ??= [];
    return online.fold<num>(
        0, (previousValue, element) => previousValue + (element.quantity ?? 0));
  }

  num get onlineTotal {
    online ??= [];
    return online.fold<num>(
        0, (previousValue, element) => previousValue + (element.total ?? 0));
  }

  GroupArgs asAppSales() {
    return GroupArgs(
      title: '門市銷售',
      items: app,
      total: appTotal,
      count: appCount,
      quantity: appQuantity,
    );
  }

  GroupArgs asOnlineSales() {
    return GroupArgs(
      title: 'LINE銷售',
      items: online,
      total: onlineTotal,
      count: onlineCount,
      quantity: onlineQuantity,
    );
  }
}

class GroupArgs {
  final String title;
  final List<App> items;
  final num total;
  final num count;
  final num quantity;

  GroupArgs({
    this.title,
    this.items,
    this.total,
    this.count,
    this.quantity,
  });
}
