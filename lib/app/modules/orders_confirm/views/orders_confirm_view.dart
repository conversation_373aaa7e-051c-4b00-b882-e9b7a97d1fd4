import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/bottom_wrapper.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/orders_post_req_edit_form.dart';
import 'package:muyipork/app/components/square_toggle.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/yes_no_button.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/enums.dart';

import '../controllers/orders_confirm_controller.dart';

class OrdersConfirmView extends GetView<OrdersConfirmController> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Get.focusScope.unfocus();
      },
      child: StandardPage(
        backgroundColor: controller.prefProvider.themeColor,
        titleText: controller.title,
        child: controller.obx((state) {
          return BottomWidgetPage(
            child: Obx(() => _main()),
            bottom: _bottom(),
          );
        }),
      ),
    );
  }

  Widget _bottom() {
    return BottomWrapper(
      child: YesNoButton(
        leftButtonText: '上一步',
        rightButtonText: '下一步',
        onLeftPressed: () => Get.back(),
        onRightPressed: _submit,
      ),
    );
  }

  Future<void> _submit() async {
    try {
      if (controller.validate()) {
        final needCheckout = controller.needToCheckout;
        final future = needCheckout ? _submitBeforeMeal : _submitAfterMeal;
        await future();
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  Future<void> _submitBeforeMeal() async {
    //Proceed to ORDERS_SUM_UP page.
    await Get.toNamed(
      Routes.ORDERS_SUM_UP,
      arguments: controller.draft,
      parameters: <String, String>{
        Keys.Tag: '${DateTime.now().millisecondsSinceEpoch}',
      },
    );
    //也有可能被上一步弄回來
    controller.refreshDraft();
  }

  Future<void> _submitAfterMeal() async {
    final ret = await FutureProgress<bool>(
      future: controller.submitAfterMeal(),
    ).dialog<bool>();
    if (true == ret) {
      Get.until((route) => Get.currentRoute == Routes.HOME);
    }
  }

  Widget _main() {
    final children = <Widget>[];
    //3 tabs
    children.add(_toggleTabs());

    children.add(Divider(height: 32));

    children.add(OrdersPostReqEditForm(
      apiProvider: controller.apiProvider,
      draft: controller.draft,
    ));

    return ListView(
      padding: EdgeInsets.only(
        top: kPadding,
        left: kPadding,
        right: kPadding,
        bottom: kBottomButtonPadding,
      ),
      children: children,
    );
  }

  // 0: 餐飲: 內用
  // 1: 餐飲: 店內->外帶 / 餐飲: 線上->自取
  // 2: 餐飲: 外送
  // 3: 零售: 自取
  // 4: 零售: 宅配
  // 5: 零售: 超商
  Widget _toggleTabs() {
    final children = <Widget>[];
    children.addAllIf(
      controller.draft.orderType.isDinner,
      _dinnerToggle(),
    );
    children.addAllIf(
      controller.draft.orderType.isRetail,
      _retailToggle(),
    );
    return Container(
      height: kMinInteractiveDimension,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        mainAxisSize: MainAxisSize.max,
        children: children,
      ),
    );
  }

  Iterable<Widget> _dinnerToggle() {
    final children = <Widget>[];

    children.addIf(
      controller.draft.orderType.isDinner,
      Expanded(
        child: SquareToggle<OrderType>(
          value: OrderType.DinnerHere,
          text: '內用',
          backgroundColor: controller.prefProvider.themeColor,
          checked: controller.draft.type.orderType.isDinnerHere,
          onPressed: (value) {
            controller.draft.type = value.index;
            controller.refreshDraft();
          },
        ),
      ),
    );
    children.addIf(true, SizedBox(width: 8));
    // 1 餐飲: 店內->外帶 / 餐飲: 線上->自取
    children.addIf(
      controller.draft.orderType.isDinner,
      Expanded(
        child: SquareToggle<OrderType>(
          value: OrderType.DinnerToGo,
          text: '外帶',
          backgroundColor: controller.prefProvider.themeColor,
          checked: controller.draft.type.orderType.isDinnerToGo,
          onPressed: (value) {
            controller.draft.type = value.index;
            controller.refreshDraft();
          },
        ),
      ),
    );
    children.addIf(true, SizedBox(width: 8));
    // 2 餐飲: 外送
    children.addIf(
      controller.draft.orderType.isDinner,
      Expanded(
        child: SquareToggle<OrderType>(
          value: OrderType.DinnerDelivery,
          text: '外送',
          backgroundColor: controller.prefProvider.themeColor,
          checked: controller.draft.type.orderType.isDinnerDelivery,
          onPressed: (value) {
            controller.draft.type = value.index;
            controller.refreshDraft();
          },
        ),
      ),
    );
    return children;
  }

  Iterable<Widget> _retailToggle() {
    final children = <Widget>[];
    children.addIf(
      controller.draft.orderType.isRetail,
      Expanded(
        child: SquareToggle<OrderType>(
          value: OrderType.RetailToGo,
          text: '自取',
          backgroundColor: controller.prefProvider.themeColor,
          checked: controller.draft.type.orderType.isRetailToGo,
          onPressed: (value) {
            controller.draft.type = value.index;
            controller.refreshDraft();
          },
        ),
      ),
    );
    children.addIf(true, SizedBox(width: 8));
    children.addIf(
      controller.draft.orderType.isRetail && controller.deliveryEnabled,
      Expanded(
        child: SquareToggle<OrderType>(
          value: OrderType.RetailDelivery,
          text: '宅配',
          backgroundColor: controller.prefProvider.themeColor,
          checked: controller.draft.type.orderType.isRetailDelivery,
          onPressed: (value) async {
            controller.draft.type = value.index;
            controller.refreshDraft();
          },
        ),
      ),
    );
    children.addIf(false, SizedBox(width: 8));
    children.addIf(
      false,
      Expanded(
        child: SquareToggle<OrderType>(
          value: OrderType.RetailInStore,
          text: '超取',
          backgroundColor: controller.prefProvider.themeColor,
          checked: controller.draft.type.orderType.isRetailInStore,
          onPressed: (value) {
            controller.draft.type = value.index;
            controller.refreshDraft();
          },
        ),
      ),
    );
    return children;
  }
}
