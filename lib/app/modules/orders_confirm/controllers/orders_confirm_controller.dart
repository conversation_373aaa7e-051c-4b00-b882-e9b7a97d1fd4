import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_sunmi_printer/flutter_sunmi_printer.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:muyipork/app/providers/table_provider.dart';
import 'package:okshop_esc_pos/okshop_esc_pos.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/other/coupon.dart';
import 'package:muyipork/app/data/models/other/local_settings.dart';
import 'package:muyipork/app/providers/coupon_provider.dart';
import 'package:muyipork/app/providers/product_provider.dart';
import 'package:muyipork/app/modules/receipt_sticker_printing/controllers/receipt_sticker_printing_controller.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/enums.dart';
import 'package:intl/intl.dart';
import 'package:muyipork/app/data/models/req/orders_post_req.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/extension.dart';

class OrdersConfirmArgs {
  OrdersConfirmArgs({
    @required this.ordersPostReq,
    @required this.kind,
  });
  OrdersPostReq ordersPostReq;

  // 類型 ProductKind
  // 0: 餐飲店內
  // 1: 餐飲線上 (這個在此處應該是不會有)
  // 2: 零售
  final int kind;
}

class OrdersConfirmController extends GetxController with StateMixin<String> {
  final ProductProvider productProvider;
  final PrinterProvider printerProvider;
  final OrderProvider orderProvider;
  final CouponProvider couponProvider;
  final TableProvider tableProvider;
  ApiProvider get apiProvider => productProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;

  //正在編輯當中準備要送出的 Orders 結構
  final _draft = OrdersPostReq().obs;
  OrdersPostReq get draft => _draft.value;
  final _disposable = Completer();
  // 優惠券
  final _coupon = Rx<Coupon>(null);
  Coupon get coupon => _coupon.value;

  // 判斷零售外送開關
  bool get deliveryEnabled {
    return prefProvider.shippingDelivery.enabled;
  }

  void refreshDraft() {
    _draft.refresh();
  }

  String get title {
    switch (draft.type.orderType.storeType) {
      case StoreType.Dinner:
        return '取餐方式/時間';
      case StoreType.Retail:
        return '取貨方式/時間';
      default:
        return '';
    }
  }

  OrdersConfirmController({
    @required this.productProvider,
    @required this.orderProvider,
    @required this.couponProvider,
    @required this.printerProvider,
    @required this.tableProvider,
  });

  ///
  /// 清除優惠券
  ///
  void resetCoupon() {
    draft.memberCouponId = null;
    draft.memberCouponDiscount = null;
    draft.memberCouponExtraPrice = null;
    _coupon.value = null;
    refreshDraft();
  }

  Future<ShippingMethod> _getShippingMethod() async {
    // 取得所有產品的 id
    final productIds = draft.normalItems.map((e) => e.productId).toSet();
    // 取得產品運送方式
    final shippingType = await productProvider.getShippingType(productIds);
    // 取得訂單運送方式
    return draft.getShippingMethodFromShippingType(shippingType);
  }

  @override
  void onInit() {
    super.onInit();
    if (Get.arguments is OrdersConfirmArgs) {
      final args = Get.arguments as OrdersConfirmArgs;
      _draft.value = args.ordersPostReq;
      switch (args.kind.productKind) {
        case ProductKind.DinnerApp: // 餐飲 offline
          // 預設內用
          draft.type = OrderType.DinnerHere.index;
          break;
        case ProductKind.Retail: // 零售
          // 預設自取
          draft.type = OrderType.RetailToGo.index;
          break;
        default:
      }
    }

    final draftStream = _draft.stream.asBroadcastStream();
    // update shipping method
    draftStream
        .map((event) => event.type)
        .distinct()
        .asyncMap((event) => _getShippingMethod())
        .takeUntil(_disposable.future)
        .listen((event) {});

    draftStream
        .map((event) => event.memberCouponId)
        .distinct()
        .asyncMap((event) {
          if (event != null) {
            return couponProvider.getMemberCoupon(
                draft.memberId, draft.memberCouponId);
          } else {
            resetCoupon();
          }
        })
        .takeUntil(_disposable.future)
        .listen((event) {
          if (event == null || event.id == null) {
            resetCoupon();
          } else {
            _coupon.value = event;
            // FIXME: 計算優惠
          }
        });
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    try {
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<void> _printOrder(num id) async {
    if (id <= 0) {
      throw 'order id($id) invalid';
    }
    // 取得本地端設定
    prefProvider.localSettings ??= LocalSettings();
    final autoPrinter =
        prefProvider.localSettings.getAutoPrint(prefProvider.storeType);
    // 明細
    if (autoPrinter.receiptEnabled == true) {
      try {
        await _printReceipt(id);
      } catch (e) {
        // logger.e(e);
      }
    }
    // 貼紙
    if (autoPrinter.stickerEnabled == true) {
      try {
        await _printSticker(id);
      } catch (e) {
        // logger.e(e);
      }
    }
  }

  Future<void> _printSticker(num id) async {
    await Get.toNamed(
      Routes.RECEIPT_STICKER_PRINTING,
      parameters: {Keys.Tag: '$id'},
      arguments: PrintingArgs(
        printingMode: PrintingMode.PrintAllAndLeave,
        printingPageSetting: PrintingPageSetting.StickerOnly,
        orderId: id,
      ),
    );
  }

  Future<void> _printReceipt(num id) async {
    final order = await orderProvider.getOrderDetail(id, cacheFirst: true);
    final fee = prefProvider.setting?.data?.other?.fee;
    num servicePercentage = fee?.percent ?? 0;
    if (ServiceFeeType.None == fee?.type?.serviceFeeType) {
      servicePercentage = 0;
    }
    // 餐飲內用才需服務費，其他歸零
    if (!order.data.type.orderType.isDinnerHere) {
      servicePercentage = 0;
    }
    final brandsInfo = prefProvider.brandsInfo;
    final jwt = this.prefProvider.jwt;
    final receipt = order.asReceipt(
      storeName: brandsInfo.name,
      userName: jwt.name,
      servicePercentage: servicePercentage,
    );
    // 列印收據
    await _printReceiptItems(receipt);
  }

  Future<void> _printReceiptItems(Receipt receipt) async {
    // 商米印表機
    await SunmiUtil.printReceiptItems(receipt);
    // 找出印表機
    final printers = printerProvider.getPrinterWithCategories(
        [PrintType.receiptItem.value]).where((element) {
      if (element.status.switcher.isOff) {
        return false;
      }
      // 目前只有網路印表機才有作用，藍芽尚未實作
      if (element.type != PrinterType.net.value) {
        return false;
      }
      return true;
    });
    for (var printer in printers) {
      final bytes = await printer.receiptItemTask(receipt);
      printer.pushTask(bytes);
    }
  }

  // 是否已經選擇了合法的桌號
  bool get _containsTable {
    // 沒有編輯桌號，則略過檢查
    if (tableProvider.cached.isEmpty) {
      return true;
    }
    if (draft != null && draft.containsTable) {
      return true;
    }
    // 有設定桌號卻未選擇
    return false;
  }

  DateTime get _mealAtDateTime {
    if (draft != null && draft.mealAt != null) {
      return DateFormat('y-M-d H:m:s').parse(draft.mealAt);
    }
    return null;
  }

  //送出訂單
  //Return orderId if success.
  //Return -1 if any failed.
  Future<num> _postUnfinishedOrder() {
    // TODO: 前結帳加入計算服務費，暫時性處理 (列印時有顯示服務費另計)
    // _updateFee();
    // final setting = prefProvider.setting;
    // final percent = (setting?.data?.other?.fee?.percent ?? 0) * 0.01;
    //final type = setting.data.other.fee.type.serviceFeeType;
    //送出之前做整理一次，算出總金額以及該填的東西。
    //draft.muyiPorkSetUnfinished();
    draft.getTotal(
      coupon: coupon,
      settingOrderFee: prefProvider.settingData.settingOrderFee,
      amb: prefProvider.shippingDelivery.getAmb(draft.shippingMethod),
    );
    // 已確認
    draft.status = OrderStatus.Accepted.index;
    // 未結清
    draft.paymentStatus = PaymentStatus.Balance.index;
    return orderProvider.postOrders(draft);
  }

  // 後結帳
  Future<bool> submitAfterMeal() async {
    final orderId = await _postUnfinishedOrder();
    if (orderId is num && orderId > 0) {
      // 不需要更新，post 時已經更新了
      // await orderProvider.getOrderDetail(orderId);
      // 列印
      await _printOrder(orderId);
      return true;
    }
    return false;
  }

  bool validate() {
    if (draft.type.orderType.isDinnerHere) {
      // 餐飲內用: 判斷桌號
      if (!_containsTable) {
        throw '請選擇桌號';
      }
    }
    if (draft.type.orderType.isDinnerDelivery) {
      // 餐飲外送: 判斷取餐時間
      if (_mealAtDateTime == null) {
        throw '請選擇取餐時間';
      }
    }
    return true;
  }

  // 這段邏輯就是在檢查是否該走前結流程，有點 tricky
  // 這邊很特殊: 走前結還是後結流程實質上要看 checkoutType 與 StoreType 決定
  // 因 AfterDinnerWithRetail 須由 storeType 決定走前結帳或後結帳
  bool get needToCheckout {
    // 零售一定是前結帳
    if (prefProvider.storeType.isRetail) {
      return true;
    }
    // 餐飲判斷
    final beforeDinner = [
      BrandsType.BeforeDinner,
      BrandsType.BeforeDinnerWithRetail,
    ];
    if (beforeDinner.contains(prefProvider.brandsType)) {
      return true;
    }
    return false;
  }
}
