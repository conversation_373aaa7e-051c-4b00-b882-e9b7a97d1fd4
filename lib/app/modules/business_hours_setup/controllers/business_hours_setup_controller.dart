import 'dart:async';

import 'package:get/get.dart';
import 'package:muyipork/app/data/models/res/setting_get_res.dart';
import 'package:muyipork/app/providers/api_provider.dart';

class BusinessHoursSetupController extends GetxController
    with StateMixin<String> {
  final _disposable = Completer<void>();
  final ApiProvider apiProvider;

  final _draft = Rx<SettingGetRes>(null);
  SettingGetRes get draft => _draft.value;

  // 是否做過任何時間變更
  final _dataChanged = false.obs;
  bool get dataChanged => _dataChanged.value;

  BusinessHoursSetupController(this.apiProvider);

  @override
  void onInit() async {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    super.onClose();
  }

  Future<void> onRefresh() async {
    _draft.value = await apiProvider.getSetting(updateCache: true);
    // 確保有預設7天資料
    draft.ensureWeekDays();
    change('', status: RxStatus.success());
  }

  void makeDataChanged() {
    _dataChanged.value = true;
  }

  // 嘗試存 Setting
  Future<bool> submit() async {
    if (draft != null) {
      await apiProvider.putSetting(draft.data);
      apiProvider.prefProvider.setting = draft;
      return true;
    }
    return false;
  }
}
