import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/custom_tab.dart';
import 'package:muyipork/app/components/custom_tab_bar.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/settings_widget.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/data/models/other/setting_point.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

import '../controllers/setting_points_controller.dart';

class SettingPointsView extends GetView<SettingPointsController> {
  @override
  Widget build(BuildContext context) {
    return StandardPage(
      titleText: '會員積點',
      child: controller.obx((state) => _main()),
    );
  }

  Widget _main() {
    return DefaultTabController(
      length: controller.tabs.length,
      child: Column(
        children: [
          _header(),
          Expanded(
            // child: Obx(() => _page()),
            child: TabBarView(
              children: _pages(),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _pages() {
    return List.generate(controller.tabs.length, (index) {
      return Obx(() => _page(index));
    });
  }

  Widget _page(num index) {
    final data = controller.tabs.elementAt(index);
    return _Page(
      data: data,
      onEnableChanged: (value) {
        data.isRewardPoints = value.switcher.index;
        controller.tabs.refresh();
      },
      onValueChanged: (value) {
        data.cashRatio = num.tryParse(value) ?? 0;
      },
      onPressed: () async {
        try {
          await FutureProgress(future: controller.submit(data)).dialog<bool>();
        } on Exception catch (e) {
          Get.showAlert(e.toString());
        }
      },
      onRefresh: controller.onRefresh,
    );
  }

  Widget _header() {
    return Container(
      decoration: BoxDecoration(
        color: OKColor.Tab,
        boxShadow: [
          BoxShadow(
            color: const Color(0x29000000),
            offset: Offset(0, 0),
            blurRadius: 6,
          ),
        ],
      ),
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        vertical: 4.0,
      ),
      // child: _tabBar(),
      child: Obx(() {
        return CustomTabBar(
          themeColor: controller.prefProvider.themeColor,
          isScrollable: false,
          tabs: _tabs(),
          onTap: controller.index,
          indicatorPadding: EdgeInsets.only(
            top: 36,
            bottom: 4,
          ),
        );
      }),
    );
  }

  List<Widget> _tabs() {
    return controller.tabs
        .map((e) => CustomTab(titleText: e.type.storeType.name))
        .toList();
  }
}

class _Page extends StatelessWidget {
  final SettingPoint data;
  final ValueChanged<bool> onEnableChanged;
  final ValueChanged<String> onValueChanged;
  final Function onPressed;
  final AsyncCallback onRefresh;
  final TextEditingController _editing = TextEditingController();

  bool get _enabled {
    return data?.isRewardPoints?.switcher?.isOn ?? false;
  }

  String get _initialValue {
    final n = data?.cashRatio ?? 0;
    return n > 0 ? '${n.round()}' : '';
  }

  _Page({
    Key key,
    this.data,
    this.onEnableChanged,
    this.onValueChanged,
    this.onPressed,
    this.onRefresh,
  }) : super(key: key) {
    _editing.text = _initialValue;
  }

  @override
  Widget build(BuildContext context) {
    return BottomWidgetPage.save(
      onPressed: onPressed,
      child: RefreshIndicator(
        onRefresh: onRefresh,
        child: ListView(
          physics: const AlwaysScrollableScrollPhysics(),
          children: _list(),
        ),
      ),
    );
  }

  List<Widget> _list() {
    final ls = <Widget>[];
    ls.add(
      SettingsWidget.title(
        titleText: '會員積點幣值',
      ),
    );
    ls.add(_memo());
    ls.add(
      SettingsWidget.title(
        titleText: '發放條件設定',
      ),
    );
    ls.add(SettingsWidget.switcher(
      titleText: '消費點數發放',
      value: _enabled,
      onChanged: onEnableChanged,
    ));
    ls.addIf(!_enabled, SettingsWidget.space());
    ls.addIf(
        _enabled,
        SettingsWidget.input(
          controller: _editing,
          // initialValue: initialValue,
          contentPadding: EdgeInsets.only(
            left: kPadding * 2.0,
            right: kPadding,
          ),
          hintText: '請輸入消費金額',
          labelText: '消費多少等於1點',
          onChanged: onValueChanged,
          // enableSuggestions: false,
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            FilteringTextInputFormatter.singleLineFormatter,
          ],
        ));
    ls.addIf(
      _enabled,
      Container(
        width: double.infinity,
        padding: EdgeInsets.only(
          left: kPadding * 2.0,
          right: kPadding,
          bottom: 62,
        ),
        color: Colors.white,
        child: Text(
          '*此為點數發放比例。',
          style: TextStyle(
            fontSize: 14,
            color: const Color(0xffe00707),
          ),
          textAlign: TextAlign.left,
        ),
      ),
    );
    ls.add(SizedBox(
      height: kBottomButtonPadding,
    ));

    return ls;
  }

  Widget _memo() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: kPadding,
        vertical: kPadding,
      ),
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '系統預設每1點可折抵 NT.1 元',
            style: TextStyle(
              fontFamily: 'Helvetica Neue',
              fontSize: 16,
              color: const Color(0xff000000),
            ),
            textAlign: TextAlign.left,
          ),
          SizedBox(
            height: 4,
          ),
          Text(
            '*結帳預設積點可以全部折抵消費金額。',
            style: TextStyle(
              fontFamily: 'Helvetica Neue',
              fontSize: 14,
              color: const Color(0xffe00707),
            ),
            textAlign: TextAlign.left,
          ),
          SizedBox(
            height: 4,
          ),
          Text(
            '*積點永久有效，無期限限制。',
            style: TextStyle(
              fontFamily: 'Helvetica Neue',
              fontSize: 14,
              color: const Color(0xffe00707),
            ),
            textAlign: TextAlign.left,
          )
        ],
      ),
    );
  }
}
