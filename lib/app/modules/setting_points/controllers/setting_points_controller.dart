import 'dart:async';

import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/setting_point.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';

class SettingPointsController extends GetxController
    with StateMixin<String>, SingleGetTickerProviderMixin {
  final _disposable = Completer();
  final tabs = <SettingPoint>[].obs;
  final ApiProvider apiProvider;
  final index = 0.obs;
  final _data = <SettingPoint>[].obs;

  PrefProvider get prefProvider => apiProvider.prefProvider;

  SettingPoint _getData(StoreType storeType) {
    final ret = _data.firstWhere(
      (element) => element.type.storeType == storeType,
      orElse: () => SettingPoint(),
    );
    _data.addIf(ret.type == null, ret);
    ret.type = storeType.index;
    return ret;
  }

  SettingPointsController({
    this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    try {
      final value = await apiProvider.getSettingPoints();
      _data.assignAll(value);
      _ensureData();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<bool> submit(SettingPoint data) async {
    final res = await apiProvider.putSettingPoints(data);
    if (res == true) {
      prefProvider.settingPoint = _data;
    }
    return res;
  }

  ///
  /// server 可能會傳空資料，如果空則建立
  ///
  void _ensureData() {
    tabs.clear();
    final type = prefProvider.brandsType ?? BrandsType.Max;
    if (type.containsDinner) {
      tabs.add(_getData(StoreType.Dinner));
    }
    if (type.containsRetail) {
      tabs.add(_getData(StoreType.Retail));
    }
  }
}
