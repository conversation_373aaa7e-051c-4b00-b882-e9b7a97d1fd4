import 'dart:async';
import 'dart:convert';

import 'package:bpscm/bpscm.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:muyipork/app/data/models/other/coupons_req.dart';
import 'package:muyipork/app/data/models/req/addition_products_get_qry.dart';
import 'package:muyipork/app/data/models/req/kind_get_qry.dart';
import 'package:muyipork/app/data/models/res/order_root.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/app/providers/coupon_provider.dart';
import 'package:muyipork/app/providers/notification_provider.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:muyipork/app/providers/product_provider.dart';
import 'package:muyipork/app/providers/setting_provider.dart';
import 'package:muyipork/app/providers/table_provider.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/main.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/enums.dart';

enum Menu {
  Member, // 0: 會員
  PreOrder, // 1: 訂單
  Order, // 2: 訂單
  Setting, // 3: 設定
}

class HomeController extends SuperController<String> {
  final _disposable = Completer();
  final PrinterProvider printerProvider;
  final ProductProvider productProvider;
  final CouponProvider couponProvider;
  final TableProvider tableProvider;
  final SettingProvider settingProvider;
  final InvoiceProvider invoiceProvider;
  final NotificationProvider notificationProvider;
  ApiProvider get apiProvider => productProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  Logger get logger => boxProvider.logger;
  OrderProvider get orderProvider => notificationProvider.orderProvider;

  final _menu = Menu.Member.obs;
  Menu get menu => _menu.value;
  set menu(Menu value) => _menu.value = value;

  HomeController({
    @required this.notificationProvider,
    @required this.printerProvider,
    @required this.productProvider,
    @required this.couponProvider,
    @required this.tableProvider,
    @required this.settingProvider,
    @required this.invoiceProvider,
  });

  @override
  void onInit() {
    logger.d('HomeController - onInit');
    super.onInit();
    boxProvider.token = prefProvider.token;
    boxProvider.init().then((e) => orderProvider.init(_disposable));
    _initObservable();
  }

  @override
  void onReady() {
    logger.d('HomeController - onReady');
    super.onReady();
    prefProvider.userDefault.put(kKeyToken, prefProvider.token);
  }

  @override
  void onClose() {
    logger.d('HomeController - onClose');
    _disposable.complete();
    Future(() {
      try {
        boxProvider.close();
      } catch (e) {
        logger.e(e.message);
      }
    });
    super.onClose();
  }

  // Mandatory
  @override
  void onDetached() {
    logger.d('HomeController - onDetached called');
  }

  // Mandatory
  @override
  void onInactive() {
    logger.d('HomeController - onInative called');
  }

  // Mandatory
  @override
  void onPaused() {
    logger.d('HomeController - onPaused called');
  }

  // Mandatory
  @override
  void onResumed() {
    logger.d('HomeController - onResumed called');
  }

  // Optional
  @override
  Future<bool> didPushRoute(String route) {
    logger.d('HomeController - the route $route will be open');
    return super.didPushRoute(route);
  }

  // Optional
  @override
  Future<bool> didPopRoute() {
    logger.d('HomeController - the current route will be closed');
    return super.didPopRoute();
  }

  // Optional
  @override
  void didChangeMetrics() {
    logger.d('HomeController - the window size did change');
    super.didChangeMetrics();
  }

  // Optional
  @override
  void didChangePlatformBrightness() {
    logger.d('HomeController - platform change ThemeMode');
    super.didChangePlatformBrightness();
  }

  // Route onGenerateRoute(RouteSettings settings) {
  //   if (Routes.ORDERS == settings.name) {
  //     return GetPageRoute(
  //       settings: settings,
  //       page: () => OrdersView(),
  //       binding: OrdersBinding(),
  //     );
  //   }
  //   if (Routes.SETTINGS == settings.name) {
  //     return GetPageRoute(
  //       settings: settings,
  //       page: () => SettingsView(),
  //       binding: SettingsBinding(),
  //     );
  //   }
  //   return null;
  // }

  String get ordersViewTitle {
    switch (prefProvider.brandsType) {
      case BrandsType.BeforeDinner:
        return '線上訂單';
      case BrandsType.AfterDinner:
        return '點餐列表';
      case BrandsType.Retail:
        return '電商訂單';
      case BrandsType.BeforeDinnerWithRetail:
        if (prefProvider.storeType == StoreType.Dinner) {
          return '線上訂單';
        }
        return '電商訂單';
      case BrandsType.AfterDinnerWithRetail:
        if (prefProvider.storeType == StoreType.Dinner) {
          return '點餐列表';
        }
        return '電商訂單';
      default:
        return '';
    }
  }

  Future<void> onRefresh() async {
    // 初始推播設定
    try {
      await _setupInteractedMessage();
    } catch (e) {
      logger.e(e);
    }
    for (var box in boxes) {
      await boxProvider.initGsBox(box);
    }
    // 下載設定
    try {
      await apiProvider.getSetting(updateCache: true);
    } catch (e) {
      logger.e(e);
    }
    // 分類
    try {
      await productProvider.fetchAllCategories();
    } catch (e) {
      logger.e(e);
    }
    // 規格分類
    try {
      await productProvider.fetchAllAdditionCategories();
    } catch (e) {
      logger.e(e);
    }
    // 規格
    try {
      await productProvider.fetchAllAdditionProducts();
    } catch (e) {
      logger.e(e);
    }
    // 產品
    try {
      await productProvider.fetchAllProducts();
    } catch (e) {
      logger.e(e);
    }
    // 運費
    try {
      prefProvider.shippingDelivery =
          await settingProvider.getShippingDelivery();
    } catch (e) {
      logger.e(e);
    }
    try {
      // 取得積點設定
      prefProvider.settingPoint = await apiProvider.getSettingPoints();
    } catch (e) {
      logger.e(e);
    }
    // 以上必須要先讀取，用來顯示頁面用
    // 顯示頁面
    change('', status: RxStatus.success());
    // 以下可延後讀取
    // 取得折扣快取
    try {
      await couponProvider.getCouponsLike(CouponsReq(
        page: 1,
        limit: 500,
      ));
    } catch (e) {
      logger.e(e);
    }
    try {
      await apiProvider.pullAll();
    } catch (e) {
      logger.e(e);
    }
    try {
      // 取得印表機
      await printerProvider.getSettingLabels();
    } catch (e) {
      logger.e(e);
    }
    try {
      await tableProvider.getTables();
    } catch (e) {
      logger.e(e);
    }
    // 取得發票設定 (有 await 必須加上 try catch，否則會中斷)
    settingProvider.getBrandsInvoice();
    // 取得所有訂單
    try {
      logger.d('[HomeController] onRefresh: 取得所有訂單');
      orderProvider.fetchAllOrders();
    } catch (e) {
      logger.e(e);
    }
  }

  void _initObservable() {
    // 一秒檢查一次
    Stream.periodic(1.seconds)
        .tap((event) {
          final token = prefProvider.token;
          if (token == null || token.isEmpty) {
            // 登出後清除所有列印工作
            printerProvider.clearAllTasks();
          }
        })
        .asyncMap((event) => printerProvider.flushAll())
        .listen((event) {});
    final interval = Stream.periodic(1.minutes).asBroadcastStream();
    // 每分鐘檢查 token 時效，過期則設定 token 為空
    interval
        .where((event) {
          final jwt = prefProvider.jwt;
          // 暫時性 token 略過
          if (jwt.isTemporary) {
            return false;
          }
          return jwt.isExpired;
        })
        .takeUntil(_disposable.future)
        .listen((event) => prefProvider.token = '');
    // 每分鐘檢查 token 是否須更新
    interval
        .where((event) {
          final jwt = prefProvider.jwt;
          // 暫時性 token 略過
          if (jwt.isTemporary) {
            return false;
          }
          // 已過期 token 略過
          if (jwt.isExpired) {
            return false;
          }
          final remainInMinutes = jwt.remainingTime.inMinutes;
          logger.d('[HomeController] token 剩餘: $remainInMinutes 分鐘');
          // 如果 token 還有 3 分鐘就過期，就先更新。
          return remainInMinutes < 3;
        })
        .asyncMap((event) => apiProvider.getRenew())
        .takeUntil(_disposable.future)
        .listen(
          (event) {
            final jwt = prefProvider.jwt;
            final remainInMinutes = jwt.remainingTime.inMinutes;
            logger.d('[HomeController] token 已更新，剩餘: $remainInMinutes 分鐘');
          },
        );
    // 每分鐘執行檢查同步發票任務
    interval
        .asyncMap((event) => _syncInvoice())
        .takeUntil(_disposable.future)
        .listen((event) {});
    // 訂閱每分鐘同步最新訂單
    interval
        .asyncMap((event) => orderProvider.fetchLatestOrders())
        .takeUntil(_disposable.future)
        .listen((event) {});
    // token 串流，不同時才會送出事件
    final tokenStream = this
        .prefProvider
        .userDefault
        .watch(key: kKeyToken)
        .distinct((prev, next) => prev.value == next.value)
        .asBroadcastStream();
    // 非法 token，回到登入頁面
    tokenStream
        .where((event) {
          // 已經在登入頁面略過
          if (Routes.LOGIN == Get.currentRoute) {
            logger.d('ApiProvider - Current route is LOGIN');
            return false;
          }
          // token 不存在
          if (event.value == null || event.value == '') {
            logger.d('ApiProvider - token is empty');
            return true;
          }
          final jwt = prefProvider.jwt;
          if (jwt.isTemporary) {
            return true;
          }
          if (jwt.isExpired) {
            return true;
          }
          return false;
        })
        // 取消所有通知
        .asyncMap((event) =>
            notificationProvider.flutterLocalNotificationsPlugin.cancelAll())
        .asyncMap((event) => Get.offAllNamed(Routes.LOGIN))
        .takeUntil(_disposable.future)
        .listen((event) {});
    // 有效 token，取得設定值
    tokenStream
        .where((event) {
          if ('' == event?.value) {
            logger.d('ApiProvider - token is EMPTY');
            return false;
          }
          final jwt = this.prefProvider.jwt;
          // 暫時性 token 略過
          if (jwt.isTemporary) {
            return false;
          }
          // 已過期 token 略過
          if (jwt.isExpired) {
            return false;
          }
          return true;
        })
        .asyncMap((event) => onRefresh())
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  ///
  /// 同步發票狀態
  ///
  Future<void> _syncInvoice() async {
    final box = await boxProvider.getLazyBox(kKeyBoxOrderInvoice);
    for (var key in box.keys) {
      if (key is String) {
        final ls = key.split('.');
        final orderId = num.tryParse(ls.first);
        final action = num.tryParse(ls.last);
        final order = await orderProvider.getOrderDetail(orderId);
        final invoiceNumber = await box.get(key) as String;
        final req = GetInvoiceStatusReq(
          invoiceDate: order.checkoutAt.yMd,
          invoiceNo: invoiceNumber,
        );
        final status = await invoiceProvider.getInvoiceStatus(req);
        if (BpscmInvoiceStatus.Invoice.value == action) {
          // 上傳開立發票
          try {
            if (status.isUnknown) {
              final invoiceNumber = await _uploadInvoice(order);
              if (invoiceNumber != null && invoiceNumber.isNotEmpty) {
                await box.delete(key);
              }
            } else if (status.isInvoice || status.isCancel) {
              await box.delete(key);
            }
          } catch (e) {
            // 檢查
            final status = await invoiceProvider.getInvoiceStatus(req);
            if (status.isInvoice) {
              await box.delete(key);
            }
          }
        } else if (BpscmInvoiceStatus.Cancel.value == action) {
          // 上傳作廢發票
          try {
            if (status.isInvoice) {
              final invoiceNumber = await _uploadCancel(order);
              if (invoiceNumber != null && invoiceNumber.isNotEmpty) {
                await box.delete(key);
              }
            } else if (status.isCancel) {
              await box.delete(key);
            }
          } catch (e) {
            // 檢查
            final status = await invoiceProvider.getInvoiceStatus(req);
            if (status.isCancel) {
              // 字軌已作廢，回傳字軌
              await box.delete(key);
            }
          }
        }
      }
    }
  }

  ///
  /// 上傳開立發票
  ///
  Future<String> _uploadInvoice(OrderRoot order) async {
    final brandsInfo = prefProvider.brandsInfo;
    // 上傳金財通物件
    final data = order.asPosInvoiceNewsReq(
      apiKey: prefProvider.invoiceApiKey,
      taxId: brandsInfo.taxId,
      posBan: kPosBAN,
      storeName: brandsInfo.name,
      storeCode: brandsInfo.code,
    );
    // 更新品項稅率
    for (var element in data.invoiceDetails) {
      // 預設應稅
      var taxType = TaxType.TX;
      try {
        // 取得產品
        final product = await productProvider.getProductDetail(
          num.tryParse(element.relateNumber ?? '') ?? 0,
          cacheFirst: true,
        );
        // 取得產品的稅率 (預設應稅)
        taxType = product?.taxTypeEnum ?? TaxType.TX;
      } catch (e) {
        logger.e(e);
      }
      // 儲存稅別
      element.remark = '${taxType.bpscmType}';
    }
    // 計算稅率總和
    data.refresh();
    // 上傳發票
    final ret = await invoiceProvider.postPosInvoiceNewsSingle(data);
    if ('OK' == ret.status) {
      return ret.invoiceNo;
    }
    throw ret.message;
  }

  ///
  /// 上傳作廢發票
  ///
  Future<String> _uploadCancel(OrderRoot order) async {
    // 字軌
    final invoiceNumber = order?.data?.orderInvoice?.number ?? '';
    // 檢查字軌狀態
    final req = GetInvoiceStatusReq(
      invoiceDate: order.checkoutAt.yMd,
      invoiceNo: invoiceNumber,
    );
    final status = await invoiceProvider.getInvoiceStatus(req);
    if (status.isInvoice) {
      final orderDetail = order.data;
      final orderInvoice = orderDetail.orderInvoice;
      final brandsInfo = prefProvider.brandsInfo;
      // HACK:
      // final taxId = kPosBAN;
      final date = order.checkoutAt;
      // orderDetail.refundCreatedAt.
      final now = DateTime.now();
      final req = PosInvoiceEditsReq(
        posBan: kPosBAN,
        apiKey: prefProvider.invoiceApiKey,
        sellerBan: brandsInfo.taxId,
        storeCode: brandsInfo.code,
        storeName: brandsInfo.name,
        registerCode: '',
        orderNo: orderDetail.orderNumber,
        state: 2,
        invoiceNo: orderInvoice.number,
        invoiceDate: date.yMdHms,
        cancelDate: now.yMdHms,
        buyerBan: orderInvoice.vatNumber,
      );
      final ret = await invoiceProvider.postPosInvoiceEditsSingle(req);
      if ('OK' == ret.status) {
        return ret.invoiceNo;
      }
      throw ret.message;
    }
    return invoiceNumber;
  }

  Future<void> _setupInteractedMessage() async {
    try {
      // Get any messages which caused the application to open from
      // a terminated state.
      final initialMessage =
          await FirebaseMessaging.instance.getInitialMessage();

      // If the message also contains a data property with a "type" of "chat",
      // navigate to a chat screen
      if (initialMessage != null) {
        logger.i('initialMessage: $initialMessage');
        _handleMessage(initialMessage);
      }
    } catch (e) {
      logger.e(e.toString());
    }

    // Also handle any interaction when the app is in the background via a
    // Stream listener
    FirebaseMessaging.onMessageOpenedApp
        .takeUntil(_disposable.future)
        .listen((message) {
      logger.i('onMessageOpenedApp: $message');
      _handleMessage(message);
    });
    FirebaseMessaging.onMessage.takeUntil(_disposable.future).listen((message) {
      logger.i('onMessage: $message');
      _handleMessage(message);
    });
  }

  Future<void> _handleMessage(RemoteMessage message) async {
    logger.i('[HomeController] handleMessage: $message');
    // logger.i('[HomeController] handleMessage: ${message.senderId}');
    // logger.i('[HomeController] handleMessage: ${message.category}');
    // logger.i('[HomeController] handleMessage: ${message.contentAvailable}');
    // logger.i('[HomeController] handleMessage: ${message.messageId}');
    // logger.i('[HomeController] handleMessage: ${message.messageType}');
    // logger.i('[HomeController] handleMessage: ${message.mutableContent}');
    // logger.i('[HomeController] handleMessage: ${message.notification?.title}');
    // logger.i('[HomeController] handleMessage: ${message.notification?.body}');
    // logger.i('[HomeController] handleMessage: ${message.sentTime}');
    // logger.i('[HomeController] handleMessage: ${message.threadId}');
    // logger.i('[HomeController] handleMessage: ${message.ttl}');
    final from = message.from;
    // from: 44148148864
    logger.i('[HomeController] from: $from');
    final data = message.data;
    // data: {order_number: dL20240213000002, type: order, order_id: 9148}
    logger.i('[HomeController] data: $data');
    final dataString = jsonEncode(data);
    // dataString: {"order_number":"dL20240213000002","type":"order","order_id":"9148"}
    logger.i('[HomeController] dataString: $dataString');
    // final isAppierPush = await AppierFlutter.isAppierPush(dataString);
    if (message.data['type'] == 'order') {
      try {
        final orderId = num.tryParse(message.data['order_id']);
        await orderProvider.getOrderDetail(orderId);
      } catch (e) {
        logger.e(e);
      }
      // Navigator.pushNamed(
      //   context,
      //   '/chat',
      //   arguments: ChatArguments(message),
      // );
    }
    notificationProvider.showNotification(message);
  }
}
