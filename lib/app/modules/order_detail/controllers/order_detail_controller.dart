import 'dart:async';

import 'package:bpscm/bpscm.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_sunmi_printer/flutter_sunmi_printer.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/app/providers/line_pay_provider.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:okshop_esc_pos/okshop_esc_pos.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:muyipork/app/data/models/other/coupon.dart';
import 'package:muyipork/app/providers/coupon_provider.dart';
import 'package:muyipork/app/providers/member_provider.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:muyipork/app/providers/product_provider.dart';
import 'package:muyipork/enums.dart';
import 'package:screenshot/screenshot.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:muyipork/app/data/models/other/order_detail.dart';
import 'package:muyipork/app/data/models/req/orders_orderid_status_put_qry.dart';
import 'package:muyipork/app/data/models/req/orders_orderid_status_put_req.dart';
import 'package:muyipork/app/data/models/req/orders_post_req.dart';
import 'package:muyipork/app/data/models/res/orders_get_res.dart';
import 'package:muyipork/app/data/models/res/order_root.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

//幾個Shortcut返回狀態給 ordersView 處理 OrderDetail 上的按鈕事件。
enum OrderDetailViewShortCut {
  AcceptOrder, //接單
  RejectOrder, //棄單按鈕
  CheckoutOrder, //結帳按鈕
  Refresh, //更新頁面
}

class OrderDetailController extends GetxController with StateMixin<String> {
  final OrderProvider orderProvider;
  final CouponProvider couponProvider;
  final ProductProvider productProvider;
  final PrinterProvider printerProvider;
  final InvoiceProvider invoiceProvider;
  final LinePayProvider linePayProvider;

  MemberProvider get memberProvider => orderProvider.memberProvider;
  BoxProvider get boxProvider => memberProvider.boxProvider;
  ApiProvider get apiProvider => memberProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  final _id = 0.obs;
  final _disposable = Completer();
  //這是取得的原訂單資料
  final Rx<OrderRoot> _data = Rx<OrderRoot>(null);
  OrderRoot get data => this._data.value;
  //正在編輯當中準備要送出的 Orders 結構,由上面取得的 data 產生
  final _draft = Rx<OrdersPostReq>(null);
  OrdersPostReq get draft => _draft.value;
  // 優惠券
  final _coupon = Rx<Coupon>(null);
  Coupon get coupon => _coupon.value;

  // 發票相關
  final invoiceScreenshotController = ScreenshotController();
  final _widgetUpdater = Completer().obs;
  Completer get widgetUpdater => _widgetUpdater.value;

  final _invoice = Rx<Invoice>(null);
  Invoice get invoice => _invoice.value;

  // TODO: refactor me.
  void refreshDraft() {
    draft.getTotal(
      // 運費
      amb: prefProvider.shippingDelivery.getAmb(draft.shippingMethod),
      // 服務費
      settingOrderFee: prefProvider.settingData.settingOrderFee,
    );
    _draft.refresh();
  }

  //比對更新價格用
  // final productsGetRes = Rx<ProductsGetRes>(null);
  // final isFetchingProducts = false.obs;

  //編輯變動會讓這個變 true
  final hasEditFormChanged = false.obs;

  num get id => _id.value;
  set id(num value) => _id.value = value;

  num get redeemMemberPoints {
    final points = data?.data?.nnRedeemMemberPoints;
    return points.abs();
  }

  // String get redeemMemberPointsTitle {
  //   final online = data?.data?.orderDiner?.source?.orderSource?.isLine ?? false;
  //   return online ? '線上折抵：' : '積點折抵：';
  // }

  // num get couponDiscount => data.getDiscountPrice(DiscountType.Coupon) ?? 0;

  // String get displayTakeMethod {
  //   if (data.orderType.isDinner) {
  //     return '取餐方式：';
  //   }
  //   if (data.orderType.isRetail) {
  //     return '取貨方式：';
  //   }
  //   return '';
  // }

  /// 顯示獲得積點，條件
  /// 1. 有會員
  /// 2. 已完成單
  bool get pointGetVisible {
    final orderDetail = data.data;
    if (orderDetail == null) {
      return false;
    }
    if (orderDetail.memberId == null || orderDetail.memberId == 0) {
      return false;
    }
    if (!orderDetail.status.orderStatus.isArchived) {
      return false;
    }
    return true;
  }

  Future<num> get pointGet async {
    final query = MemberPointQry(
      memberId: data.data.memberId,
      orderId: data.data.id,
      type: PointType.Income.index,
    );
    final ls = await memberProvider.getMemberPoint(query);
    return ls.isEmpty ? 0 : ls.first.points;
  }

  // bool get showPrintButton {
  //   final condition1 = this.prefProvider.hasPrinter;
  //   final condition2 = this.productsGetRes.value != null;
  //   return condition1 && condition2;
  // }

  bool get hasCarrierId {
    return this.carrierId.isNotEmpty;
  }

  bool get hasNpoBan {
    return this.npoBan.isNotEmpty;
  }

  String get npoBan {
    return this.data.data.orderInvoice?.npoBan ?? '';
  }

  String get carrierId {
    return this.data.data.orderInvoice?.carrierId ?? '';
  }

  bool get printInvoiceEnabled {
    final orderInvoice = data.data?.orderInvoice ?? OrderInvoice();
    return orderInvoice.printable;
  }

  String get displayInvoiceStatus {
    return data?.data?.displayInvoiceStatus ?? '';
  }

  bool get hasInvoice {
    final orderDetail = data.data;
    final orderInvoice = orderDetail.orderInvoice;
    if (orderInvoice == null) {
      return false;
    }
    if (orderInvoice.number?.isNotEmpty ?? true) {
      return false;
    }
    return true;
  }

  num get invoiceStatus {
    final orderDetail = this.data.data;
    final orderInvoice = orderDetail.orderInvoice;
    return orderInvoice?.status ?? -1;
  }

  // Iterable<OrderItem> get normalItems {
  //   return draft?.normalItems ?? data?.normalItems ?? <OrderItem>[];
  // }

  // num get normalItemsCount {
  //   return normalItems.fold<num>(0, (previousValue, element) {
  //     return previousValue + element.nnQuantity;
  //   });
  // }

  // num get normalItemsPrice {
  //   return normalItems.fold<num>(0, (previousValue, element) {
  //     return previousValue + element.stackPrice;
  //   });
  // }

  OrderDetailController({
    @required this.orderProvider,
    @required this.invoiceProvider,
    @required this.couponProvider,
    @required this.productProvider,
    @required this.printerProvider,
    @required this.linePayProvider,
  });

  void _initObservable() {
    final dataStream = _data.stream.asBroadcastStream();
    dataStream
        .where((event) => event != null)
        .takeUntil(_disposable.future)
        .listen((event) {
      final it = event.data.orderDiscount.firstWhere(
        (element) => element.discountDescription != null,
        orElse: () => null,
      );
      if (it != null) {
        final jsonString = it.discountDescription.toRawJson();
        _coupon.value = Coupon.fromRawJson(jsonString);
      }
    });
    // 產生草稿
    dataStream
        .where((event) => event != null)
        .takeUntil(_disposable.future)
        .listen(
      (event) {
        _draft.value = data.asOrdersPutReq(productProvider: productProvider);
      },
    );
    // 訂單 id
    final idStream = _id.stream.asBroadcastStream();
    idStream
        .distinct()
        .skip(1)
        .tap((event) => change('', status: RxStatus.loading()))
        .skipWhile((event) {
          if (orderProvider.containsDetail(event)) {
            // 使用 cache
            logger.d(
                '[OrderDetailController] Order detail cache was found with with id($event)');
            _data.value = orderProvider.getOrderDetailFromLocalStorage(event);
            change('', status: RxStatus.success());
          }
          return true;
        })
        .takeUntil(_disposable.future)
        .listen((event) => onRefresh());
  }

  @override
  void onInit() {
    super.onInit();
    _initObservable();
    //需要商品列表資料
    // await reFetchProducts();

    //6/24 按下修改餐點當下才做新價格檢查更新。下方的兩個動作搬到按下修改餐點去做。

    // //做一次 ordersPostReq 內的商品列表比對，移除過時商品並且跳提示
    // if (orderEditable() && tryRemoveLegacyProducts()) {
    //   //跳提示
    //   DialogGeneral.show(
    //       DialogArgs(
    //         header: DialogGeneral.titleText('提醒'),
    //         contentIcon: DialogContentIcon.None,
    //         content: DialogGeneral.centerContentText('已移除部分過時商品!'),
    //         mainButtonText: '確認',
    //       ),
    //       barrierDismissible: true);
    //   //有移除掉過時商品
    //   hasEditFormChanged.value = true;
    // }
    //
    // //嘗試更新訂單內的商品價格 (只有未完成單才能被編輯，也才能更新價格)
    // if (orderEditable()) {
    //   if (tryUpdateProductPrice()) {
    //     DialogGeneral.show(
    //         DialogArgs(
    //           header: DialogGeneral.titleText('提醒'),
    //           contentIcon: DialogContentIcon.None,
    //           content: DialogGeneral.centerContentText('已更新使用最新的商品價格!'),
    //           mainButtonText: '確認',
    //         ),
    //         barrierDismissible: true);
    //     hasEditFormChanged.value = true;
    //   }
    // }
  }

  @override
  void onReady() {
    super.onReady();
    if (Get.parameters.containsKey(Keys.Id)) {
      _id.value = num.tryParse(Get.parameters[Keys.Id]) ?? 0;
    }
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    try {
      logger.d('[OrderDetailController] onRefresh: id($id)');
      _data.value = await orderProvider.getOrderDetail(id);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  //店內 Or 線上?
  int source() {
    if (data != null) {
      return data.data.orderDiner.source;
    }
    return 0;
  }

  //還原變更，其實就是是重新產生新的編輯資料
  revertChanges() {
    hasEditFormChanged.value = false;
    _data.refresh();
  }

  //這會嘗試移除所有的過時商品 (找不到 product ID 的)
  bool tryRemoveLegacyProducts() {
    bool hasRemovedOrderItem = false;
    // if (_draft.value != null && productsGetRes.value != null) {
    //   for (int i = _draft.value.items.length - 1; i >= 0; --i) {
    //     if (!productsGetRes.value
    //         .isProductExist(_draft.value.items[i].productId)) {
    //       //We need to remove this thing.
    //       _draft.value.items.removeAt(i);
    //       hasRemovedOrderItem = true;
    //     }
    //   }
    // }
    final ids = productProvider
        .getProductsFromStorage()
        .map((e) => e.productId)
        .toSet();
    draft.items ??= <OrderItem>[];
    draft.items.removeWhere((element) {
      final needToRemove = !ids.contains(element.productId);
      if (needToRemove) {
        hasRemovedOrderItem = true;
      }
      return needToRemove;
    });

    if (hasRemovedOrderItem) {
      //需要重算
      refreshDraft();
    }

    return hasRemovedOrderItem;
  }

  //嘗試搜尋價格差異並且更新
  bool tryUpdateProductPrice() {
    bool hasUpdateOneFinalPrice = false;

    draft.items ??= <OrderItem>[];
    for (var i = draft.items.length - 1; i >= 0; --i) {
      //先檢查主商品
      final orderItem = draft.items.elementAt(i);
      final product = productProvider.getProduct(orderItem.productId);
      final productPrice = product.price ?? 0;

      //這邊暫時先忽略 product 內是否有附屬品分類這件事，有需要再處理，因為目前附屬品分類並不是在 productsGetRes 可以獲得

      //再檢查附屬品
      num totalAdditionProductPrice = 0;
      if (_draft.value.items[i].productSpec1Ids != null) {
        for (int j = _draft.value.items[i].productSpec1Ids.length - 1;
            j >= 0;
            --j) {
          final ap = productProvider.getAdditionProductFromLocalStorage(
              draft.items[i].productSpec1Ids[j]);
          if (ap != null) {
            ap.price ??= 0;
            totalAdditionProductPrice += ap.price;
          } else {
            print('AdditionProduct [' +
                _draft.value.items[i].productSpec1Ids[j].toString() +
                '] not exist! Total price maybe updated!');
          }
        }
      }

      if (_draft.value.items[i].finalPrice !=
          productPrice + totalAdditionProductPrice) {
        //偵測到主商品有價格變動
        _draft.value.items[i].finalPrice =
            productPrice + totalAdditionProductPrice;
        hasUpdateOneFinalPrice = true;
      }
    }

    if (hasUpdateOneFinalPrice) {
      //需要重算
      refreshDraft();
    }

    return hasUpdateOneFinalPrice;
  }

  //目前沒這需求，但是我先做起來放著，這是在需要對話框徵詢同意更新價格用
  // //是否偵測到價格有變動?
  // bool hasUpdatedProductPrice() {
  //   bool hasUpdatedOrderItem = false;
  //
  //   if (ordersPostReq.value != null && productsGetRes.value != null) {
  //     for (int i = ordersPostReq.value.items.length - 1 ; i >= 0 ; --i) {
  //       //先檢查主商品
  //       int productPrice = 0;
  //       if (productsGetRes.value.isProductExist(ordersPostReq.value.items[i].productId)) {
  //         //取得現有主商品價格
  //         productPrice = productsGetRes.value.getProductPrice(ordersPostReq.value.items[i].productId);
  //       } else {
  //         //主商品不存在，因此已被變動
  //         hasUpdatedOrderItem = true;
  //       }
  //
  //       //這邊暫時先忽略 product 內是否有附屬品分類這件事，有需要再處理，因為目前附屬品分類並不是在 productsGetRes 可以獲得
  //
  //       //再檢查附屬品
  //       int totalAdditionProductPrice = 0;
  //       if (ordersPostReq.value.items[i].productSpec1IDs != null) {
  //         for (int j = ordersPostReq.value.items[i].productSpec1IDs.length - 1 ; j >= 0  ; --j) {
  //           AdditionProduct ap = apiProvider.tryGetAdditionProductInCache(ordersPostReq.value.source, ordersPostReq.value.items[i].productSpec1IDs[j]);
  //           if (ap != null) {
  //             totalAdditionProductPrice += ap.price;
  //           } else {
  //             //附屬品已不存在，因此已被變動
  //             hasUpdatedOrderItem = true;
  //           }
  //         }
  //       }
  //
  //       if (ordersPostReq.value.items[i].finalPrice != productPrice + totalAdditionProductPrice) {
  //         //偵測到主商品有價格變動
  //         hasUpdatedOrderItem = true;
  //       }
  //     }
  //   }
  //   return hasUpdatedOrderItem;
  // }

  Future<bool> submitReedit() async {
    final orderId = await orderProvider.putOrderReedit(id, draft);
    if (orderId is num && orderId > 0) {
      hasEditFormChanged.value = false;
      // 取得最新訂單
      _data.value =
          await orderProvider.getOrderDetail(orderId, cacheFirst: true);
      return true;
    }
    return false;
  }

  ///
  /// 變更訂單狀態
  ///
  Future<num> _putOrderStatus(OrderStatus status) async {
    data.subOrder ??= <OrderDetail>[];
    // 更新所有子訂單狀態
    for (var order in (data.subOrder ?? <OrderDetail>[])) {
      try {
        await orderProvider.putOrderStatus(
          order.id,
          OrdersOrderIdStatusPutQry(isPushMsg: Switcher.On.index),
          OrdersOrderIdStatusPutReq(status: status.index),
        );
      } catch (e) {
        logger.e(e);
      }
    }
    // 更新主訂單狀態
    return orderProvider.putOrderStatus(
      id,
      OrdersOrderIdStatusPutQry(
        isPushMsg: Switcher.On.index,
      ),
      OrdersOrderIdStatusPutReq(
        status: status.index,
      ),
    );
  }

  ///
  /// 補印發票
  ///
  Future<void> printInvoice() async {
    try {
      // 購買的相關產品
      final items = data.allOrderItems;
      final ids = items.map((e) => e.productId).toSet();
      final products = await _getProductSingles(ids);
      final brandsInfo = prefProvider.brandsInfo;
      final invoice = data.asInvoice();
      invoice.storeName = brandsInfo.name;
      invoice.printMark = Times.Again.index; // 補印
      invoice.seller = brandsInfo.taxId;
      // 設定商品稅別
      for (var item in invoice.items) {
        // 取得產品
        final index = products.indexWhere((element) {
          return element.id == item.productId;
        });
        // 取得產品的稅率 (預設應稅)
        var taxType =
            index >= 0 ? products.elementAt(index).taxTypeEnum : TaxType.TX;
        // 儲存稅別
        item.taxType = taxType.bpscmType;
      }
      invoice.refresh();
      await _printInvoice(invoice);
      return invoice;
    } catch (e) {
      // TODO: 移動到 view
      DialogGeneral.alert('$e').dialog();
    }
  }

  Future<void> _printInvoice(Invoice invoice) async {
    _widgetUpdater.value = Completer();
    // 設定內容
    _invoice.value = invoice;
    try {
      // 使用商米印表機列印
      await _printInvoiceByEsc(invoice);
    } catch (e) {
      logger.e(e);
    }
    try {
      // 使用 V2 列印
      await SunmiUtil.printInvoice(invoice);
    } catch (e) {
      logger.e(e);
    }
  }

  Future<void> _printInvoiceByEsc(Invoice invoice) async {
    logger.d(
        '[OrderDetailController] _printInvoiceByEsc: invoice(${invoice.invoiceNumber})');
    // 等待內容變更完成
    await widgetUpdater.future;
    final image = await invoiceScreenshotController.capture(
      pixelRatio: 1.0,
      delay: 200.milliseconds,
    );
    // 尋找 printers
    final ids = [PrintType.invoice.value];
    final printers = printerProvider.getPrinterWithCategories(ids);
    for (var printer in printers) {
      // 使用圖片方式列印 invoice
      final bytes = await printer.invoiceTask(invoice, image);
      printer.pushTask(bytes);
    }
  }

  // 退貨、退款
  Future<num> refund() async {
    // TODO: 暫時處理方式，因無判斷是否含 line pay 付款資訊，先執行 linepay 退款，再攔截錯誤訊息
    try {
      await linepayRefund();
    } catch (e) {
      logger.e(e);
    }
    // 作廢發票
    try {
      await _editInvoice();
    } catch (e) {
      logger.e(e);
    }
    // 標記訂單狀態
    final orderId = await _putOrderStatus(OrderStatus.Rejection);
    if (orderId is num && orderId > 0) {
      _data.value =
          await orderProvider.getOrderDetail(orderId, cacheFirst: true);
    }
    return orderId;
  }

  Future<void> linepayRefund() async {
    final ret = await linePayProvider.linePayRefund(id);
    final acceptCode = [
      "0000", // 正常
      "1150", // 沒有交易 id
      "1165", // 此筆交易已退款
    ];
    if (!acceptCode.contains(ret.returnCode)) {
      throw ret.returnMessage;
    }
  }

  // 作廢發票
  Future<void> _editInvoice() async {
    if (data.invoiceStatus.isInvoice) {
      final orderId = data.data.id;
      // 先標記作廢
      await orderProvider.putOrderInvoice(orderId);
      try {
        throw '強制背景上傳作廢發票';
        // 再上傳金財通
        final invoiceNumber = await uploadCancel();
      } catch (e) {
        // 作廢發票失敗，加入補上傳佇列
        // TODO: timeout 也會 catch 到，但可能成功，所以必須判斷狀態
        final box = await boxProvider.getLazyBox(kKeyBoxOrderInvoice);
        final key = '$orderId.${BpscmInvoiceStatus.Cancel.value}';
        final invoiceNumber = data.data.orderInvoice.number;
        await box.put(key, invoiceNumber);
      }
    }
  }

  // void checkOutOrder() {
  //   // TODO:
  //   OrderSummary orderSummary = data.data.asOrderSummary();
  //   if (orderSummary.type.orderType.isRetail) {
  //     Get.toNamed(
  //       Routes.ORDERS_SUM_UP,
  //       arguments: [orderSummary],
  //     );
  //   } else {
  //     //傳入整體清單以及過濾條件
  //     Get.toNamed(
  //       Routes.ORDERS_SELECTOR,
  //       arguments: OrdersSelectorArgs(
  //         sourceOrders: controller.orders,
  //         filter: OrdersSelectorFilter(
  //           id: orderSummary.id,
  //           table1Id: orderSummary.table1Id,
  //           table2Id: orderSummary.table2Id,
  //           table1Name: orderSummary.table1Name,
  //           table2Name: orderSummary.table2Name,
  //           sourceOrKind: orderSummary.source,
  //         ),
  //       ),
  //     );
  //   }
  // }

  Future<void> printReceiptLite(Receipt receipt) async {
    await SunmiUtil.printReceiptLite(receipt);
    // 找出印表機
    final printers = printerProvider
        .getPrinterWithCategories([PrintType.receiptLite.value]).where(
            (element) => element.status.switcher.isOn);
    printers
        .where((element) => element.type == PrinterType.net.value)
        .forEach((printer) async {
      await printer.printReceiptLite(receipt);
    });
  }

  Future<void> printReceiptItems(Receipt receipt) async {
    await SunmiUtil.printReceiptItems(receipt);
    // 找出印表機
    final printers = printerProvider
        .getPrinterWithCategories([PrintType.receiptItem.value]).where(
            (element) => element.status.switcher.isOn);
    printers
        .where((element) => element.type == PrinterType.net.value)
        .forEach((printer) async {
      // await printer.printReceiptItem(receipt);
      final bytes = await printer.receiptItemTask(receipt);
      printer.pushTask(bytes);
    });
  }

  ///
  /// 上傳作廢發票到金財通，成功時回傳字軌
  ///
  Future<String> uploadCancel() async {
    // HACK: 模擬上傳失敗
    // throw '模擬上傳失敗';
    final orderDetail = data.data;
    final orderInvoice = orderDetail.orderInvoice;
    final brandsInfo = prefProvider.brandsInfo;
    // HACK:
    // final taxId = kPosBAN;
    final date = data.checkoutAt;
    final now = DateTime.now();
    final req = PosInvoiceEditsReq(
      posBan: kPosBAN,
      apiKey: prefProvider.invoiceApiKey,
      sellerBan: brandsInfo.taxId,
      storeCode: brandsInfo.code,
      storeName: brandsInfo.name,
      registerCode: '',
      orderNo: orderDetail.orderNumber,
      state: 2,
      invoiceNo: orderInvoice.number,
      invoiceDate: date.yMdHms,
      cancelDate: now.yMdHms,
      buyerBan: orderInvoice.vatNumber,
    );
    final ret = await invoiceProvider.postPosInvoiceEditsSingle(req);
    if ('OK' == ret.status) {
      return ret.invoiceNo;
    }
    throw ret.message;
  }

  ///
  /// 上傳發票到金財通
  ///
  Future<PosInvoiceNewsRes> uploadInvoice() async {
    final order = await orderProvider.getOrderDetail(id, cacheFirst: true);
    final brandsInfo = prefProvider.brandsInfo;
    // 上傳金財通物件
    final data = order.asPosInvoiceNewsReq(
      apiKey: prefProvider.invoiceApiKey,
      taxId: brandsInfo.taxId,
      posBan: kPosBAN,
      storeName: brandsInfo.name,
      storeCode: brandsInfo.code,
    );
    // 更新品項稅率
    for (var element in data.invoiceDetails) {
      // 預設應稅
      var taxType = TaxType.TX;
      try {
        // 取得產品
        final product = await productProvider.getProductDetail(
          num.tryParse(element.relateNumber ?? '') ?? 0,
          cacheFirst: true,
        );
        // 取得產品的稅率 (預設應稅)
        taxType = product?.taxTypeEnum ?? TaxType.TX;
      } catch (e) {
        logger.e(e);
      }
      // 儲存稅別
      element.remark = '${taxType.bpscmType}';
    }
    // 計算稅率總和
    data.refresh();
    return invoiceProvider.postPosInvoiceNewsSingle(data);
  }

  Future<List<ProductSingle>> _getProductSingles(Iterable<num> ids) async {
    ids ??= <num>[];
    final futures = ids.map((id) {
      return productProvider.getProductDetail(id, cacheFirst: true);
    });
    return Future.wait(futures);
  }

  num get serviceCharges {
    return draft.fee ?? data.serviceCharges ?? 0;
  }
}
