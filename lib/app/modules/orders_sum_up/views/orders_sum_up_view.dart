import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/components/bottom_widget_page.dart';
import 'package:muyipork/app/components/bottom_wrapper.dart';
import 'package:muyipork/app/components/custom_editor.dart';
import 'package:muyipork/app/components/dialog_general.dart';
import 'package:muyipork/app/components/future_progress.dart';
import 'package:muyipork/app/components/icon_menu_item.dart';
import 'package:muyipork/app/components/invoice_widget.dart';
import 'package:muyipork/app/components/member_avatar.dart';
import 'package:muyipork/app/components/paymethod_item.dart';
import 'package:muyipork/app/components/qrcode_scanner.dart';
import 'package:muyipork/app/components/standard_page.dart';
import 'package:muyipork/app/components/coupon_item.dart';
import 'package:muyipork/app/components/yes_no_button.dart';
import 'package:muyipork/app/data/models/other/local_settings.dart';
import 'package:muyipork/app/data/models/other/member.dart';
import 'package:muyipork/app/data/models/other/multiple_payment.dart';
import 'package:muyipork/app/data/models/other/qr_format.dart';
import 'package:muyipork/app/data/models/req/orders_post_req.dart';
import 'package:muyipork/app/modules/multiple_payment/views/multiple_payment_view.dart';
import 'package:muyipork/app/modules/promotion_picker/views/promotion_picker_view.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/app/utils/upper_case_text_formatter.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/keys.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:screenshot/screenshot.dart';

import '../controllers/orders_sum_up_controller.dart';
import 'orders_confirm.dart';

class OrdersSumUpView extends GetView<OrdersSumUpController> {
  // @override
  // final String tag;

  OrdersSumUpView({Key key})
      : // tag = Get.parameters[Keys.Tag],
        super(key: key) {
    // Get.lazyPut(
    //   () => OrdersSumUpController(
    //     productProvider: Get.find(),
    //     memberProvider: Get.find(),
    //     invoiceProvider: Get.find(),
    //     orderProvider: Get.find(),
    //     couponProvider: Get.find(),
    //     printerProvider: Get.find(),
    //   ),
    //   tag: tag,
    // );
  }

  Iterable<Widget> _actions() sync* {
    // 掃描會員及優惠券使用
    yield GestureDetector(
      onLongPress: controller.devtool.toggle,
      child: _scan(
        color: Colors.white,
        onPressed: () {
          DialogGeneral.quest(
            '變更會員可能會造成價格變更\n及移除 VIP 專屬商品',
            onMainButtonPressed: _scanMember,
          ).dialog();
        },
      ),
    );
    // HACK: test vip toggle
    // yield _vip();
    // HACK: test member
    // yield _devtool();
  }

  Widget _devtool() {
    return Obx(() {
      return Visibility(
        visible: controller.devtool.value,
        child: IconButton(
          icon: Icon(
            Icons.build,
            color: Colors.white,
          ),
          // onPressed: _showMemberPicker,
          onPressed: _loadCoupon,
        ),
      );
    });
  }

  void _loadCoupon() {
    final jsonString = jsonEncode(
        {'type': 'coupon', 'member_id': 78, 'member_coupon_id': 326});
    _parse(jsonString);
  }

  void _showMemberPicker() {
    controller.memberProvider.pickerDialog().then(_setMemberId);
  }

  void _scanMember() {
    // HACK: test
    // 不正確字串
    // _parse('/KK2X9NQ');
    // _parse('{type: member, member_id: 1}');
    // 訂單
    // _parse('{"type":"order","order_id":1}');
    // 不存在會員
    // _parse('{"type":"member","member_id":1}');
    // 一般會員
    // _parse('{"type":"member","member_id":50}');
    // VIP會員
    // _parse('{"type":"member","member_id":12}');
    QrCodeScanner().dialog<String>().then(
      (value) {
        if (value != null && value.isNotEmpty) {
          _parse(value);
        }
      },
    );
  }

  Widget _vip() {
    return IconButton(
      iconSize: 36.0,
      icon: SizedBox.fromSize(
        // size: Size.square(28.0),
        child: SvgPicture.asset(
          'assets/images/icon_crown.svg',
          color: Colors.white,
        ),
      ),
      onPressed: controller.vip.toggle,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        logger.d('[OrdersSumUpView] GestureDetector: onTap');
        Get.focusScope?.unfocus();
      },
      child: StandardPage(
        backgroundColor: controller.prefProvider.themeColor,
        // title:
        //     Obx(() => StandardPage.titleWidget('結帳${controller.orderNumber}')),
        // titleText: '結帳${controller.orderNumber}',
        titleText: '結帳',
        actions: _actions().toList(growable: false),
        child: controller.obx((state) {
          logger.d('[OrdersSumUpView] controller.obx');
          return BottomWidgetPage(
            child: _theme(_main()),
            bottom: _bottomButtons(),
          );
        }),
      ),
    );
  }

  Widget _theme(Widget child) {
    return Theme(
      data: Get.theme.copyWith(
        inputDecorationTheme: InputDecorationTheme(
          // alignLabelWithHint: true,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 8.0,
          ),
          hintStyle: const TextStyle(
            fontSize: 24.0,
            color: OKColor.GrayBF,
          ),
          labelStyle: const TextStyle(
            fontSize: 16.0,
            color: OKColor.Gray33,
          ),
          filled: true,
          fillColor: Colors.white,
          border: const OutlineInputBorder(
            borderRadius: kBorderRadius10,
            borderSide: const BorderSide(
              width: 1.0,
              color: OKColor.GrayDD,
            ),
          ),
          enabledBorder: const OutlineInputBorder(
            borderRadius: kBorderRadius10,
            borderSide: const BorderSide(
              width: 1.0,
              color: OKColor.GrayDD,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: kBorderRadius10,
            borderSide: BorderSide(
              width: 1.0,
              color: OKColor.Error,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: kBorderRadius10,
            borderSide: BorderSide(
              width: 1.0,
              color: OKColor.Primary,
            ),
          ),
        ),
      ),
      child: child ?? SizedBox.shrink(),
    );
  }

  Widget _bottomButtons() {
    return BottomWrapper(
      child: YesNoButton(
        leftButtonText: '上一步',
        rightButtonText: '結帳',
        rightColor: OKColor.Destructive,
        onLeftPressed: () => Get.back(),
        onRightPressed: _submitting,
      ),
    );
  }

  Future<void> _submitting() async {
    try {
      if (controller.draft.validate()) {
        await _onCheckoutPressed();
      }
    } catch (e) {
      DialogGeneral.alert('$e').dialog();
    }
  }

  Future<void> _onCheckoutPressed() async {
    // 檢查當前實收是否為0, 如果是的話就自動填入商品總價
    final total = (controller.total ?? 0).round();
    final paid = (controller.draft.paid ?? 0).round();
    if ((total > 0) && (paid <= 0)) {
      logger.d('[OrdersSumUpView] 付款金額為($paid)，把總價($total)填入');
      controller.paidEditing.text = '$total';
      controller.draft.paid = total;
      // 特殊: 單一支付則把金額灌到支付方式
      if (controller.draft.isSinglePayment) {
        controller.draft.multiplePayment.first.money = total;
      }
      logger.d('[OrdersSumUpView] _onCheckoutPressed: refreshDraft');
      controller.refreshDraft();
    }
    // 發票
    controller.draft.invoice = controller.invoiceEnabled;
    if (controller.draft.invoice) {
      // 零元確認
      if (controller.draft.total is num && controller.draft.total <= 0) {
        final selected =
            await _interruptionConfirm('0元不產生發票', mainButtonText: '繼續結帳');
        if (Button.Negative == selected) {
          logger.d('[OrdersSumUpView] _onCheckoutPressed: 取消結帳');
          return;
        }
        // continue without invoice
        logger.d('[OrdersSumUpView] _onCheckoutPressed: 不開發票，繼續結帳');
        controller.draft.invoice = false;
      }
    }
    await _showConfirmDialog();
  }

  Future<Button> _interruptionConfirm(
    String message, {
    String mainButtonText,
    String secondaryButtonText,
  }) {
    final completer = Completer<Button>();
    DialogGeneral.quest(
      message ?? '',
      onMainButtonPressed: () => completer.complete(Button.Positive),
      onSecondaryButtonPress: () => completer.complete(Button.Negative),
      mainButtonText: mainButtonText,
      secondaryButtonText: secondaryButtonText,
    ).dialog();
    return completer.future;
  }

  // 可以結帳，確認送出訂單?
  // Display the confirm dialog.
  Future<void> _showConfirmDialog() async {
    logger.d(
        '[OrdersSumUpView] _showConfirmDialog: invoice(${controller.draft.invoice})');
    Get.focusScope?.unfocus();
    await DialogGeneral(
      DialogArgs(
        // contentPadding: EdgeInsets.zero,
        contentPadding: EdgeInsets.all(kPadding),
        header: DialogGeneral.titleText('確認結帳'),
        content: OrdersConfirm(
          data: controller.draft,
          prefProvider: controller.prefProvider,
        ),
        mainButtonText: '確認',
        secondaryButtonText: '取消',
        // enableMainButton: (controller.ordersPostReq.change >= 0),  //不擋了，下面按下跳提示檔
        onMainButtonPress: _submit,
      ),
    ).dialog();
  }

  // 按下確認結帳
  Future<void> _submit() async {
    final ret = await FutureProgress(
      future: controller.submit(),
    ).dialog();
    if (true == ret) {
      // 回到首頁
      Get.until((route) => Get.currentRoute == Routes.HOME);
    }
  }

  Widget _main() {
    return Stack(
      alignment: Alignment.topCenter,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    // 發票截圖
    yield Obx(() {
      if (controller.invoice == null) {
        return SizedBox.shrink();
      }
      final ret = Screenshot(
        controller: controller.invoiceScreenshotController,
        child: InvoiceWidget(data: controller.invoice),
      );
      controller.widgetUpdater.complete();
      return ret;
    });
    // 國防布
    yield ColoredBox(
      color: kColorBackground,
      child: SizedBox.expand(),
    );
    // 使用者實際看到的預覽介面
    yield Obx(() => _body());
  }

  Widget _body() {
    return Column(
      children: [
        // 現場消費者
        _member(),
        // 子頁面
        Expanded(
          child: ListView(
            physics: AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.only(bottom: kBottomButtonPadding),
            children: _list().toList(growable: false),
          ),
        ),
      ],
    );
  }

  Widget _coupon() {
    final children = <Widget>[];
    children.addIf(
      true,
      Container(
        alignment: Alignment.bottomCenter,
        margin: EdgeInsets.only(
          top: kPadding,
          bottom: 8,
        ),
        child: CouponItem(
          data: controller.coupon,
          onPressed: _showCouponDetail,
        ),
      ),
    );
    children.addIf(
      true,
      IconButton(
        iconSize: 32,
        icon: DecoratedBox(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFE0E0E0),
                spreadRadius: 0,
                blurRadius: 8,
              ),
            ],
          ),
          child: Icon(
            Icons.cancel,
            color: Colors.white,
          ),
        ),
        onPressed: controller.resetCoupon,
      ),
    );
    return Stack(
      alignment: Alignment.topRight,
      children: children,
    );
  }

  Widget _coouponMessage() {
    return Center(
      child: Text(
        controller.couponMessage ?? '',
        style: TextStyle(
          fontSize: 14,
          color: OKColor.Error,
        ),
        textAlign: TextAlign.right,
      ),
    );
  }

  Iterable<Widget> _list() sync* {
    // 優惠券
    if (controller.coupon != null) {
      yield _coupon();
    }
    // 優惠券例外訊息
    if (controller.couponMessage != null &&
        controller.couponMessage.isNotEmpty) {
      yield _coouponMessage();
    }
    // 商品小計
    yield _subtotal();
    // 運費 (零售宅配要顯示運費)
    if (controller.shippingFeeVisible) {
      yield _shippingFee();
    }
    // 金流手續費，零售需顯示(線上付款會出現)
    if (controller.paymentFeeVisible) {
      yield _paymentFee();
    }
    // 原價服務費
    if (controller.serviceFeeOriginVisible) {
      yield _serviceCharge();
    }
    // 現場減價
    yield _discount();
    // 額外費用
    yield _additionalCharge();
    // 折價後服務費
    if (controller.serviceFeeDiscountVisible) {
      yield _serviceCharge();
    }
    // 優惠券金額
    yield _couponFee();
    // 優惠券折抵
    yield _couponDiscount();
    // 積點折抵 (有會員才需顯示)
    if (controller.pointDiscountVisible) {
      yield _pointDiscount();
    }
    // 商品總價
    yield _total();
    // 支付方式
    yield _paymentMethod();
    // 實收
    yield _paid();
    // 現金找零
    yield _change();
    // space
    yield SizedBox(height: 4);
    // line pay
    if (controller.linePayEnabled) {
      yield _linePay();
    }
    // 發票總開關
    if (controller.prefProvider.brandsInvoice?.invoiceEnabled ?? false) {
      yield _invoice();
    }
    yield _receipt();
    yield _itemReceipt();
  }

  Widget _itemReceipt() {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 12,
        horizontal: 12,
      ),
      color: const Color(0xffdedee6),
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: const Color(0x29000000),
              offset: Offset(0, 3),
              blurRadius: 6,
            ),
          ],
        ),
        child: StreamBuilder<LocalSettings>(
          initialData: controller.prefProvider.localSettings,
          stream: controller.prefProvider.localSettingsStream(),
          builder: (context, snapshot) {
            final switcher =
                snapshot.data?.printItemReceipt?.switcher ?? Switcher.On;
            return SwitchListTile(
              value: switcher.isOn,
              onChanged: (value) {
                final localSettings = controller.prefProvider.localSettings;
                localSettings.printItemReceipt = value.switcher.index;
                controller.prefProvider.localSettings = localSettings;
              },
              title: Row(
                children: [
                  Text(
                    '列印「${controller.draft.orderTypeName}明細」',
                    style: TextStyle(
                      fontSize: 16,
                      color: const Color(0xff6d7278),
                    ),
                    textAlign: TextAlign.left,
                  ),
                  Expanded(
                    child: CustomEditor.number(
                      scrollPadding:
                          EdgeInsets.only(bottom: kBottomButtonPadding),
                      textAlign: TextAlign.center,
                      controller: controller.itemReceiptEditing,
                      onChanged: (value) {
                        final count = int.tryParse(value) ?? 0;
                        final localSettings =
                            controller.prefProvider.localSettings;
                        localSettings.printItemReceiptCount = count;
                        controller.prefProvider.localSettings = localSettings;
                      },
                    ).paddingSymmetric(
                      horizontal: 8,
                    ),
                  ),
                  Text(
                    '張',
                    style: TextStyle(
                      fontSize: 16,
                      color: const Color(0xff6d7278),
                    ),
                    textAlign: TextAlign.left,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _receipt() {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 12,
        horizontal: 12,
      ),
      color: const Color(0xffdedee6),
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: const Color(0x29000000),
              offset: Offset(0, 3),
              blurRadius: 6,
            ),
          ],
        ),
        child: StreamBuilder<LocalSettings>(
          initialData: controller.prefProvider.localSettings,
          stream: controller.prefProvider.localSettingsStream(),
          builder: (context, snapshot) {
            final switcher =
                snapshot.data?.printReceipt?.switcher ?? Switcher.On;
            return SwitchListTile(
              value: switcher.isOn,
              onChanged: (value) {
                final localSettings = controller.prefProvider.localSettings;
                localSettings.printReceipt = value.switcher.index;
                controller.prefProvider.localSettings = localSettings;
              },
              title: Row(
                children: [
                  Text(
                    '列印「消費明細」',
                    style: TextStyle(
                      fontSize: 16,
                      color: const Color(0xff6d7278),
                    ),
                    textAlign: TextAlign.left,
                  ),
                  Expanded(
                    child: CustomEditor.number(
                      scrollPadding:
                          EdgeInsets.only(bottom: kBottomButtonPadding),
                      textAlign: TextAlign.center,
                      controller: controller.receiptEditing,
                      onChanged: (value) {
                        final count = int.tryParse(value) ?? 0;
                        final localSettings =
                            controller.prefProvider.localSettings;
                        localSettings.printReceiptCount = count;
                        controller.prefProvider.localSettings = localSettings;
                      },
                    ).paddingSymmetric(
                      horizontal: 8,
                    ),
                  ),
                  Text(
                    '張',
                    style: TextStyle(
                      fontSize: 16,
                      color: const Color(0xff6d7278),
                    ),
                    textAlign: TextAlign.left,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _paymentIcon() {
    // 多重支付
    if (controller.draft.isMutiplePayment) {
      return PaymethodItem(
        image: AppPayMethod.Mutilpe.icon,
      );
    }

    String name;
    String icon;

    // 線上單
    if (controller.draft.source.orderSource.isLine) {
      final _orderDetail = controller.data?.data;
      if (_orderDetail != null &&
          _orderDetail.orderPayments != null &&
          _orderDetail.orderPayments.isNotEmpty) {
        final data = controller.data.data.orderPayments.first;
        icon = data.payMethodId.appPayMethod.icon;
        name = data?.name ?? '';
      }
    }

    if (controller.draft.isSinglePayment) {
      final data = controller.draft.multiplePayment.first;
      final pay = controller.prefProvider.settingPay.firstWhere(
        (element) => element.id == data.paymentMethodId,
        orElse: () => null,
      );
      final _icon = pay?.payMethodId?.appPayMethod?.icon;
      final _name = pay?.name;

      if (_icon != null && _icon.isNotEmpty) {
        icon = _icon;
      }
      if (_name != null && _name.isNotEmpty) {
        name = _name;
      }
    }

    return PaymethodItem(
      title: name,
      image: icon,
      showTitle: icon == null || icon.isEmpty,
    );
  }

  // 支付方式
  Widget _paymentMethod() {
    return IconMenuItem(
      minHeight: 48,
      icon: Icons.account_balance_wallet,
      titleText: '支付方式',
      trailing: GestureDetector(
        onTap: controller.paymentEnabled ? _showPaymentMethod : null,
        // onTap: _showPaymentMethod,
        child: SizedBox(
          height: 48,
          child: _paymentIcon(),
        ).paddingSymmetric(
          vertical: 2.0,
        ),
      ),
    );
  }

  // 優惠券費用
  Widget _couponFee() {
    final price = (controller.memberCouponExtraPrice ?? 0).abs();
    if (price is num && price > 0) {
      return IconMenuItem(
        leading: SvgPicture.asset(
          'assets/images/icon_ticket.svg',
          color: OKColor.Gray33,
          // colorBlendMode: BlendMode.srcIn,
        ),
        titleText: '優惠券費用',
        trailing: Text(
          price.decimalStyle,
          style: const TextStyle(
            fontSize: 17.0,
            color: OKColor.Gray33,
          ),
          textAlign: TextAlign.right,
        ),
      );
    }
    return SizedBox();
  }

  // 優惠券折價
  Widget _couponDiscount() {
    final price = (controller.memberCouponDiscount ?? 0).abs();
    if (price is num && price > 0) {
      return IconMenuItem(
        leading: SvgPicture.asset(
          'assets/images/icon_ticket.svg',
          color: OKColor.Gray33,
        ),
        titleText: '優惠券折價',
        trailing: Text(
          '-${price.decimalStyle}',
          style: const TextStyle(
            fontSize: 17.0,
            color: OKColor.Gray33,
          ),
          textAlign: TextAlign.right,
        ),
      );
    }
    return SizedBox();
  }

  Widget _paymentFee() {
    return IconMenuItem(
      // icon: Icons.local_shipping,
      leading: SvgPicture.asset('assets/images/icon_payment_fee.svg'),
      titleText: '金流手續費',
      trailing: Text(
        (controller.data?.orderPaymentFee ?? 0).decimalStyle,
        style: const TextStyle(
          fontSize: 17.0,
          color: OKColor.Gray33,
        ),
        textAlign: TextAlign.right,
      ),
    );
  }

  Widget _member() {
    return ListTile(
      contentPadding: kContentPadding,
      tileColor: Colors.white,
      leading: MemberAvatar(
        imageUrl: controller.member?.avatar,
      ),
      title: Text(
        // '現場消費者',
        // controller.data?.data?.memberName ?? '現場消費者',
        controller.member?.name ?? '現場消費者',
        style: const TextStyle(
          fontSize: 20.0,
          color: Colors.black,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.left,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
      trailing: _removeMemberButton(),
    );
  }

  // 移除會員按鈕
  Widget _removeMemberButton() {
    // 無會員不可顯示移除按鈕
    if (controller.member == null) {
      return SizedBox();
    }
    // 從 line 過來的訂單不可顯示移除按鈕
    if (controller.draft.source.orderSource.isLine) {
      return SizedBox();
    }
    return IconButton(
      iconSize: 32,
      icon: DecoratedBox(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFE0E0E0),
              spreadRadius: 0,
              blurRadius: 8,
            ),
          ],
        ),
        child: Icon(
          Icons.cancel,
          color: Colors.white,
        ),
      ),
      onPressed: _removeMember,
    );
  }

  void _removeMember() {
    DialogGeneral.quest(
      '移除會員可能會造成價格變更\n及移除 VIP 專屬商品',
      onMainButtonPressed: () {
        logger
            .d('[OrdersSumUpView] _removeMember: draft.memberId.refreshDraft');
        controller.draft.memberId = null;
        controller.refreshDraft();
        // controller.member = null;
      },
    ).dialog();
  }

  Widget _linePayCode() {
    return TextFormField(
      autocorrect: false,
      scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
      // initialValue: controller.linePayCode,
      controller: controller.linePayEditing,
      decoration: InputDecoration(
        labelText: 'LINE Pay 付款條碼',
        hintText: 'LINE Pay 付款條碼',
        // TODO: need test
        suffixIcon: _scan(onPressed: _showLinaPayCodePicker),
      ),
      textCapitalization: TextCapitalization.characters,
      enableSuggestions: false,
      // style: Get.textTheme.headline5,
      style: const TextStyle(
        fontSize: 24.0,
        color: OKColor.Gray33,
      ),
      textInputAction: TextInputAction.done,
      inputFormatters: <TextInputFormatter>[
        FilteringTextInputFormatter.singleLineFormatter,
        FilteringTextInputFormatter.allow(RegExp(r'\d')),
      ],
      textAlign: TextAlign.center,
      onChanged: (value) {
        controller.linePayCode = value;
      },
      validator: (value) {
        if (value.isEmpty) {
          return null;
        }
        // TODO: 格式檢查
        // final vatNumber = controller.draft.vatNumber ?? '';
        // final carrierId = controller.draft.carrierId ?? '';
        // if (vatNumber.isNotEmpty && carrierId.isNotEmpty) {
        //   return '統一編號及載具/愛心碼不能同時輸入';
        // }
        // final isFormat = Utils.isCarrierId(value) || Utils.isNpoBan(value);
        // if (!isFormat) {
        //   return '載具/愛心碼 格式錯誤';
        // }
        return null;
      },
    );
  }

  Iterable<Widget> _linePayElements() sync* {
    yield ColoredBox(
      color: Colors.white,
      child: Row(children: _linePaySwitcher().toList(growable: false)),
    );
    if (controller.linePaySwitcher) {
      yield SizedBox(height: 8);
    }
    if (controller.linePaySwitcher) {
      yield _linePayCode().paddingSymmetric(horizontal: 16);
      yield SizedBox(height: 8);
    }
  }

  Widget _linePay() {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 12,
        horizontal: 12,
      ),
      color: const Color(0xffdedee6),
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.0),
          color: const Color(0xffeaeaf2),
          boxShadow: [
            BoxShadow(
              color: const Color(0x29000000),
              offset: Offset(0, 3),
              blurRadius: 6,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.all(Radius.circular(15)),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: _linePayElements().toList(growable: false),
          ),
        ),
      ),
    );
  }

  Iterable<Widget> _linePaySwitcher() sync* {
    yield SizedBox(width: kPadding);
    yield Expanded(
      child: Text(
        'LINE Pay 掃描',
        style: TextStyle(
          fontSize: 16,
          color: const Color(0xff6d7278),
        ),
        textAlign: TextAlign.left,
      ).paddingSymmetric(vertical: 12),
    );
    yield SizedBox(width: 8);
  }

  Iterable<Widget> _invoiceSwitcher() sync* {
    yield SizedBox(width: kPadding);
    yield Expanded(
      child: Text(
        '開立發票',
        style: TextStyle(
          fontSize: 16,
          color: const Color(0xff6d7278),
        ),
        textAlign: TextAlign.left,
      ).paddingSymmetric(vertical: 12),
    );
    // 發票單次開關
    if (controller.prefProvider.brandsInvoice.invoiceSwitchEnabled) {
      yield Switch(
        value: controller.invoiceSwitcher.isOn,
        onChanged: (value) {
          controller.invoiceSwitcher = value.switcher;
        },
      );
    }
    yield SizedBox(width: 8);
  }

  Iterable<Widget> _invoiceElements() sync* {
    // 發票單次開關
    yield ColoredBox(
      color: Colors.white,
      child: Row(
        children: _invoiceSwitcher().toList(growable: false),
      ),
    );
    if (controller.invoiceSwitcher.isOn) {
      yield SizedBox(height: 8);
      yield _buyer().paddingSymmetric(horizontal: 16);
      yield SizedBox(height: 8);
      yield _carrier().paddingSymmetric(horizontal: 16);
      yield SizedBox(height: 8);
    }
  }

  Widget _invoice() {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 12,
        horizontal: 12,
      ),
      color: const Color(0xffdedee6),
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.0),
          color: const Color(0xffeaeaf2),
          boxShadow: [
            BoxShadow(
              color: const Color(0x29000000),
              offset: Offset(0, 3),
              blurRadius: 6,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.all(Radius.circular(15)),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: _invoiceElements().toList(growable: false),
          ),
        ),
      ),
    );
  }

  // 現金找零
  Widget _change() {
    return IconMenuItem(
      minHeight: 38,
      icon: Icons.money_off,
      titleText: '現金找零',
      trailing: Text(
        controller.draft.change?.decimalStyle ?? '',
        style: const TextStyle(
          fontSize: 32.0,
          color: OKColor.Error,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.right,
      ),
    );
  }

  // 實收
  Widget _paid() {
    return IconMenuItem(
      minHeight: 48,
      icon: Icons.monetization_on,
      titleText: '實收',
      trailing: GestureDetector(
        onTap: _showAlert,
        child: TextFormField(
          autocorrect: false,
          scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
          readOnly: !controller.inputEnabled,
          // enabled: controller.inputEnabled,
          controller: controller.paidEditing,
          decoration: InputDecoration(
            hintText: '實收',
          ),
          style: TextStyle(
            fontSize: 32.0,
            color: OKColor.Gray33,
            fontWeight: FontWeight.w600,
          ),
          keyboardType: TextInputType.number,
          textInputAction: TextInputAction.done,
          inputFormatters: <TextInputFormatter>[
            FilteringTextInputFormatter.digitsOnly,
            FilteringTextInputFormatter.singleLineFormatter,
          ],
          textAlign: TextAlign.end,
          onTap: () {
            if (controller.inputEnabled) {
              // controller.paidEditing.selection = TextSelection(
              //   baseOffset: 0,
              //   extentOffset: controller.paidEditing.text.length,
              // );
            } else {
              _showAlert();
            }
          },
          autovalidateMode: AutovalidateMode.always,
          validator: (value) {
            final req = controller.draft;
            // 尚未填入金額，略過檢查
            req.paid ??= 0;
            if (req.paid <= 0) {
              return null;
            }
            req.total ??= 0;
            if (req.paid < req.total) {
              return '實收金額不足';
            }
            // if (req.paid > req.total) {
            //   // 需找零，支付方式必須包含現金
            //   bool containersCash =
            //       controller.containsPayMethod(AppPayMethod.Cash);
            //   if (!containersCash) {
            //     return '有找零，支付方式必含現金';
            //   }
            // }
            return null;
          },
          onChanged: (value) {
            logger.d('[OrdersSumUpView] _paid: draft.paid.refreshDraft');
            final d = num.tryParse(value) ?? 0.0;
            controller.draft.paid = d;
            // 特殊: 單一支付則把金額灌到支付方式
            if (controller.draft.isSinglePayment) {
              controller.draft.multiplePayment.first.money = d;
            }
            controller.refreshDraft();
          },
        ),
      ),
    );
  }

  // TODO: remove me
  // void _paidValidator() {
  //   try {
  //     final req = controller.draft;
  //     final paid = req.paid ?? 0;
  //     final total = req.total ?? 0;
  //     if (paid > 0 && paid < total) {
  //       throw '實收金額不足';
  //     }
  //   } catch (e) {
  //     rethrow;
  //   }
  // }

  Widget _total() {
    return IconMenuItem(
      minHeight: 38,
      icon: Icons.attach_money,
      titleText: '商品總價',
      trailing: Text(
        controller.total.decimalStyle ?? '',
        style: const TextStyle(
          fontSize: 32.0,
          color: const Color(0xd9000000),
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.right,
      ),
    );
  }

  Iterable<Widget> _additionalChargeElements() sync* {
    yield Expanded(
      child: GestureDetector(
        onTap: _showAlert,
        child: TextFormField(
          autocorrect: false,
          scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
          enabled: controller.inputEnabled,
          controller: controller.additionalChargeEditing,
          decoration: InputDecoration(
            hintText: '額外費用',
          ),
          // style: Get.textTheme.headline6,
          style: const TextStyle(
            fontSize: 24.0,
            color: const Color(0xff333333),
          ),
          keyboardType: TextInputType.number,
          textInputAction: TextInputAction.done,
          inputFormatters: <TextInputFormatter>[
            FilteringTextInputFormatter.digitsOnly,
            FilteringTextInputFormatter.singleLineFormatter,
          ],
          textAlign: TextAlign.end,
          onTap: () {
            // select all
            // controller.additionalChargeEditing.selection = TextSelection(
            //     baseOffset: 0,
            //     extentOffset: controller.additionalChargeEditing.text.length);
          },
          onChanged: (value) {
            logger.d(
                '[OrdersSumUpView] _additionalCharge: draft.additionalCharges.refreshDraft');
            final input = num.tryParse(value) ?? 0;
            controller.draft.additionalCharges = input.abs();
            controller.refreshDraft();
          },
        ),
      ),
    );
    yield IconButton(
      icon: Icon(
        Icons.expand_more,
        color: OKColor.Primary,
      ),
      onPressed: () {
        // final jsonString = controller.draft.toRawJson();
        // final draft = OrdersPostReq.fromRawJson(jsonString);
        final draft = controller.draft;
        draft.additionalCharges ??= 0;
        final tempValue = draft.additionalCharges;
        draft.additionalCharges = 0;
        draft.total -= tempValue;
        // 顯示 promotion picker sheet
        PromotionPickerView(
          draft: draft,
          promotionTypes: [PromotionType.Upgrade],
        ).sheet().then((value) {
          if (value is num) {
            final input = value.round().abs();
            draft.additionalCharges = input;
          } else {
            draft.additionalCharges = tempValue;
          }
          final input = draft.additionalCharges.round().abs();
          controller.additionalChargeEditing.text = '$input';
          controller.refreshDraft();
        });
      },
    );
  }

  Widget _additionalCharge() {
    return IconMenuItem(
      minHeight: 48,
      icon: Icons.add_to_photos,
      titleText: '額外費用',
      middle: const Icon(
        Icons.add,
        color: const Color(0xFF6D7278),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: _additionalChargeElements().toList(growable: false),
      ),
    );
  }

  // 現場減價
  Widget _discount() {
    Iterable<Widget> _children() sync* {
      yield Expanded(
        child: GestureDetector(
          onTap: _showAlert,
          child: TextFormField(
            autocorrect: false,
            scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
            enabled: controller.inputEnabled,
            controller: controller.discountEditing,
            decoration: InputDecoration(
              hintText: '現場減價',
            ),
            style: const TextStyle(
              fontSize: 24.0,
              color: OKColor.Gray33,
            ),
            keyboardType: TextInputType.number,
            textInputAction: TextInputAction.done,
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.digitsOnly,
              FilteringTextInputFormatter.singleLineFormatter,
            ],
            textAlign: TextAlign.end,
            onTap: () {
              // select all
              // controller.discountEditing.selection = TextSelection(
              //     baseOffset: 0,
              //     extentOffset: controller.discountEditing.text.length);
            },
            // validator: (value) {
            //   // if (value.isEmpty) {
            //   //   return null;
            //   // }
            //   // if (value.isNum) {
            //   //   return null;
            //   // }
            //   // return '輸入數字';
            //   return null;
            // },
            onChanged: (value) {
              logger.d(
                  '[OrdersSumUpView] _discount: draft.discount.refreshDraft');
              final input = num.tryParse(value) ?? 0;
              controller.draft.discount = -(input.abs());
              controller.refreshDraft();
            },
          ),
        ),
      );
      yield IconButton(
        icon: Icon(
          Icons.expand_more,
          color: OKColor.Primary,
        ),
        onPressed: () {
          // final jsonString = controller.draft.toRawJson();
          // final draft = OrdersPostReq.fromRawJson(jsonString);
          final draft = controller.draft;
          draft.discount ??= 0;
          final tempValue = draft.discount;
          draft.discount = 0;
          draft.total -= tempValue;
          // 顯示 promotion picker sheet
          PromotionPickerView(
            draft: draft,
            promotionTypes: [PromotionType.Discount],
          ).sheet().then((value) {
            if (value is num) {
              final input = value.round().abs();
              draft.discount = -input;
            } else {
              draft.discount = tempValue;
            }
            final input = draft.discount.round().abs();
            controller.discountEditing.text = '$input';
            controller.refreshDraft();
          });
        },
      );
    }

    return IconMenuItem(
      minHeight: 48,
      leading: SvgPicture.asset('assets/images/icon_price_down.svg'),
      titleText: '現場減價',
      middle: const Icon(
        Icons.remove,
        color: const Color(0xFF6D7278),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: _children().toList(growable: false),
      ),
    );
  }

  // 運費
  Widget _shippingFee() {
    return IconMenuItem(
      icon: Icons.local_shipping,
      titleText: '運費',
      trailing: Text(
        controller.displayShippingFee,
        style: const TextStyle(
          fontSize: 17.0,
          color: const Color(0xff333333),
        ),
        textAlign: TextAlign.right,
      ),
    );
  }

  Widget _pointDiscountDesc() {
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontSize: 14,
          color: const Color(0xff6d7278),
        ),
        children: [
          TextSpan(
            text: '目前積點為',
          ),
          TextSpan(
            // text: '1688',
            text: controller.totalPoints.decimalStyle ?? '',
            style: TextStyle(
              color: controller.prefProvider.themeColor,
            ),
          ),
          TextSpan(
            text: '點(1點=1元)',
          ),
        ],
      ),
      textAlign: TextAlign.right,
    );
  }

  // FIXME: 積點折抵
  Widget _pointDiscount() {
    return IconMenuItem(
      minHeight: 48,
      subtitle: _pointDiscountDesc(),
      leading: Container(
        width: 12,
        height: 12,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: const Color(0xff6d7278),
        ),
        child: Text(
          'P',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      titleText: '積點折抵',
      middle: const Icon(
        Icons.remove,
        color: const Color(0xFF6D7278),
      ),
      trailing: GestureDetector(
        onTap: _showAlert,
        child: Visibility(
          visible: controller.redeemMemberPointsReadonly,
          child: Container(
            height: 48,
            alignment: Alignment.centerRight,
            child: Text(
              (controller.draft?.redeemMemberPoints ?? 0).decimalStyle,
              style: const TextStyle(
                fontSize: 24.0,
                color: OKColor.Gray33,
              ),
              textAlign: TextAlign.right,
            ),
          ),
          replacement: TextFormField(
            autocorrect: false,
            scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
            // readOnly: controller.redeemMemberPointsReadonly,
            enabled: controller.inputEnabled,
            controller: controller.pointEditing,
            decoration: InputDecoration(
              hintText: '積點折抵',
            ),
            style: const TextStyle(
              fontSize: 24.0,
              color: OKColor.Gray33,
            ),
            keyboardType: TextInputType.number,
            textInputAction: TextInputAction.done,
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.digitsOnly,
              FilteringTextInputFormatter.singleLineFormatter,
            ],
            textAlign: TextAlign.end,
            onTap: () {
              // select all
              // controller.pointEditing.selection = TextSelection(
              //     baseOffset: 0,
              //     extentOffset: controller.pointEditing.text.length);
            },
            validator: (value) {
              // if (value.isEmpty) {
              //   return null;
              // }
              // if (value.isNum) {
              //   return null;
              // }
              // return '輸入數字';
              // final current = max(0, controller.member?.points ?? 0);
              // final usage = controller.draft?.redeemMemberPoints ?? 0;
              // if (usage > current) {
              //   return '會員積點不足';
              // }
              return null;
            },
            onChanged: (value) {
              logger.d(
                  '[OrdersSumUpView] _pointDiscount: draft.redeemMemberPoints.refreshDraft');
              final input = num.tryParse(value) ?? 0;
              controller.draft.redeemMemberPoints = input.round().abs();
              controller.refreshDraft();
            },
          ),
        ),
      ),
    );
  }

  // 服務費
  Widget _serviceCharge() {
    Iterable<Widget> _children() sync* {
      yield Spacer();
      yield Text(
        controller.serviceFee.decimalStyle ?? '',
        style: const TextStyle(
          fontSize: 17.0,
          color: OKColor.Gray33,
        ),
        textAlign: TextAlign.right,
      );
      yield SizedBox.fromSize(
        size: Size.square(28.0),
        child: Checkbox(
          value: controller.serviceFeeEnabled,
          onChanged: (value) {
            if (controller.inputEnabled) {
              logger.d(
                  '[OrdersSumUpView] _serviceCharge: serviceFeeEnabled.refreshDraft');
              controller.serviceFeeEnabled = value;
              controller.refreshDraft();
            }
          },
        ),
      );
    }

    return IconMenuItem(
      leading: SvgPicture.asset('assets/images/icon_jarvis.svg'),
      titleText: '服務費',
      trailing: Row(children: _children().toList(growable: false)),
    );
  }

  // 商品小計
  Widget _subtotal() {
    return IconMenuItem(
      leading: SvgPicture.asset(
        'assets/images/icon_add_task.svg',
      ),
      titleText: '商品小計',
      trailing: Text(
        controller.draft.normalItemsPrice.decimalStyle,
        style: const TextStyle(
          fontSize: 17.0,
          color: OKColor.Gray33,
        ),
        textAlign: TextAlign.right,
      ),
    );
  }

  Widget _buyer() {
    return TextFormField(
      autocorrect: false,
      scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
      // maxLength: 8,
      maxLengthEnforcement: MaxLengthEnforcement.enforced,
      // controller: controller.vatNumberEditing,
      initialValue: controller.draft.vatNumber ?? '',
      decoration: InputDecoration(
        labelText: '統一編號',
        hintText: '請輸入統一編號',
      ),
      // style: Get.textTheme.headline5,
      style: const TextStyle(
        fontSize: 24.0,
        color: const Color(0xff333333),
      ),
      keyboardType: TextInputType.number,
      // keyboardType: TextInputType.phone,
      enableSuggestions: false,
      textInputAction: TextInputAction.done,
      inputFormatters: <TextInputFormatter>[
        FilteringTextInputFormatter.digitsOnly,
        FilteringTextInputFormatter.singleLineFormatter,
        LengthLimitingTextInputFormatter(8),
      ],
      textAlign: TextAlign.center,
      // onTap: () {
      //   controller.vatNumberEditing.selection = TextSelection(
      //       baseOffset: 0,
      //       extentOffset: controller.vatNumberEditing.text.length);
      // },
      onChanged: (value) {
        controller.draft.vatNumber = value;
      },
      validator: (value) {
        if (value.isEmpty) {
          return null;
        }
        final vatNumber = controller.draft.vatNumber ?? '';
        final carrierId = controller.draft.carrierId ?? '';
        if (vatNumber.isNotEmpty && carrierId.isNotEmpty) {
          return '統一編號及載具/愛心碼不能同時輸入';
        }
        if (!Utils.isTaxId(value)) {
          return '統一編號錯誤';
        }
        return null;
      },
    );
  }

  /// 載具
  Widget _carrier() {
    return TextFormField(
      autocorrect: false,
      scrollPadding: EdgeInsets.only(bottom: kBottomButtonPadding),
      controller: controller.carrierIdEditing,
      decoration: InputDecoration(
        labelText: '載具/愛心碼',
        hintText: '載具/愛心碼',
        // TODO: need test
        suffixIcon: _scan(onPressed: _showBarcodePicker),
      ),
      textCapitalization: TextCapitalization.characters,
      enableSuggestions: false,
      // style: Get.textTheme.headline5,
      style: const TextStyle(
        fontSize: 24.0,
        color: OKColor.Gray33,
      ),
      textInputAction: TextInputAction.done,
      inputFormatters: <TextInputFormatter>[
        FilteringTextInputFormatter.singleLineFormatter,
        UpperCaseTextFormatter(),
        LengthLimitingTextInputFormatter(8),
        FilteringTextInputFormatter.allow(RegExp(r'[\dA-Z+-\./]{0,8}')),
      ],
      textAlign: TextAlign.center,
      onChanged: (value) {
        controller.draft.carrierId = value;
      },
      validator: (value) {
        if (value.isEmpty) {
          return null;
        }
        final vatNumber = controller.draft.vatNumber ?? '';
        final carrierId = controller.draft.carrierId ?? '';
        if (vatNumber.isNotEmpty && carrierId.isNotEmpty) {
          return '統一編號及載具/愛心碼不能同時輸入';
        }
        final isFormat = Utils.isCarrierId(value) || Utils.isNpoBan(value);
        if (!isFormat) {
          return '載具/愛心碼 格式錯誤';
        }
        return null;
      },
    );
  }

  // TODO: refactor me
  void _parse(String value) {
    try {
      final qr = QrFormat.fromRawJson(value);
      if (qr.isMember) {
        // 會員
        if (controller.draft.source.orderSource.isLine) {
          DialogGeneral.alert('LINE 訂單不能變更會員').dialog();
        } else {
          _setMemberId(qr.memberId);
        }
      } else if (qr.isCoupon || qr.isCoupon1 || qr.isCoupon2) {
        // 檢查會員
        if (controller.draft.source.orderSource.isLine &&
            controller.draft.memberId != qr.memberId) {
          DialogGeneral.alert('此優惠券不屬於 LINE 訂單會員').dialog();
        } else {
          // 載入優惠券
          controller.couponProvider
              .getMemberCoupon(qr.memberId, qr.memberCouponId)
              .then(
            (event) {
              if (event.isAvailable) {
                logger
                    .d('[OrdersSumUpView] _parse: draft.memberId.refreshDraft');
                controller.draft.memberId = qr.memberId;
                controller.draft.memberCouponId = qr.memberCouponId;
                controller.refreshDraft();
                _setMemberId(qr.memberId);
              } else {
                DialogGeneral.alert(event.message).dialog();
              }
            },
          );
        }
      } else {
        DialogGeneral.alert('不是正確的 QR 碼').dialog();
      }
    } catch (e) {
      // 顯示錯誤訊息
      logger.e(e);
      DialogGeneral.alert('不是正確的 QR 碼').dialog();
    }
  }

  void _setMemberId(num id) {
    FutureProgress(
      future: controller.memberProvider.getMember(id),
    ).dialog<Member>().then(
      (value) {
        logger.d('[OrdersSumUpView] _setMemberId: draft.memberId.refreshDraft');
        controller.draft.memberId = value.id;
        controller.refreshDraft();
        // controller.member = value;
      },
    ).then(
      (value) {
        final member = controller.memberProvider.getMemberFromLocalStorage(id);
        controller.vip.value = member?.isVip?.switcher?.isOn ?? false;
      },
    );
  }

  Widget _scan({
    final Function onPressed,
    final Color color,
  }) {
    return IconButton(
      iconSize: 36.0,
      icon: SizedBox.fromSize(
        size: Size.square(28.0),
        child: SvgPicture.asset(
          'assets/images/icon_scan.svg',
          color: color ?? controller.prefProvider.themeColor,
        ),
      ),
      onPressed: onPressed,
    );
  }

  Future<void> _showLinaPayCodePicker() async {
    try {
      // 縮下鍵盤
      Get.focusScope?.unfocus();
      await 200.milliseconds.delay();
      final code = await QrCodeScanner().dialog<String>();
      if (code != null && code.isNotEmpty) {
        controller.linePayEditing.text = code;
        controller.linePayCode = code;
      }
    } catch (e) {
      // TODO: 顯示錯誤訊息
    }
  }

  void _showBarcodePicker() {
    Future(() {
      Get.focusScope?.unfocus();
      return 200.milliseconds.delay();
    }).then((value) {
      return QrCodeScanner().dialog<String>();
    }).then((value) {
      if (value != null && value.isNotEmpty) {
        controller.draft.carrierId = value;
        controller.carrierIdEditing.text = value;
      }
    });
  }

  // Widget formattedConfirmDialogItem(
  //   String label,
  //   String value, {
  //   Color labelColor = Colors.black,
  //   Color valueColor = Colors.black,
  // }) {
  //   return LabelValueText(
  //     left: TextArgs.left(
  //       label,
  //       color: labelColor,
  //       fontSize: 16,
  //     ),
  //     right: TextArgs.right(
  //       value,
  //       color: valueColor,
  //       fontSize: 16,
  //     ),
  //   );
  // }

  Iterable<MultiplePayment> get defaultPayment {
    return [
      MultiplePayment(
      paymentMethodId: AppPayMethod.Cash.index,
      money: controller.draft.total,
      ),
    ];
  }

  // 編輯支付方式
  void _showPaymentMethod() {
    // 多重支付
    MultiplePaymentView(
      total: controller.draft.total,
      // multiplePayment: controller.draft.multiplePayment ?? defaultPayment,
      multiplePayment: controller.draft.noMultiplePayment
          ? defaultPayment
          : controller.draft.multiplePayment,
      enabled: controller.paymentEnabled,
    ).sheet<Iterable<MultiplePayment>>().then(
      (value) {
        if (value != null) {
          controller.draft.multiplePayment = List.from(value);
          // 填入實收
          final paid = value.fold<num>(0.0, (previousValue, element) {
            return previousValue + element.money;
          });
          controller.paidEditing.text = paid.decimalStyle;
          controller.draft.paid = paid;
          logger.d(
              '[OrdersSumUpView] _showPaymentMethod: draft.paid.refreshDraft');
          controller.refreshDraft();
        }
      },
    );
  }

  void _showAlert() {
    final paid = controller.data.isPaid;
    final message = paid ? '已付款無法更動，如須異動請重新下單' : '請點選支付方式，以更改金額';
    DialogGeneral.alert(message).dialog();
  }

  ///
  /// 顯示優惠券詳細訊息
  ///
  void _showCouponDetail() {
    Get.toNamed(
      Routes.COUPON_DETAIL,
      parameters: <String, String>{
        Keys.Data: QrFormat(
          memberId: controller.draft.memberId,
          memberCouponId: controller.draft.memberCouponId,
        ).toRawJson(),
        'actions': '0',
      },
    );
  }
}

extension ExtensionOrdersPostReq on OrdersPostReq {
  String get orderTypeName {
    if (type.orderType.isDinner) {
      return '餐點';
    }
    if (type.orderType.isRetail) {
      return '商品';
    }
    return '';
  }
}
