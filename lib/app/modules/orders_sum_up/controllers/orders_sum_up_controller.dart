import 'dart:async';
import 'dart:math';

import 'package:bpscm/bpscm.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_sunmi_printer/flutter_sunmi_printer.dart';
import 'package:muyipork/app/data/models/other/line_pay_setting.dart';
import 'package:muyipork/app/data/models/req/setting_order_fee.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/app/providers/line_pay_provider.dart';
import 'package:muyipork/app/providers/local_invoice_provider.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:muyipork/app/providers/setting_provider.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:muyipork/app/data/models/other/coupon.dart';
import 'package:muyipork/app/data/models/other/member.dart';
import 'package:muyipork/app/data/models/other/setting_point.dart';
import 'package:muyipork/app/modules/receipt_sticker_printing/controllers/receipt_sticker_printing_controller.dart';
import 'package:muyipork/app/providers/coupon_provider.dart';
import 'package:muyipork/app/providers/member_provider.dart';
import 'package:muyipork/app/providers/order_provider.dart';
import 'package:muyipork/app/providers/product_provider.dart';
import 'package:muyipork/enums.dart';
import 'package:okshop_esc_pos/okshop_esc_pos.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:screenshot/screenshot.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/req/orders_post_req.dart';
import 'package:muyipork/app/data/models/res/orders_get_res.dart';
import 'package:muyipork/app/data/models/res/order_root.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/routes/app_pages.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';

class OrdersSumUpController extends GetxController
    with StateMixin<OrdersPostReq> {
  final _disposable = Completer();
  final OrderProvider orderProvider;
  final ProductProvider productProvider;
  final MemberProvider memberProvider;
  final CouponProvider couponProvider;
  final InvoiceProvider invoiceProvider;
  final PrinterProvider printerProvider;
  final SettingProvider settingProvider;
  final LinePayProvider linePayProvider;
  final LocalInvoiceProvider localInvoiceProvider;

  ApiProvider get apiProvider => memberProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => memberProvider.boxProvider;

  // 會員
  final _member = Rx<Member>(null);
  Member get member => _member.value;
  final vip = false.obs;

  // 優惠券
  final _coupon = Rx<Coupon>(null);
  Coupon get coupon => _coupon.value;

  // 開發工具
  final devtool = false.obs;

  //正在編輯當中準備要送出的 Orders 結構
  final _draft = OrdersPostReq().obs;
  OrdersPostReq get draft => _draft.value;
  final isPostingOrder = false.obs;

  //這只會在後結模式有 (因為需要合併訂單)
  final orderList = <OrderSummary>[].obs;
  String get orderNumber {
    if (orderList.length == 1) {
      return orderList.elementAt(0).orderNumber ?? '';
    }
    return '';
  }

  //執行模式 (前後結差異)
  BrandsType checkoutType = BrandsType.Max;

  //UI 元件 Controllers.
  final pointEditing = TextEditingController();
  final discountEditing = TextEditingController();
  final additionalChargeEditing = TextEditingController();
  final paidEditing = TextEditingController();
  final receiptEditing = TextEditingController();
  final itemReceiptEditing = TextEditingController();
  final vatNumberEditing = TextEditingController();
  final carrierIdEditing = TextEditingController();

  num get totalPoints {
    return _member.value?.points ?? 0;
  }

  // 本地單次服務費開關
  final _serviceFeeEnabled = true.obs;
  bool get serviceFeeEnabled => _serviceFeeEnabled.value;
  set serviceFeeEnabled(bool value) => _serviceFeeEnabled.value = value;
  // 為了讓 obx 觸發，實際上不使用
  final _invoiceSwitcher = Switcher.On.obs;
  // 本地單次發票開關
  set invoiceSwitcher(Switcher value) {
    _invoiceSwitcher.value = value;
    final localSettings = prefProvider.localSettings;
    localSettings.printInvoice = value.index;
    prefProvider.localSettings = localSettings;
  }

  Switcher get invoiceSwitcher {
    _invoiceSwitcher.value;
    return prefProvider.localSettings.nnPrintInvoice.switcher;
  }

  // 最後計算發票開關
  bool get invoiceEnabled {
    // 總開關
    if (!(prefProvider.brandsInvoice?.invoiceEnabled ?? false)) {
      logger.d('[OrdersSumUpController] 停用發票 - 總開關關閉');
      return false;
    }
    // 單次開關啟用，但是關閉
    if (prefProvider.brandsInvoice.invoiceSwitchEnabled) {
      if (invoiceSwitcher.isOff) {
        logger.d('[OrdersSumUpController] 停用發票 - 單次開關關閉');
        return false;
      }
    }
    logger.d('[OrdersSumUpController] 啟用發票');
    return true;
  }

  // 發票截圖
  final invoiceScreenshotController = ScreenshotController();
  final widgetUpdater = Completer();
  final _invoice = Rx<Invoice>(null);
  Invoice get invoice => _invoice.value;

  // LINE Pay
  final _linePayCode = ''.obs;
  String get linePayCode => _linePayCode.value;
  set linePayCode(String value) => _linePayCode.value = value;
  // 掃描後需變更顯示狀態用
  final linePayEditing = TextEditingController();
  final _linePayEnabled = false.obs;
  bool get linePayEnabled => _linePayEnabled.value;
  final _linePaySwitcher = true.obs;
  bool get linePaySwitcher => _linePaySwitcher.value;
  set linePaySwitcher(bool value) => _linePaySwitcher.value = value;

  OrdersSumUpController({
    @required this.productProvider,
    @required this.memberProvider,
    @required this.invoiceProvider,
    @required this.orderProvider,
    @required this.couponProvider,
    @required this.printerProvider,
    @required this.settingProvider,
    @required this.linePayProvider,
    @required this.localInvoiceProvider,
  }) {
    // 初始數值
    final localSettings = prefProvider.localSettings;
    receiptEditing.text = '${localSettings.printReceiptCount ?? 1}';
    itemReceiptEditing.text = '${localSettings.printItemReceiptCount ?? 1}';
  }

  // 顯示原價服務費，條件:
  // 1. 餐飲內用
  // 2. 設定啟用原價服務費
  // 3. 餐飲現場點餐
  bool get serviceFeeOriginVisible {
    return prefProvider.settingData.settingOrderFee.serviceFeeType.isOrigin &&
        [OrderType.DinnerHere, OrderType.DinnerOrder].contains(draft.orderType);
  }

  // 顯示折價後服務費，條件:
  // 1. 餐飲內用
  // 2. 設定啟用折價後服務費
  // 3. 餐飲現場點餐
  bool get serviceFeeDiscountVisible {
    return prefProvider.settingData.settingOrderFee.serviceFeeType.isDiscount &&
        [OrderType.DinnerHere, OrderType.DinnerOrder].contains(draft.orderType);
  }

  // 顯示金流手續費，條件:
  // 1. 零售
  // 2. 線上
  bool get paymentFeeVisible {
    return draft.orderType.isRetail && draft.orderSource.isLine;
  }

  // 顯示運費，條件:
  // 1. 零售宅配
  // 2. 設定啟用運費
  // TODO: 3. 餐飲運費
  bool get shippingFeeVisible {
    return draft.orderType.isRetailDelivery &&
        prefProvider.shippingDelivery.enabled;
  }

  // 顯示積點折抵，條件: 有會員
  bool get pointDiscountVisible {
    return draft.memberId is num && draft.memberId > 0;
  }

  final _data = Rx<OrderRoot>(null);
  OrderRoot get data => _data.value;

  void _initObservable() {
    // vip 旗標
    vip.stream
        .asBroadcastStream()
        // line 訂單跳過 vip 資格變更內容
        // .skipWhile((element) => draft.source.orderSource.isLine)
        // .where((event) => draft.source.orderSource.isApp)
        .takeUntil(_disposable.future)
        .listen(
      (event) {
        // 非 vip 則移除 vip 商品
        if (!event) {
          draft.items.removeWhere((element) {
            final p = productProvider.getProduct(element.productId);
            return p.isVip != null && p.isVip.switcher.isOn;
          });
        }
        // 重新計算價格
        draft.restoreAdditionProducts(productProvider);
        draft.items.forEach((element) {
          final product = productProvider.getProduct(element.productId);
          final price = event ? product.vipPrice : product.price;
          element.calFinalPrice(price ?? 0);
        });
        // 更新
        logger.d('[OrdersSumUpController] vip.stream: refreshDraft');
        refreshDraft();
      },
    );
    final memberStream = _member.stream.asBroadcastStream();
    memberStream
        // 特殊: 跳過第一次更新會員資料，防止載入最新價格
        .skip(1)
        .takeUntil(_disposable.future)
        .listen(
      (event) {
        // 空值表示無會員
        vip.value = event?.isVip?.switcher?.isOn ?? false;
      },
    );
    // 無會員，移除積點相關資訊
    memberStream
        .where((event) => event == null)
        .takeUntil(_disposable.future)
        .listen(
      (event) {
        draft.redeemMemberPoints = null;
        draft.pointDiscountLimit = null;
        draft.pointGet = null;
        logger.d('[OrdersSumUpController] memberStream: refreshDraft');
        refreshDraft();
      },
    );

    final draftStream = _draft.stream.asBroadcastStream();
    // 優惠券
    draftStream
        .map((event) => event.memberCouponId)
        .distinct()
        .asyncMap((event) {
          if (event != null) {
            return couponProvider.getMemberCoupon(
                draft.memberId, draft.memberCouponId);
          } else {
            resetCoupon();
          }
        })
        .takeUntil(_disposable.future)
        .listen(
          (event) {
            if (event == null || event.id == null) {
              resetCoupon();
            } else {
              _coupon.value = event;
              // FIXME: 計算優惠
              logger.d('[OrdersSumUpController] draftStream: refreshDraft');
              refreshDraft();
            }
          },
        );
    // 監聽會員
    draftStream
        .map((event) => event.memberId)
        .distinct()
        .where((event) {
          final ret = event != null;
          if (!ret) {
            _member.value = null;
          }
          return ret;
        })
        .asyncMap((event) => _getMember(event))
        .takeUntil(_disposable.future)
        .listen((event) => _member.value = event);
  }

  @override
  void onInit() async {
    super.onInit();
    this._initObservable();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    this._disposable.complete();
  }

  Future<void> onRefresh() async {
    if (Get.arguments is OrdersPostReq) {
      //前結
      checkoutType = BrandsType.BeforeDinner;
      _draft.value = Get.arguments;
      // 預設現金
      draft.paymentMethodId ??= AppPayMethod.Cash.index;
      //決定小計數字
      _calCoupon();
      //前結必定是店員點餐
      draft.source = OrderSource.App.index;
      //不會有 List<Order>
      super.change(draft, status: RxStatus.success());
    } else if (Get.arguments is List<OrderSummary>) {
      //後結
      checkoutType = BrandsType.AfterDinner;
      //後結合併訂單流程。
      orderList.assignAll(Get.arguments);
      //特例: 判斷實質上需不需要做合併，如果兩張以上才需要，只有一張的話我們還是只能用 postOrder ordersPostReq 處理
      if (multipleOrders == true) {
        //這邊要做一個假的暫時的 ordersPostReq 來做為編輯用
        //注意這個編輯用 ordersPostReq 不會有詳細商品項
        _draft.value = await generateTempOrdersPostReq(orderList);
        // 預設現金
        draft.paymentMethodId ??= AppPayMethod.Cash.index;
        super.change(draft, status: RxStatus.success());
      } else if (orderList.length == 1) {
        final order = orderList.elementAt(0);
        //只有一張
        _data.value =
            await orderProvider.getOrderDetail(order.id, cacheFirst: true);
        // HACK: 模擬綠界信用卡
        // ordersOrderIdGetRes.data.orderPayments.add(OrderPayment(
        //   name: AppPayMethod.ECPay.name,
        //   payMethodId: AppPayMethod.ECPay.index,
        //   paymentMethodId: AppPayMethod.ECPay.index,
        // ));
        // 從詳細資訊產生可送出資料
        _draft.value = data.asOrdersPutReq(productProvider: productProvider);
        // 從訂單取得會員
        // if (data.data.memberId != null) {
        //   memberProvider
        //       .getMember(data.data.memberId)
        //       .then((value) => _member.value = value);
        // }
        // 預設現金
        draft.paymentMethodId ??= AppPayMethod.Cash.index;
        // 發票開關
        draft.invoice = invoiceEnabled;
        super.change(draft, status: RxStatus.success());
      }
    } else {
      //智障行為
      checkoutType = BrandsType.Max;
    }
    //這邊嘗試初始化界面
    if (draft != null) {
      //總之先算一次結算數字
      _calCoupon();
      draft.recalculate();

      discountEditing.text = draft.discount.abs().decimalStyle;
      // additionalChargeEditing.text = '${draft.additionalCharges}';
      additionalChargeEditing.text =
          (draft.additionalCharges ?? 0).decimalStyle;
      paidEditing.text = draft.paid.decimalStyle;
      vatNumberEditing.text = draft.vatNumber;
      carrierIdEditing.text = draft.carrierId;
      if (draft.redeemMemberPoints != null) {
        final redeemMemberPoints = draft.redeemMemberPoints ?? 0;
        pointEditing.text = redeemMemberPoints != 0
            ? '${redeemMemberPoints.round().abs()}'
            : '';
      }
    }
    // 取得 line pay 設定
    final linePaySetting = await _linePaySettings();
    _linePayEnabled.value = linePaySetting.offline.switcher.isOn;
  }

  // TODO: refactor this.
  //從一堆 Order 做出一個暫時的 OrdersPostReq 來容許編輯
  Future<OrdersPostReq> generateTempOrdersPostReq(
      List<OrderSummary> orders) async {
    final futures =
        orders.map((e) => orderProvider.getOrderDetail(e.id, cacheFirst: true));
    final ls = await Future.wait(futures);
    final req = ls.fold<OrdersPostReq>(null, (previousValue, element) {
      if (previousValue == null) {
        final d = element.asOrdersPutReq(productProvider: productProvider);
        d.redeemMemberPoints = (d.redeemMemberPoints ?? 0).abs();
        return d;
      }
      // 累加使用點數
      previousValue.redeemMemberPoints +=
          (element.data?.redeemMemberPoints ?? 0).abs();
      previousValue.items ??= [];
      previousValue.items.addAll(element.normalItems);
      return previousValue;
    });
    // 餐飲內用優先
    if (orders.any((element) => element.orderType.isDinnerHere)) {
      req.type = OrderType.DinnerHere.index;
    }
    // 取得第一位會員
    final order = orders.firstWhere(
      (element) {
        final memberId = element.member?.id;
        return memberId is num && memberId > 0;
      },
      orElse: () => OrderSummary(),
    );
    req.memberId = order.member?.id;
    return req;
  }

  num get serviceFee {
    total;
    return draft.nnFee;
  }

  SettingOrderFee get settingOrderFee {
    return serviceFeeEnabled
        ? prefProvider.settingData.settingOrderFee
        : SettingOrderFee(
            type: ServiceFeeType.None.index,
            percent: 0,
          );
  }

  num get memberCouponDiscount {
    total;
    return draft.memberCouponDiscount;
  }

  num get memberCouponExtraPrice {
    total;
    return draft.memberCouponExtraPrice;
  }

  num get _serviceFeeFromOrderList {
    final price = orderList.where((e) => e.orderType.isDinnerHere).fold<num>(0,
        (previousValue, element) {
      final order = orderProvider.getOrderDetailFromLocalStorage(element.id);
      return previousValue + (order?.normalItemsPrice ?? 0);
    });
    return settingOrderFee.calServiceFee(price, draft.nnDiscountAbs);
  }

  num get total {
    // 必須先計算優惠券，會影響總價計算
    _calCoupon();
    // 計算總價
    final servicesFee = multipleOrders ? _serviceFeeFromOrderList : null;
    final ret = draft.getTotal(
      serviceFee: servicesFee,
      coupon: coupon,
      // 運費
      amb: prefProvider.shippingDelivery.getAmb(draft.shippingMethod),
      // 單次服務費有勾選
      settingOrderFee: settingOrderFee,
    );
    // 由總價計算獲得點數
    draft.pointGet = _calPointGet(ret);
    return ret;
  }

  // 依照執行模式來決定要跑 postOrders 還是 postOrdersCombining
  Future<bool> submit() async {
    if (BrandsType.BeforeDinner == checkoutType) {
      // 前結流程
      await _tryCheckoutBeforeMeal();
      return true;
    } else if (BrandsType.AfterDinner == checkoutType) {
      // 後結流程
      await _tryCheckoutAfterMeal();
      return true;
    } else {
      //Yeah WTF you're doing?
      return false;
    }
  }

  Future<void> _linePay(num orderId, String otk) async {
    if (linePayEnabled && otk.isNotEmpty) {
      try {
        final res = await linePayProvider.linePayCode(orderId, otk);
        if ("0000" != res.returnCode) {
          throw res.returnMessage;
        }
      } catch (e) {
        rethrow;
      }
    }
  }

  // 判斷合併結帳
  bool get multipleOrders => orderList.length > 1;

  // 後結帳
  Future<void> _tryCheckoutAfterMeal() async {
    //Send post Order. (這邊有點詭異，OrdersCombining 不接受只有一張訂單，因此只有一張訂單的狀況我們要用 postOrders)
    final future =
        multipleOrders ? _postFinishedOrdersCombining : _putFinishedOrder;
    final orderId = await future();

    // 計算積點 (取消，伺服器會計算)
    // if (draft.memberId != null) {
    //   await _calculateMemberPoint();
    // }

    await _postUpsertOrder(orderId);
    // 新增 line pay 付款
    await _linePay(orderId, linePayCode);
  }

  Future<void> _postUpsertOrder(num orderId) async {
    // 發票
    if (true == draft.invoice && draft.total is num && draft.total > 0) {
      final order =
          await orderProvider.getOrderDetail(orderId, cacheFirst: true);
      // 產生發票
      final invoiceNumber = await _postInvoice(order);
      // 有字軌則列印發票
      if (invoiceNumber != null &&
          invoiceNumber.isNotEmpty &&
          draft.isNeedToPrint) {
        // 列印發票
        _invoice.value = await _createInvoice(orderId);
        await _printInvoice(invoice);
      }
    }

    final orderRoot =
        await orderProvider.getOrderDetail(orderId, cacheFirst: true);
    // print receipt
    final receipt = orderRoot.asReceipt(
      storeName: prefProvider.brandsInfo?.name ?? '',
      userName: prefProvider.jwt?.name ?? '',
    );
    await _printReceiptLite(receipt);
    await _printReceiptItem(receipt);
  }

  // 送出合併訂單
  // Return orderId if success
  Future<num> _postFinishedOrdersCombining() {
    // 從編輯過後的 ordersPostReq 跟傳入的訂單製出 OrdersCombiningPostReq
    final orderIds = orderList.map((element) => element.id);
    final ordersCombiningPostReq = draft.asOrdersCombiningPostReq(orderIds);
    // 特殊: 重構多重支付方式
    ordersCombiningPostReq.refactorMultiplePayment();
    // TODO: 取得訂單詳情使用點數狀況
    // orderProvider.getOrderDetail(id, cacheFirst: true);
    // 特殊: 已使用積點的 line 訂單不傳積點資訊
    if (redeemMemberPointsReadonly) {
      ordersCombiningPostReq.redeemMemberPoints = null;
    }
    return orderProvider.postOrdersCombining(ordersCombiningPostReq);
  }

  // 先結帳
  Future<void> _tryCheckoutBeforeMeal() async {
    //Send post Order.
    final orderId = await _postFinishedOrder();
    // print sticker
    await Get.toNamed(
      Routes.RECEIPT_STICKER_PRINTING,
      parameters: {Keys.Tag: '$orderId'},
      arguments: PrintingArgs(
        printingMode: PrintingMode.PrintAllAndLeave,
        printingPageSetting: PrintingPageSetting.StickerOnly,
        orderId: orderId,
      ),
    );
    await _postUpsertOrder(orderId);
    // 新增 line pay 付款
    await _linePay(orderId, linePayCode);
  }

  // 送出訂單 (後結)
  // Return orderId if success
  Future<num> _putFinishedOrder() {
    // 2：訂單完成
    draft.status = OrderStatus.Completed.index;
    //已付款
    draft.paymentStatus = PaymentStatus.Paid.index;
    // server 會檢查收件人資料
    if (draft.receiverName == null || draft.receiverName.isEmpty) {
      draft.receiverName = '-';
    }
    if (draft.receiverPhone == null || draft.receiverPhone.isEmpty) {
      draft.receiverPhone = '-';
    }
    if (draft.receiverCityId == null || draft.receiverCityId <= 0) {
      draft.receiverCityId = 1;
    }
    if (draft.receiverCityareaId == null || draft.receiverCityareaId <= 0) {
      draft.receiverCityareaId = 1;
    }
    if (draft.receiverAddress == null || draft.receiverAddress.isEmpty) {
      draft.receiverAddress = '-';
    }
    // server 會檢查訂購人姓名
    if (draft.buyerName == null || draft.buyerName.isEmpty) {
      draft.buyerName = draft.receiverName;
    }
    // server 會檢查訂購人電話
    if (draft.buyerPhone == null || draft.buyerPhone.isEmpty) {
      draft.buyerPhone = draft.receiverPhone;
    }
    // 特殊: 重構多重支付
    draft.refactorMultiplePayment();
    // OrdersOrderIdPutRes result = await apiProvider.putOrdersOrderIdReedit(
    //     orderList[0].id, ordersPostReq);
    // 特殊: 已使用積點的 line 訂單不傳積點資訊
    if (redeemMemberPointsReadonly) {
      draft.redeemMemberPoints = null;
    }
    // 特殊: 已使用優惠券的 line 訂單不傳優惠券資訊
    if (draft.source.orderSource.isLine) {
      draft.memberCouponId = null;
    }
    return orderProvider.putOrder(data.id, draft);
  }

  ///
  /// line 訂單並且已使用積點，不可變更
  ///
  bool get redeemMemberPointsReadonly {
    // 多張訂單
    if (multipleOrders == true) {
      for (final order in orderList) {
        final orderDetail =
            orderProvider.getOrderDetailFromLocalStorage(order.id);
        final redeemMemberPoints = orderDetail?.data?.redeemMemberPoints;
        if (redeemMemberPoints is num && redeemMemberPoints.abs() > 0) {
          return true;
        }
      }
      return false;
    }
    // 取得原訂單使用積點數
    final usage = (data?.data?.redeemMemberPoints ?? 0).abs();
    return draft.source.orderSource.isLine && usage > 0;
  }

  // 建立訂單 (前結)
  // Return orderId if success
  Future<num> _postFinishedOrder() async {
    // 訂單完成
    draft.status = OrderStatus.Completed.index;
    // 已付款
    draft.paymentStatus = PaymentStatus.Paid.index;
    // 特殊: 重構多重支付方式
    draft.refactorMultiplePayment();
    // TODO: refactor me
    // 特殊: 已使用優惠券的 line 訂單不傳優惠券資訊
    // 前結帳不會有 line 訂單
    // if (draft.source.orderSource.isLine) {
    //   draft.memberCouponId = null;
    // }
    return orderProvider.postOrders(draft);
  }

  //
  bool get paymentDisabled => data != null && data.isPaid;
  // 已付款不可變更支付方式
  bool get paymentEnabled => !paymentDisabled;

  bool get inputEnabled {
    // 多重支付已設定不可編輯輸入
    if (draft.isMutiplePayment) {
      return false;
    }
    // 特殊: 已付款不可編輯輸入
    if (data != null && data.isPaid) {
      return false;
    }
    return true;
  }

  void refreshDraft() {
    logger.d('[OrdersSumUpController] refreshDraft');
    _draft.refresh();
  }

  ///
  /// 清除會員 (變更會員時，需同時異動優惠券)
  ///
  void resetMember() {
    draft.memberId = null;
    _member.value = null;
    logger.d('[OrdersSumUpController] resetMember: refreshDraft');
    refreshDraft();
  }

  ///
  /// 清除優惠券
  ///
  void resetCoupon() {
    draft.memberCouponId = null;
    draft.memberCouponDiscount = null;
    draft.memberCouponExtraPrice = null;
    _coupon.value = null;
    logger.d('[OrdersSumUpController] resetCoupon: refreshDraft');
    refreshDraft();
  }

  // 計算獲得點數
  num _calPointGet(num total) {
    // 取得設定
    final sp = prefProvider.settingPoint.firstWhere(
      (element) => draft.storeType == element.storeType,
      orElse: () => SettingPoint(),
    );
    if (sp.isAvailable && draft.memberId != null) {
      // 無條件捨去
      final ret = (total / sp.cashRatio).floor();
      logger.d('[OrdersSumUpController] pointGet($ret)');
      return ret;
    }
    return 0;
  }

  bool containsPayMethod(AppPayMethod value) {
    if (prefProvider.settingPay != null) {
      final pay = prefProvider.settingPay.firstWhere(
        (element) => value.index == element.payMethodId,
        orElse: () => null,
      );
      if (pay != null && draft.multiplePayment != null) {
        return draft.multiplePayment
            .any((element) => pay.id == element.paymentMethodId);
      }
    }
    return false;
  }

  String get couponMessage {
    if (coupon != null) {
      if (coupon.message != null && coupon.message.isNotEmpty) {
        return coupon.message;
      }
      // 檢查線上或線下
      if (coupon.isOnline != draft.source) {
        if (coupon.isOnline.switcher.isOn) {
          return '線上優惠券只能於 line 下單使用';
        } else {
          return '線下優惠券只能於現場使用';
        }
      }
      // 檢查餐飲或零售
      // 特殊:
      // 1: 餐飲
      // 2: 零售
      // FIXME:
      // if (coupon.storeType != draft.orderType.storeType) {
      //   if (storeType.isDinner) {
      //     return '餐飲優惠券只適用於餐飲訂單';
      //   } else {
      //     return '零售優惠券只適用於零售訂單';
      //   }
      // }
      // 檢查最低金額
      final minPrice = coupon.minPrice ?? 0;
      if (draft.itemsTotal < minPrice) {
        return '提醒：未達最低消費金額$minPrice元，不得使用本券!';
      }
    }
    return '';
  }

  // FIXME: 缺少型態對應的數值 (可能是 usagePeriod)
  // draft.memberCouponExtraPrice 加價
  // draft.memberCouponDiscount 減價
  void _calCoupon() {
    if (coupon != null) {
      coupon.minPrice ??= 0;
      coupon.discount ??= 0;
      coupon.extraPrice ??= 0;
      draft.memberCouponExtraPrice = 0;
      draft.memberCouponDiscount = 0;
      // 低消判斷
      if (draft.normalItemsPrice > coupon.minPrice) {
        switch (coupon.promotionType.promotionType) {
          case PromotionType.Off: // 打折
            draft.memberCouponDiscount =
                (100 - coupon.discount) * 0.01 * draft.normalItemsPrice;
            break;
          case PromotionType.Discount: // 折價
            draft.memberCouponDiscount = coupon.discount;
            break;
          case PromotionType.Gift: // 贈送
          case PromotionType.Upgrade: // 升級
            draft.memberCouponExtraPrice = coupon.extraPrice;
            break;
          default:
        }
      }
      // 最多只能折小計
      draft.memberCouponDiscount =
          min(draft.memberCouponDiscount, draft.normalItemsPrice);
    } else {
      draft.memberCouponExtraPrice = null;
      draft.memberCouponDiscount = null;
    }
  }

  ///
  /// 列印發票
  ///
  Future<void> _printInvoice(Invoice invoice) async {
    // 使用 V2 列印
    try {
      await SunmiUtil.printInvoice(invoice);
    } catch (e) {
      logger.e(e);
    }
    // 使用商米印表機列印
    try {
      await _printInvoiceByEsc();
    } catch (e) {
      logger.e(e);
    }
  }

  ///
  /// 列印消費明細
  ///
  Future<void> _printReceiptLite(Receipt receipt) async {
    final enabled =
        prefProvider.localSettings?.printReceipt ?? Switcher.On.index;
    if (enabled.switcher.isOn) {
      final count = prefProvider.localSettings?.printReceiptCount ?? 1;
      // 找出印表機
      final printers = printerProvider
          .getPrinterWithCategories([PrintType.receiptLite.value]).where(
              (element) => element.status.switcher.isOn);
      for (var i = 0; i < count; i++) {
        // 列印消費明細
        await SunmiUtil.printReceiptLite(receipt);
        // for (var j = 0; j < printers.length; j++) {
        //   final printer = printers.elementAt(j);
        //   await printer.printReceiptLite(receipt);
        // }
        // await Future.wait(printers.map((e) => e.printReceiptLite(receipt)));
        printers
            .where((element) => element.type == PrinterType.net.value)
            .forEach((printer) async {
          await printer.printReceiptLite(receipt);
        });
      }
    }
  }

  ///
  /// 列印商品明細
  ///
  Future<void> _printReceiptItem(Receipt receipt) async {
    final enabled =
        prefProvider.localSettings?.printItemReceipt ?? Switcher.On.index;
    if (enabled.switcher.isOn) {
      final count = prefProvider.localSettings.printItemReceiptCount ?? 1;
      // 找出印表機
      final printers = printerProvider
          .getPrinterWithCategories([PrintType.receiptItem.value]).where(
              (element) => element.status.switcher.isOn);
      for (var i = 0; i < count; i++) {
        await SunmiUtil.printReceiptItems(receipt);
        // for (var j = 0; j < printers.length; j++) {
        //   final printer = printers.elementAt(j);
        //   await printer.printReceiptItem(receipt);
        // }
        // await Future.wait(printers.map((e) => e.printReceiptItem(receipt)));
        printers
            .where((element) => element.type == PrinterType.net.value)
            .forEach((printer) async {
          // await printer.printReceiptItem(receipt);
          final bytes = await printer.receiptItemTask(receipt);
          printer.pushTask(bytes);
        });
      }
    }
  }

  String get displayShippingFee {
    // 顯示資訊
    final infos = <String>[];
    // 常溫、低溫運送
    infos.add(draft.shippingMethod.name);
    // 取得運費設定
    final amb = prefProvider.shippingDelivery.getAmb(draft.shippingMethod);
    // 免運門檻
    if (draft.normalItemsPrice > amb.shippingFree) {
      infos.add('(已達免運門檻 ${amb.shippingFree.decimalStyle})');
    }
    // 計算運費
    total;
    // 運費
    infos.add(draft.shippingFee.decimalStyle);
    return infos.join(' ');
  }

  Future<void> _printInvoiceByEsc() async {
    try {
      // 等待內容變更完成
      await widgetUpdater.future;
      final image = await invoiceScreenshotController.capture(
        pixelRatio: 1.0,
        delay: 200.milliseconds,
      );
      // 尋找 printers
      final ids = [PrintType.invoice.value];
      final printers = printerProvider.getPrinterWithCategories(ids);
      for (var printer in printers) {
        // 使用圖片方式列印 invoice
        final bytes = await printer.invoiceTask(invoice, image);
        printer.pushTask(bytes);
        // 只印一張
        break;
      }
    } catch (e) {
      rethrow;
    }
  }

  // 訂單附上發票 (先)
  Future<String> _postInvoice(OrderRoot order) async {
    // _validateInvoice();
    // 發票號碼
    final invoiceNumber = await localInvoiceProvider.getInvoiceNumber(
      seller: prefProvider.brandsInfo.taxId,
      date: order.checkoutAt ?? order.data.createdAt.localAt,
    );
    if (invoiceNumber != null && invoiceNumber.isNotEmpty) {
      draft.invoiceNumber = invoiceNumber;
      // 隨機碼
      draft.randomNumber = InvoiceProvider.genRandomNumber();
      // 是否列印紙本
      draft.invoicePaper = draft.isNeedToPrint;
      // 載具/愛心碼
      final carrierId = this.draft.carrierId;
      draft.carrierId = Utils.isCarrierId(carrierId) ? carrierId : '';
      draft.npoBan = Utils.isNpoBan(carrierId) ? carrierId : '';
      // 載具類別
      if (draft.carrierId != null && draft.carrierId.isNotEmpty) {
        draft.carrierType = CarrierType.Mobile.index; // 3: 手機條碼
      }
      final data = draft.toOrdersOrderIdInvoicePostReq();
      // 送出發票訊息到 okshop server
      final orderId = await orderProvider.postOrderInvoice(order.data.id, data);
      if (orderId is num && orderId > 0) {
        try {
          throw '強制丟到背景上傳發票到金財通 order id: $orderId';
          await _syncInvoice(orderId);
        } catch (e) {
          // 上傳發票失敗，加入補上傳佇列
          final box = await boxProvider.getLazyBox(kKeyBoxOrderInvoice);
          final key = '$orderId.${BpscmInvoiceStatus.Invoice.value}';
          await box.put(key, invoiceNumber);
        }
      }
    }
    return invoiceNumber;
  }

  // 上傳金財通 (後)
  Future<String> _syncInvoice(num orderId) async {
    // HACK: 模擬上傳失敗
    // throw '模擬上傳失敗 order id: $orderId';
    final order = await orderProvider.getOrderDetail(orderId);
    final brandsInfo = prefProvider.brandsInfo;
    // 上傳金財通物件
    final data = order.asPosInvoiceNewsReq(
      apiKey: prefProvider.invoiceApiKey,
      taxId: brandsInfo.taxId,
      posBan: kPosBAN,
      storeName: brandsInfo.name,
      storeCode: brandsInfo.code,
    );
    // 更新品項稅率
    for (var element in data.invoiceDetails) {
      // 預設應稅
      var taxType = TaxType.TX;
      try {
        // 取得產品
        final product = await productProvider.getProductDetail(
          num.tryParse(element.relateNumber ?? '') ?? 0,
          cacheFirst: true,
        );
        // 取得產品的稅率 (預設應稅)
        taxType = product?.taxTypeEnum ?? TaxType.TX;
      } catch (e) {
        logger.e(e);
      }
      // 儲存稅別
      element.remark = '${taxType.bpscmType}';
    }
    // 計算稅率總和
    data.refresh();
    // 上傳發票
    final ret = await invoiceProvider.postPosInvoiceNewsSingle(data);
    if ('OK' == ret.status) {
      return ret.invoiceNo;
    }
    throw ret.message;
  }

  ///
  /// 產生發票物件
  ///
  Future<Invoice> _createInvoice(num orderId) async {
    final order = await orderProvider.getOrderDetail(orderId, cacheFirst: true);
    final invoice = order.asInvoice();
    //
    final brandsInfo = prefProvider.brandsInfo;
    // final brandsInvoice = this.prefProvider.brandsInvoice;
    // final hasBuyer = invoice.buyer.isNotEmpty ?? false;
    // final taxType = brandsInvoice.taxTypeForServer;
    // final taxRate = InvoiceProvider.getTaxRate(hasBuyer, taxType);
    // 列印
    invoice.storeName = brandsInfo.name;
    invoice.printMark = Times.First.index; // 首次列印
    invoice.seller = brandsInfo.taxId;
    for (var item in invoice.items) {
      // 預設應稅
      var taxType = TaxType.TX;
      try {
        // 取得產品
        final product = await productProvider.getProductDetail(
          item.productId,
          cacheFirst: true,
        );
        // 取得產品的稅率
        taxType = product?.taxTypeEnum ?? TaxType.TX;
      } catch (e) {
        logger.e(e);
      }
      // 儲存稅別
      item.taxType = taxType.bpscmType;
    }
    invoice.refresh();
    return invoice;
  }

  Future<Member> _getMember(num id) async {
    final member = await memberProvider.getMember(id, cacheFirst: true);
    try {
      member?.points = await memberProvider.getMemberPointTotal(id);
    } catch (e) {
      // 會員封鎖時，取點數會失敗
      logger.e(e);
    }
    return member;
  }

  Future<LinePaySetting> _linePaySettings() async {
    final data = await settingProvider.getPayment(AppPayMethod.LinePay.index);
    return data.linePaySetting;
  }
}
