import 'dart:async';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/line_pay_setting.dart';
import 'package:muyipork/app/data/models/other/payment_instore.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/setting_provider.dart';
import 'package:muyipork/extension.dart';
import 'package:okshop_common/okshop_common.dart';

class PaymentLinePayController extends GetxController with StateMixin<String> {
  final _disposable = Completer();
  final SettingProvider settingProvider;
  ApiProvider get apiProvider => settingProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  // PaymentInstore: 付款方式共用結構，
  // TODO: rename ChannelPayment
  final _data = Rx<PaymentInstore>(null);
  PaymentInstore get data => _data.value;

  final _linePaySetting = Rx<LinePaySetting>(null);
  LinePaySetting get linePaySetting => _linePaySetting.value;

  bool get containsDinner => prefProvider.brandsType.containsDinner;

  // 技術上: 可同時有 '餐飲' 及 '零售' 的設定同時存在
  // 實務上: 並沒有販賣兩種同時存在的商品
  // 不過 '餐飲' 及 '零售' 同時存在的狀況會發生，
  // 以餐飲優先顯示處理此狀況
  String get titleText => containsDinner ? '餐飲商店' : '零售商店';

  Switcher get linePayOffline {
    linePaySetting.offline ??= Switcher.Off.index;
    return linePaySetting.offline.switcher;
  }

  set linePayOffline(Switcher value) {
    linePaySetting.offline = value.index;
  }

  Switcher get switcher {
    // 餐飲優先
    if (containsDinner) {
      data.statusDiner ??= Switcher.Off.index;
      return data.statusDiner.switcher;
    }
    // 零售
    data.status ??= Switcher.Off.index;
    return data.status.switcher;
  }

  set switcher(Switcher value) {
    if (containsDinner) {
      data.statusDiner = value.index;
    } else {
      data.status = value.index;
    }
    linePaySetting.online = value.index;
  }

  String get description {
    // 餐飲優先
    if (containsDinner) {
      data.descriptionDiner ??= '';
      return data.descriptionDiner;
    }
    // 零售
    data.description ??= '';
    return data.description;
  }

  set description(String value) {
    if (containsDinner) {
      data.descriptionDiner = value;
    } else {
      data.description = value;
    }
  }

  PaymentLinePayController({
    @required this.settingProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
  }

  Future<void> onRefresh() async {
    try {
      _data.value =
          await settingProvider.getPayment(AppPayMethod.LinePay.index);
      _linePaySetting.value = data.linePaySetting;
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<bool> submit() async {
    // 特殊: 送出前序列化
    data.setting = linePaySetting.toRawJson();
    final ret =
        await settingProvider.putPaymethod(AppPayMethod.LinePay.index, data);
    return ret is num && ret > 0;
  }

  void refreshDraft() {
    _data.refresh();
  }
}
