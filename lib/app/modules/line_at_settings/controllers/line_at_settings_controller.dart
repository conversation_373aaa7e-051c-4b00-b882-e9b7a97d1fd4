import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/brands_info.dart';
import 'package:muyipork/app/data/models/other/brands_put.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/extension.dart';

class LineAtSettingsController extends GetxController with StateMixin<String> {
  final ApiProvider apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  // 編輯中的資料
  final _draft = BrandsPut().obs;
  BrandsPut get draft => _draft.value;
  BrandsInfo get data => prefProvider.brandsInfo;

  LineAtSettingsController({
    @required this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    //
  }

  Future<void> onRefresh() async {
    try {
      final ret = await apiProvider.getBrandsInfo();
      prefProvider.brandsInfo = ret;
      _draft.value = ret.asBrandsPut();
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  Future<bool> submit() async {
    try {
      final ret = await apiProvider.putBrands(draft);
      return ret != null && ret > 0;
    } catch (e) {
      rethrow;
    }
  }
}
