import 'dart:convert';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/req/id_sort.dart';
import 'package:muyipork/constants.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:okshop_model/okshop_model.dart';

import 'api_provider.dart';
import 'box_provider.dart';

class TableProvider {
  final ApiProvider apiProvider;
  BoxProvider get boxProvider => apiProvider.prefProvider.boxProvider;
  final cached = <num, Table>{}.obs;

  TableProvider({
    @required this.apiProvider,
  });

  Future<Iterable<Table>> getTables() async {
    final list = await apiProvider.getList(
      unencodedPath: 'tables',
      creator: (json) => Table.fromJson(json),
    );
    final entries = list.map((e) => MapEntry(e.id, e));
    cached.clear();
    cached.addEntries(entries);
    // save to storage
    final box = boxProvider.getGsBox(kBoxTable);
    box.erase();
    for (final table in list) {
      box.write('${table.id}', table.toJson());
    }
    return list;
  }

  String getDisplayName(num table1Id, num table2Id) {
    // read from storage
    final box = boxProvider.getGsBox(kBoxTable);
    if (box.hasData('$table1Id')) {
      final table1 = Table.fromJson(box.read('$table1Id'));
      table1.child ??= <Table>[];
      final table2 = table1.child.firstWhere(
        (element) => element.id == table2Id,
        orElse: () => Table(),
      );
      return '${table1.name ?? ''}${table2.name ?? ''}';
    }
    return '';
  }

  Future<num> delete(num id) async {
    final res = await apiProvider.delete<num>(
      'tables/$id',
      creator: (json) {
        if (json.containsKey(Keys.IsDeleted) && json[Keys.IsDeleted]) {
          return json['table_id'];
        }
        return null;
      },
    );
    // remove from storage
    final box = boxProvider.getGsBox(kBoxTable);
    box.remove('$res');
    return res;
  }

  // 更新區域、桌號名稱
  Future<num> put(num id, TableReq req) async {
    final res = await apiProvider.put(
      'tables/$id',
      data: req.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && json[Keys.IsUpdated]) {
          return json['table_id'];
        }
        return null;
      },
    );
    // save to storage
    final box = boxProvider.getGsBox(kBoxTable);
    box.write('$res', req.toJson());
    return res;
  }

  ///
  /// 建立
  ///
  Future<num> post(TableReq req) async {
    final res = await apiProvider.post(
      'tables',
      data: req.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsCreated) && json[Keys.IsCreated]) {
          return json['table_id'];
        }
        return null;
      },
    );
    // save to storage
    final box = boxProvider.getGsBox(kBoxTable);
    box.write('$res', req.toJson());
    return res;
  }

  ///
  /// 排序
  ///
  Future<bool> sort(Iterable<IDSort> it) async {
    final res = await apiProvider.post(
      'tables/sort',
      data: {
        'data': jsonEncode([...it]),
      },
      creator: (json) {
        if (json.containsKey(Keys.IsSorted.value)) {
          return json[Keys.IsSorted.value];
        }
        return null;
      },
    );
    if (res == true) {
      // save to storage
      final box = boxProvider.getGsBox(kBoxTable);
      for (var sort in it) {
        if (box.hasData('${sort.id}')) {
          final table = Table.fromJson(box.read('${sort.id}'));
          table.sort = sort.sort;
          box.write('${sort.id}', table.toJson());
        }
      }
    }
    return res;
  }
}
