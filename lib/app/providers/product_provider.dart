import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/product_categories_put.dart';
import 'package:muyipork/app/data/models/req/id_sort.dart';
import 'package:muyipork/app/data/models/req/products_get_qry.dart';
import 'package:muyipork/app/data/models/req/products_post_req.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/enums.dart';

import 'api_provider.dart';

class ProductProvider {
  final ApiProvider apiProvider;
  // 因 product 可以同時存在不同 category
  // 故 product id 會重複，使用 product id + category id 取 hash
  // product detail, 使用 product id 當 key
  final productCached = <num, ProductSingle>{}.obs;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;

  ProductProvider({
    @required this.apiProvider,
  });

  Iterable<AdditionProduct> getAdditionProductFromIds(Iterable<num> ids) sync* {
    ids ??= <num>[];
    final box = boxProvider.getGsBox(kBoxAdditionProduct);
    for (var id in ids) {
      if (box.hasData('$id')) {
        yield AdditionProduct.fromJson(box.read('$id'));
      }
    }
  }

  AdditionProduct getAdditionProductFromLocalStorage(num id) {
    final box = boxProvider.getGsBox(kBoxAdditionProduct);
    if (box.hasData('$id')) {
      return AdditionProduct.fromJson(box.read('$id'));
    }
    return AdditionProduct(
      additionCategoryId: 0,
      id: 0,
      kind: ProductKind.Max.index,
      name: '',
      price: 0,
      sort: 0,
    );
  }

  ///
  /// 判斷品項包含低溫宅配
  ///
  Future<ShippingType> getShippingType(Iterable<num> productIds) async {
    productIds ??= <num>[];
    for (var id in productIds) {
      final product = await getProductDetail(id, cacheFirst: true);
      if (ShippingType.Cold.index == product.shippingType) {
        return ShippingType.Cold;
      }
    }
    return ShippingType.Normal;
  }

  ///
  /// 取得產品詳細資料
  ///
  Future<ProductSingle> getProductDetail(num id, {bool cacheFirst}) async {
    final isValidId = id is num && id > 0;
    if (!isValidId) {
      throw '產品 id ($id) 不存在';
      // final ret = ProductSingle();
      // ret.init();
      // return ret;
    }

    if (true == cacheFirst && productCached.containsKey(id)) {
      return productCached[id];
    }
    // final box = boxProvider.getGsBox(kBoxProductSingle);
    // if (true == cacheFirst && box.hasData('$id')) {
    //   return ProductSingle.fromJson(box.read('$id'));
    // }

    final ret = await apiProvider.getData<ProductSingle>(
      unencodedPath: 'products/$id',
      creator: (value) => ProductSingle.fromJson(value),
    );
    productCached[id] = ret;
    // write to mem
    // box.write('$id', ret.toJson());
    return ret;
  }

  ///
  /// 刪除產品
  ///
  Future<num> deleteProduct(num id) async {
    final ret = await apiProvider.delete(
      'products/$id',
      creator: (json) {
        if (true == json[Keys.IsDeleted]) {
          return json['product_id'] ?? 0;
        }
        return 0;
      },
    );
    // update cache
    if (ret is num && ret > 0) {
      // remove from mem
      final box = boxProvider.getGsBox(kBoxProductInfo);
      final items = Iterable.castFrom(box.getValues())
          .map((e) => ProductInfo.fromJson(e))
          .where((e) => e.productId == id)
          .toList(growable: false);
      for (var item in items) {
        box.remove('${item.uniqueId}');
      }
      productCached.remove(ret);
      // remove from mem
      // final box = boxProvider.getGsBox(kBoxProductSingle);
      // box.remove('$id');
      return ret;
    }
    return 0;
  }

  Future<Iterable<ProductInfo>> getProducts(ProductsGetQry req) async {
    req.page ??= 1;
    req.limit ??= kLimit;
    final it = await apiProvider.getList(
      unencodedPath: 'products',
      filter: req.toJson(),
      creator: (value) => ProductInfo.fromJson(value),
    );
    // insert mem
    final box = boxProvider.getGsBox(kBoxProductInfo);
    for (var item in it) {
      box.write('${item.uniqueId}', item.toJson());
    }
    // TODO: save to local storage
    return it;
  }

  Future<void> fetchAllProductsWithKind(ProductKind kind) async {
    var page = 1;
    do {
      logger.d(
          '[ProductProvider] fetchAllProductsWithKind: kind($kind), page($page)');
      final req = ProductsGetQry(
        page: page,
        limit: kLimitMax,
        kind: kind.index,
      );
      final it = await getProducts(req);
      if (it.length < req.limit) {
        logger.d(
            '[ProductProvider] fetchAllProductsWithKind: kind($kind), 所有產品下載完成)');
        break;
      }
      page++;
      await 100.milliseconds.delay();
    } while (true);
  }

  ///
  /// 使用於登入後，遞迴取得所有產品
  ///
  Future<void> fetchAllProducts() async {
    logger.d('[ProductProvider] fetchAllProducts');
    final box = boxProvider.getGsBox(kBoxProductInfo);
    box.erase();
    for (var kind in ProductKind.values) {
      if (ProductKind.Max != kind) {
        try {
          logger.d('[ProductProvider] fetchAllProducts: $kind');
          await fetchAllProductsWithKind(kind);
        } catch (e) {
          logger.e(e);
        }
      }
    }
  }

  ///
  /// 取得本地所有的產品
  ///
  Iterable<ProductInfo> getProductsFromStorage() sync* {
    final box = boxProvider.getGsBox(kBoxProductInfo);
    for (var item in box.getValues()) {
      final product = ProductInfo.fromJson(item);
      yield product;
    }
  }

  ///
  /// 新增分類
  ///
  Future<num> postCategories(Category data) async {
    final ret = await apiProvider.post<num>(
      'categories',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsCreated) && true == json[Keys.IsCreated]) {
          if (json.containsKey('category_id')) {
            return json['category_id'];
          }
        }
        return 0;
      },
    );
    // update cache
    if (ret is num && ret > 0) {
      data.id = ret;
      final box = boxProvider.getGsBox(kBoxCategory);
      box.write('${data.id}', data.toJson());
    }
    return ret;
  }

  ///
  /// 更新分類
  ///
  Future<num> putCategories(Category data) async {
    final ret = await apiProvider.put(
      'categories/${data.id}',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          if (json.containsKey('category_id')) {
            return json['category_id'];
          }
        }
        return 0;
      },
    );
    // update cache
    if (ret != null && ret > 0) {
      final box = boxProvider.getGsBox(kBoxCategory);
      box.write('${data.id}', data.toJson());
    }
    return ret;
  }

  Iterable<ProductInfo> getProductsFromStorageWithCategory(
      Category category) sync* {
    final box = boxProvider.getGsBox(kBoxProductInfo);
    for (var item in box.getValues()) {
      final product = ProductInfo.fromJson(item);
      if (product.categoryId == category.id) {
        yield product;
      }
    }
  }

  Iterable<ProductInfo> getProductsFromStorageWithKind(ProductKind kind) sync* {
    final box = boxProvider.getGsBox(kBoxProductInfo);
    for (var item in box.getValues()) {
      final product = ProductInfo.fromJson(item);
      if (product.kind == kind.index) {
        yield product;
      }
    }
  }

  Iterable<Category> getCategoriesFromStorage(ProductKind kind) sync* {
    final box = boxProvider.getGsBox(kBoxCategory);
    for (var item in box.getValues()) {
      final cate = Category.fromJson(item);
      if (cate.kind == kind.index) {
        yield cate;
      }
    }
  }

  Iterable<Category> getAdditionCategoriesFromStorage(ProductKind kind) sync* {
    final box = boxProvider.getGsBox(kBoxAdditionCategory);
    for (var item in box.getValues()) {
      final cate = Category.fromJson(item);
      if (cate.kind == kind.index) {
        yield cate;
      }
    }
  }

  Category getAdditionCategoryWithId(num id) {
    final box = boxProvider.getGsBox(kBoxAdditionCategory);
    if (box.hasData('$id')) {
      return Category.fromJson(box.read('$id'));
    }
    return Category();
  }

  // 使用於登入後，遞迴取得所有分類
  Future<void> fetchAllCategories() async {
    for (var kind in ProductKind.values) {
      if (ProductKind.Max != kind) {
        try {
          await getCategories(kind);
          await 100.milliseconds.delay();
        } catch (e) {
          logger.e(e);
        }
      }
    }
  }

  // 使用於登入後，遞迴取得所有規格分類
  Future<void> fetchAllAdditionCategories() async {
    for (var kind in ProductKind.values) {
      if (ProductKind.Max != kind) {
        try {
          await getAdditionCategories(kind);
          await 100.milliseconds.delay();
        } catch (e) {
          logger.e(e);
        }
      }
    }
  }

  Future<Iterable<Category>> getCategories(ProductKind kind) async {
    final value = await apiProvider.getList<Category>(
      unencodedPath: 'categories',
      filter: {'kind': '${kind.index}'},
      creator: (value) => Category.fromJson(value),
    );
    final box = boxProvider.getGsBox(kBoxCategory);
    // remove current kind
    final items = Iterable.castFrom(box.getValues())
        .map((e) => Category.fromJson(e))
        .where((e) => e.kind == kind.index)
        .toList(growable: false);
    for (var item in items) {
      box.remove('${item.id}');
    }
    // insert mem
    for (var item in value) {
      box.write('${item.id}', item.toJson());
    }
    return value;
  }

  Future<bool> tryDeleteCategory(num categoryId, ProductKind kind) async {
    final filter = ProductsGetQry(
      page: 1,
      limit: 500,
      categoryId: categoryId,
      kind: kind.index,
    );
    // 檢查有使用到此分類的產品
    final products = await getProducts(filter);
    if (products.isNotEmpty) {
      throw products;
    }
    return deleteCategory(categoryId);
  }

  ///
  /// 刪除分類
  ///
  Future<bool> deleteCategory(num id) async {
    final ret = await apiProvider.delete<bool>(
      'categories/$id',
      creator: (json) {
        if (json.containsKey(Keys.IsDeleted)) {
          return json[Keys.IsDeleted];
        }
        return false;
      },
    );
    // update cache
    if (true == ret) {
      // remove from mem
      final box = boxProvider.getGsBox(kBoxCategory);
      box.remove('$id');
    }
    return ret;
  }

  Future<bool> sortCategories(Iterable<IDSort> it) async {
    final ret = await apiProvider.post<bool>(
      'categories/sort',
      data: {
        'data': jsonEncode(it.toList()),
      },
      creator: (json) {
        if (json.containsKey(Keys.IsSorted.value)) {
          return json[Keys.IsSorted.value];
        }
        return false;
      },
    );
    if (true == ret) {
      final box = boxProvider.getGsBox(kBoxCategory);
      for (var item in it) {
        final cate = Category.fromJson(box.read('${item.id}'));
        cate.sort = item.sort;
        box.write('${item.id}', cate.toJson());
      }
    }
    return ret;
  }

  bool get isEmpty {
    final box = boxProvider.getGsBox(kBoxProductInfo);
    return Iterable.castFrom(box.getKeys()).isEmpty;
  }

  bool isEmptyWithProductKind(ProductKind value) {
    return getCategoriesFromStorage(value).isEmpty;
  }

  ProductInfo getProduct(num productId) {
    final box = boxProvider.getGsBox(kBoxProductInfo);
    for (var item in box.getValues()) {
      final product = ProductInfo.fromJson(item);
      if (product.productId == productId) {
        return product;
      }
    }
    return ProductInfo();
  }

  Future<num> sold(num productId) async {
    final ret = await apiProvider.getResData<num>(
      unencodedPath: 'products/$productId/sold',
      creator: (json) {
        if (json[Keys.IsUpdated] == true) {
          return json['product_id'] ?? 0;
        }
        return 0;
      },
    );
    // update cache
    if (ret is num && ret > 0) {
      final box = boxProvider.getGsBox(kBoxProductInfo);
      for (var item in box.getValues()) {
        final product = ProductInfo.fromJson(item);
        if (product.productId == productId) {
          product.isSoldOut = false;
          box.write('${product.uniqueId}', product.toJson());
        }
      }
    }
    return ret;
  }

  Future<num> soldOut(num productId) async {
    final ret = await apiProvider.getResData<num>(
      unencodedPath: 'products/$productId/sold_out',
      creator: (json) {
        if (json[Keys.IsUpdated] == true) {
          return json['product_id'] ?? 0;
        }
        return 0;
      },
    );
    // update cache
    if (ret is num && ret > 0) {
      final box = boxProvider.getGsBox(kBoxProductInfo);
      for (var item in box.getValues()) {
        final product = ProductInfo.fromJson(item);
        if (product.productId == productId) {
          product.isSoldOut = true;
          box.write('${product.uniqueId}', product.toJson());
        }
      }
    }
    return ret;
  }

  ///
  /// 產品排序
  ///
  Future<bool> sort(ProductCategoriesPut req) async {
    final res = await apiProvider.putProductCategories(req);
    if (res == true) {
      final box = boxProvider.getGsBox(kBoxProductInfo);
      if (box.hasData('${req.id}')) {
        final product = ProductInfo.fromJson(box.read('${req.id}'));
        product.sort = req.sort;
        box.write('${req.id}', product.toJson());
      }
    }
    return res;
  }

  ///
  /// 分類附加品列表
  ///
  Future<Iterable<Category>> getAdditionCategories(ProductKind kind) async {
    final value = await apiProvider.getList<Category>(
      unencodedPath: 'addition-categories',
      filter: {'kind': '${kind.index}'},
      creator: (value) => Category.fromJson(value),
    );
    final box = boxProvider.getGsBox(kBoxAdditionCategory);
    // remove current kind
    final items = Iterable.castFrom(box.getValues())
        .map((e) => Category.fromJson(e))
        .where((e) => e.kind == kind.index)
        .toList(growable: false);
    for (var item in items) {
      box.remove('${item.id}');
    }
    // insert mem
    for (var item in value) {
      box.write('${item.id}', item.toJson());
    }
    return value;
  }

  ///
  /// 新增附加品分類
  ///
  Future<num> postAdditionCategories(Category data) async {
    final ret = await apiProvider.post<num>(
      'addition-categories',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsCreated) && true == json[Keys.IsCreated]) {
          if (json.containsKey('addition_category_id')) {
            return json['addition_category_id'];
          }
        }
        return 0;
      },
    );
    // update cache
    if (ret != null && ret > 0) {
      data.id = ret;
      final box = boxProvider.getGsBox(kBoxAdditionCategory);
      box.write('${data.id}', data.toJson());
    }
    return ret;
  }

  ///
  /// 更新附加品分類
  ///
  Future<num> putAdditionCategories(Category data) async {
    final ret = await apiProvider.put(
      'addition-categories/${data.id}',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          if (json.containsKey('addition_category_id')) {
            return json['addition_category_id'];
          }
        }
        return 0;
      },
    );
    // update cache
    if (ret != null && ret > 0) {
      final box = boxProvider.getGsBox(kBoxAdditionCategory);
      box.write('${data.id}', data.toJson());
    }
    return ret;
  }

  ///
  /// 刪除附加品分類
  ///
  Future<bool> deleteAdditionCategory(num id) async {
    final ret = await apiProvider.delete<bool>(
      'addition-categories/$id',
      creator: (json) {
        if (json.containsKey(Keys.IsDeleted)) {
          return json[Keys.IsDeleted];
        }
        return false;
      },
    );
    // update cache
    if (true == ret) {
      final box = boxProvider.getGsBox(kBoxAdditionCategory);
      box.remove('$id');
    }
    return ret;
  }

  ///
  /// 排序附加品分類
  ///
  Future<bool> sortAdditionCategories(Iterable<IDSort> it) async {
    final ret = await apiProvider.post<bool>(
      'addition-categories/sort',
      data: {
        'data': jsonEncode(it.toList()),
      },
      creator: (json) {
        if (json.containsKey(Keys.IsSorted.value)) {
          return json[Keys.IsSorted.value];
        }
        return false;
      },
    );
    // update cache
    if (true == ret) {
      final box = boxProvider.getGsBox(kBoxAdditionCategory);
      for (var item in it) {
        final cate = Category.fromJson(box.read('${item.id}'));
        cate.sort = item.sort;
        box.write('${item.id}', cate.toJson());
      }
    }
    return ret;
  }

  ///
  /// 用於登入後，取得所有規格
  ///
  Future<void> fetchAllAdditionProducts() async {
    final box = boxProvider.getGsBox(kBoxAdditionProduct);
    box.erase();
    for (var kind in ProductKind.values) {
      if (ProductKind.Max != kind) {
        try {
          await getAdditionProducts(kind);
          await 100.milliseconds.delay();
        } catch (e) {
          logger.e(e);
        }
      }
    }
  }

  Iterable<AdditionProduct> getAdditionProductsFromLocalStorage(
    ProductKind kind, {
    num additionCategoryId, // 有機會是 null，代表回傳所有的 kind
  }) sync* {
    final box = boxProvider.getGsBox(kBoxAdditionProduct);
    for (var element in box.getValues()) {
      final item = AdditionProduct.fromJson(element);
      if (kind != null) {
        if (kind.index != item.kind) {
          continue;
        }
      }
      if (additionCategoryId is num) {
        if (additionCategoryId != item.additionCategoryId) {
          continue;
        }
      }
      yield item;
    }
  }

  ///
  /// 附加品列表
  ///
  Future<Iterable<AdditionProduct>> getAdditionProducts(
    ProductKind kind, {
    num additionCategoryId,
    bool cacheFirst,
  }) async {
    final box = boxProvider.getGsBox(kBoxAdditionProduct);
    if (true == cacheFirst) {
      final it = <AdditionProduct>[];
      for (var element in box.getValues()) {
        final item = AdditionProduct.fromJson(element);
        if (kind != null) {
          if (kind.index != item.kind) {
            continue;
          }
        }
        if (additionCategoryId is num) {
          if (additionCategoryId != item.additionCategoryId) {
            continue;
          }
        }
        // yield item;
        it.add(item);
      }
      if (it.isNotEmpty) {
        return it;
      }
    }
    final value = await apiProvider.getList<AdditionProduct>(
      unencodedPath: 'addition-products',
      filter: <String, dynamic>{
        'kind': '${kind.index}',
        'addition_category_id': additionCategoryId,
      },
      creator: (value) => AdditionProduct.fromJson(value),
    );
    // remove old
    final items = Iterable.castFrom(box.getValues())
        .map((e) => AdditionProduct.fromJson(e))
        .where((element) => element.additionCategoryId == additionCategoryId)
        .toList(growable: false);
    for (final item in items) {
      box.remove('${item.id}');
    }
    // write to mem
    for (final item in value) {
      box.write('${item.id}', item.toJson());
    }
    return value;
  }

  ///
  /// 新增附加品
  ///
  Future<num> postAdditionProduct(AdditionProduct data) async {
    // 特殊: 使用整數
    data.price = data.price.round();
    final ret = await apiProvider.post<num>(
      'addition-products',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsCreated) && true == json[Keys.IsCreated]) {
          if (json.containsKey('addition_product_id')) {
            return json['addition_product_id'];
          }
        }
        return 0;
      },
    );
    // update cache
    if (ret is num && ret > 0) {
      data.id = ret;
      final box = boxProvider.getGsBox(kBoxAdditionProduct);
      box.write('${data.id}', data.toJson());
    }
    return ret;
  }

  ///
  /// 更新附加品
  ///
  Future<num> putAdditionProduct(AdditionProduct data) async {
    // 特殊: 使用整數
    data.price = data.price.round();
    final ret = await apiProvider.put(
      'addition-products/${data.id}',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && true == json[Keys.IsUpdated]) {
          if (json.containsKey('addition_product_id')) {
            return json['addition_product_id'];
          }
        }
        return 0;
      },
    );
    // update cache
    if (ret != null && ret > 0) {
      final box = boxProvider.getGsBox(kBoxAdditionProduct);
      box.write('${data.id}', data.toJson());
    }
    return ret;
  }

  ///
  /// 刪除附加品
  ///
  Future<bool> deleteAdditionProduct(num id) async {
    final ret = await apiProvider.delete<bool>(
      'addition-products/$id',
      creator: (json) {
        if (json.containsKey(Keys.IsDeleted)) {
          return json[Keys.IsDeleted];
        }
        return false;
      },
    );
    // update cache
    if (true == ret) {
      // remove from mem
      final box = boxProvider.getGsBox(kBoxAdditionProduct);
      box.remove('$id');
    }
    return ret;
  }

  ///
  /// 排序附加品
  ///
  Future<bool> sortAdditionProducts(Iterable<IDSort> it) async {
    final ret = await apiProvider.post<bool>(
      'addition-products/sort',
      data: {
        'data': jsonEncode(it.toList()),
      },
      creator: (json) {
        if (json.containsKey(Keys.IsSorted.value)) {
          return json[Keys.IsSorted.value];
        }
        return false;
      },
    );
    // update cache
    if (true == ret) {
      final box = boxProvider.getGsBox(kBoxAdditionProduct);
      for (var item in it) {
        final cate = AdditionProduct.fromJson(box.read('${item.id}'));
        cate.sort = item.sort;
        box.write('${item.id}', cate.toJson());
      }
    }
    return ret;
  }

  ///
  /// 更新產品
  ///
  Future<num> putProduct(num id, ProductsPostReq data) async {
    final ret = await apiProvider.put(
      'products/$id',
      data: data.toJson(),
      creator: (json) {
        if (true == json[Keys.IsUpdated]) {
          return json['product_id'] ?? 0;
        }
        return 0;
      },
    );
    // update cache
    if (ret is num && ret > 0) {
      await getProductDetail(ret);
      await fetchAllProducts();
    }
    return ret;
  }

  ///
  /// 新增慘品
  ///
  Future<num> postProduct(ProductsPostReq data) async {
    final ret = await apiProvider.post<num>(
      'products',
      data: data.toJson(),
      creator: (json) {
        if (true == json[Keys.IsCreated]) {
          return json['product_id'] ?? 0;
        }
        return 0;
      },
    );
    // update cache
    if (ret is num && ret > 0) {
      await getProductDetail(ret);
      await fetchAllProducts();
    }
    return ret;
  }
}

extension _ExtensionProductCategoriesPut on ProductCategoriesPut {
  // product id 可能重複，使用 product id + category id 取 hash
  num get id => '$productId.$categoryId'.hashCode;
}
