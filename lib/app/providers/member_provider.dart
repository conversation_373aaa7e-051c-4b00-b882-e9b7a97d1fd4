import 'package:flutter/foundation.dart' show required;
import 'package:get/get.dart' hide Condition;
import 'package:muyipork/app/data/models/other/brands_banners.dart';
import 'package:muyipork/app/data/models/other/brands_banners_put.dart';
import 'package:muyipork/app/data/models/other/member.dart';
import 'package:muyipork/app/data/models/other/member_page.dart';
import 'package:muyipork/app/data/models/res/member_req.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/enums.dart';

import 'api_provider.dart';

class MemberProvider {
  final ApiProvider apiProvider;
  BoxProvider get boxProvider => apiProvider.prefProvider.boxProvider;
  final profiles = <num, MemberProfile>{}.obs;
  final _vipCount = 0.obs;
  final _total = 0.obs;

  num get vipCount => _vipCount.value;
  num get total => _total.value;

  MemberProvider({
    @required this.apiProvider,
  });

  Future<Map> pushMessage(num id, String message) {
    return apiProvider.putMemberMessage(id, {
      'message': message,
    });
  }

  Box<Member> get memberBox => boxProvider.store.box<Member>();

  // Stream<Member> getMemberStream(MemberReq filter) {
  //   return getMemberQuery(filter).stream();
  // }

  Stream<Member> getMemberStream(MemberReq filter) async* {
    final box = boxProvider.store.box<Member>();
    final condition = filter.toQueryCondition();
    final builder = box.query(condition);
    builder.order(Member_.id, flags: Order.descending);
    final query = builder.build();
    query.limit = filter.nnLimit;
    query.offset = filter.offset;
    yield* query.stream();
    query.close();
  }

  // Stream<Query<Member>> getMemberQueryStream(MemberReq filter) {
  //   final box = boxProvider.store.box<Member>();
  //   Condition<Member> condition;
  //   if (filter?.keyword != null && filter.keyword.isNotEmpty) {
  //     condition = Member_.mobilePhone
  //         .contains(filter.keyword)
  //         .or(Member_.name.contains(filter.keyword));
  //   }
  //   final builder = box.query(condition);
  //   builder.order(Member_.id, flags: Order.descending);
  //   // final query = builder.build();
  //   // query.limit = filter.nnLimit;
  //   // query.offset = filter.offset;
  //   return builder.watch();
  // }

  ///
  /// 取得會員列表
  ///
  Future<Iterable<Member>> getMemberList(final MemberReq req) async {
    req.page ??= 1;
    req.limit ??= kLimit;
    // HACK: test limit
    // req.limit = 5;
    final value = await apiProvider.getResData<MemberPage>(
      unencodedPath: 'members',
      filter: req?.toJson(),
      creator: (json) {
        final ret = MemberPage.fromJson(json);
        ret.data ??= <Member>[];
        ret.data = [...ret.data];
        return ret;
      },
    );
    // update total
    _total.value = value?.pagination?.total ?? 0;
    // update vip count
    _vipCount.value = value?.vipCount ?? 0;
    // update cache
    final members = List<Member>.from(value?.data ?? <Member>[]);
    final timestamp = DateTime.now().toUtc().millisecondsSinceEpoch;
    for (var member in members) {
      member.timestamp = timestamp;
      member.rawJson = member.toRawJson();
    }
    members.forEach((member) => member.rawJson = member.toRawJson());
    // insert db
    final box = boxProvider.store.box<Member>();
    try {
      box.putMany(members);
    } catch (e) {
      logger.e(e);
      _writeToMem(members);
    }
    // for (var member in members) {
    //   box.putQueued(member);
    // }
    return members;
  }

  void _writeToMem(Iterable<Member> members) {
    final box = boxProvider.getGsBox(kBoxMember);
    for (var member in members) {
      box.write('${member.id}', member.toJson());
    }
  }

  bool contains(num id) {
    if (id is num && id > 0) {
      final box = boxProvider.store.box<Member>();
      return box.contains(id);
    }
    return false;
  }

  ///
  /// 取得單一會員資料
  ///
  Future<Member> getMember(num id, {bool cacheFirst}) async {
    final isValidId = id is num && id > 0;
    if (!isValidId) {
      throw '會員 id ($id) 不存在';
    }
    // insert db
    final box = boxProvider.store.box<Member>();
    if (true == cacheFirst && contains(id)) {
      return box.get(id);
    }
    final member = await apiProvider.getData(
      unencodedPath: 'members/$id',
      creator: (json) => Member.fromJson(json),
    );
    if (contains(member.id)) {
      // 特殊: 儲存 status
      member.status = box.get(id).status;
    }
    member.rawJson = member.toRawJson();
    // update db
    try {
      box.put(member);
    } catch (e) {
      logger.e(e);
      _writeToMem([member]);
    }
    // await box.putAsync(member);
    // box.putQueued(member);
    return member;
  }

  ///
  /// 取得單一會員基本資料
  ///
  Future<MemberProfile> getMemberProfile(num id) async {
    final mp = await apiProvider.getData(
      unencodedPath: 'members/$id/profile',
      creator: (json) => MemberProfile.fromJson(json),
    );
    profiles[mp.id] = mp;
    return mp;
  }

  ///
  /// 取得會員列表
  ///
  List<Member> getMembersFromLocalStorage([List<num> ids]) {
    final box = boxProvider.store.box<Member>();
    if (ids is List && ids.isNotEmpty) {
      final box = boxProvider.store.box<Member>();
      return box.getMany(ids);
    }
    return box.getAll();
  }

  Member getMemberFromLocalStorage(num id) {
    if (contains(id)) {
      final box = boxProvider.store.box<Member>();
      return box.get(id);
    }
    return null;
  }

  void saveMemberToLocalStorage(Member member) {
    final box = boxProvider.store.box<Member>();
    box.put(member);
  }

  ///
  /// 會員積點列表
  ///
  Future<Iterable<MemberPoint>> getMemberPoints(num id, num page) async {
    logger.d('[MemberProvider] getMemberPoints: id($id), page($page)');
    // HACK: test limit
    // limit = 5;
    const limit = kLimit;
    final res = await apiProvider.getList<MemberPoint>(
      unencodedPath: 'member-points/$id',
      filter: <String, dynamic>{
        'page': page,
        'limit': limit,
      },
      creator: (json) => MemberPoint.fromJson(json),
    );
    for (var memberPoint in res) {
      // update member
      final memberId = memberPoint.memberId ?? 0;
      // logger.d('[MemberProvider] memberId: $memberId');
      if (memberId > 0) {
        // logger.d('[MemberProvider] contains memberId: $memberId');
        final _box = boxProvider.store.box<Member>();
        if (_box.contains(memberId)) {
          final member = _box.get(memberId);
          // logger.d('[MemberProvider] member: $member');
          memberPoint.customer.target = member;
        }
      }
      final orderId = memberPoint.orderId ?? 0;
      if (orderId > 0) {
        // logger.d('[MemberProvider] contains orderId: $orderId');
        final _box = boxProvider.store.box<OrderSummary>();
        if (_box.contains(orderId)) {
          final order = _box.get(orderId);
          // logger.d('[MemberProvider] order: $order');
          memberPoint.order.target = order;
        }
      }
    }
    // insert db
    final box = boxProvider.store.box<MemberPoint>();
    box.putMany(res);
    return res;
  }

  QueryBuilder<MemberPoint> _createQueryBuilder([MemberPointQry qry]) {
    final box = boxProvider.store.box<MemberPoint>();
    final builder = box.query(qry?.toQueryCondition());
    final memberQueryCondition = qry?.toMemberQueryCondition();
    if (memberQueryCondition != null) {
      builder.link(MemberPoint_.customer, memberQueryCondition);
    }
    final orderQueryCondition = qry?.toOrderQueryCondition();
    if (orderQueryCondition != null) {
      builder.link(MemberPoint_.order, orderQueryCondition);
    }
    builder.order(MemberPoint_.id, flags: Order.descending);
    return builder;
  }

  Future<Iterable<MemberPoint>> getMemberPoint(MemberPointQry req) async {
    // offline query
    final builder = _createQueryBuilder(req);
    final query = builder.build();
    query.limit = kLimit;
    query.offset = 0;
    final list = await query.stream().toList();
    if (list.isNotEmpty) {
      return list;
    }
    // online query
    final it = await getMemberPoints(req.memberId, 1);
    return it.where(
      (element) {
        if (req.memberId != null && req.memberId != element.memberId) {
          return false;
        }
        if (req.orderId != null && req.orderId != element.orderId) {
          return false;
        }
        if (req.type != null && req.type != element.type) {
          return false;
        }
        return true;
      },
    );
  }

  ///
  /// 會員總積點
  ///
  Future<num> getMemberPointTotal(num id) {
    return apiProvider.getResData<num>(
      unencodedPath: 'member-points/$id/total',
      creator: (json) => json[Keys.Points],
    );
  }

  ///
  /// 取得單一會員圖片列表
  ///
  Future<Iterable<BrandsBanners>> getMemberImages(num id) {
    return apiProvider.getList(
      unencodedPath: 'members/$id/images',
      creator: (json) => BrandsBanners.fromJson(json),
    );
  }

  ///
  /// 設定單一會員店家備註
  ///
  Future<bool> putMemberMemo(num id, String memo) {
    return apiProvider.put<bool>(
      'members/$id/memo',
      data: {
        'memo': memo,
      },
      creator: (json) => json[Keys.IsUpdated],
    );
  }

  ///
  /// 更新單一會員圖片集
  ///
  Future<bool> putMemberImages(num id, BrandsBannersPut data) async {
    return apiProvider.put<bool>(
      'members/$id/images',
      data: data.toJson(),
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated)) {
          return json[Keys.IsUpdated];
        }
        return null;
      },
    );
  }

  ///
  /// 將單一會員設定為 vip
  ///
  Future<num> getMemberVip(num id) async {
    logger.d('[MemberProvider] getMemberVip: id($id)');
    final res = await apiProvider.getResData<num>(
      unencodedPath: 'members/$id/vip',
      creator: (json) => json[Keys.MemberId],
    );
    if (res is num && res > 0) {
      getMember(id);
    }
    return res;
  }

  ///
  /// 解除單一會員 vip 資格 (O)
  ///
  Future<num> getMemberUnvip(num id) async {
    logger.d('[MemberProvider] getMemberUnvip: id($id)');
    final res = await apiProvider.getResData<num>(
      unencodedPath: 'members/$id/unvip',
      creator: (json) => json[Keys.MemberId],
    );
    if (res is num && res > 0) {
      getMember(id);
    }
    return res;
  }

  void addVipCount(num value) {
    _vipCount.value = (vipCount + value).clamp(0, total);
  }

  ///
  /// 封鎖單一會員資料
  ///
  Future<num> getMemberBlock(num id) async {
    logger.d('[MemberProvider] getMemberBlock: id($id)');
    final res = await apiProvider.getResData<num>(
      unencodedPath: 'members/$id/block',
      creator: (json) => json[Keys.MemberId],
    );
    if (res is num && res > 0) {
      getMember(id, cacheFirst: true).then((value) {
        if (value != null) {
          value.status = Switcher.Off.index;
          saveMemberToLocalStorage(value);
        }
      });
    }
    return res;
  }

  ///
  /// 解除封鎖單一會員資料
  ///
  Future<num> getMemberUnblock(num id) async {
    logger.d('[MemberProvider] getMemberUnblock: id($id)');
    final res = await apiProvider.getResData<num>(
      unencodedPath: 'members/$id/unblock',
      creator: (json) => json[Keys.MemberId],
    );
    if (res is num && res > 0) {
      getMember(id, cacheFirst: true).then((value) {
        if (value != null) {
          value.status = Switcher.On.index;
          saveMemberToLocalStorage(value);
        }
      });
    }
    return res;
  }

  ///
  /// 設定單一會員自定義名稱
  ///
  Future<num> putMemberNicknameStore(num id, String nickname) {
    return apiProvider.put(
      'members/$id/nickname_store',
      data: {
        'nickname_store': nickname,
      },
      creator: (json) {
        if (json.containsKey(Keys.IsUpdated) && json[Keys.IsUpdated]) {
          if (json.containsKey(Keys.MemberId)) {
            return json[Keys.MemberId];
          }
        }
        return null;
      },
    );
  }
}

extension _MemberPointQryX on MemberPointQry {
  Condition<Member> toMemberQueryCondition() {
    Iterable<Condition<Member>> _orAny() sync* {
      if (memberId is num && memberId > 0) {
        yield Member_.id.equals(memberId);
      }
    }

    final it = _orAny();
    if (it.isNotEmpty) {
      return it.reduce((value, element) => value.or(element));
    }
    return null;
  }

  Condition<OrderSummary> toOrderQueryCondition() {
    Iterable<Condition<OrderSummary>> _orAny() sync* {
      if (orderId is num && orderId > 0) {
        yield OrderSummary_.id.equals(orderId);
      }
    }

    final it = _orAny();
    if (it != null && it.isNotEmpty) {
      return it.reduce((value, element) => value.or(element));
    }
    return null;
  }

  Condition<MemberPoint> toQueryCondition() {
    Iterable<Condition<MemberPoint>> _andAll() sync* {
      if (type != null) {
        yield MemberPoint_.type.equals(type);
      }
      if (comment != null && comment.isNotEmpty) {
        yield MemberPoint_.comment.contains(comment);
      }
    }

    final it = _andAll();
    if (it != null && it.isNotEmpty) {
      return it.reduce((value, element) => value.and(element));
    }
    return null;
  }
}
