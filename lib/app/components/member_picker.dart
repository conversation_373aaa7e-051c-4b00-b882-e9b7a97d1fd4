import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/data/models/other/member.dart';
import 'package:muyipork/colors.dart';

import 'member_avatar.dart';
import 'radio_button.dart';

class MemberPicker extends StatelessWidget {
  final Iterable<Member> data;
  final RxNum selected;

  const MemberPicker({
    Key key,
    @required this.data,
    @required this.selected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => _main());
  }

  Widget _main() {
    final ls = <Widget>[];
    ls.addIf(
      true,
      RadioButton<num>(
        value: 0,
        groupValue: selected.value,
        onChanged: (value) => selected.value = value,
        child: Row(
          children: [
            SizedBox(width: 34),
            Text(
              '不使用會員身份結帳',
              style: TextStyle(
                fontSize: 20,
                color: OKColor.Primary,
              ),
              textAlign: TextAlign.left,
            ),
          ],
        ),
      ),
    );
    final it = data.map((e) {
      return RadioButton<num>(
        value: e.id,
        groupValue: selected.value,
        onChanged: (value) => selected.value = value,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(width: 34),
            MemberAvatar(
              imageUrl: e.avatar,
              size: 36.0,
            ),
            SizedBox(width: 12),
            Text(
              e.name ?? '',
              style: TextStyle(
                fontSize: 20,
                color: Colors.black,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.left,
            ),
          ],
        ),
      );
    });
    ls.addAll(it);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: ls,
    );
  }
}
