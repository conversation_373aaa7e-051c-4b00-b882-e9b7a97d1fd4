import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:muyipork/app/data/models/res/orders_get_res.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:get/get.dart';

import 'label_value.dart';
import 'line_tag.dart';
import 'member_avatar.dart';
import 'stadium_button.dart';

class OrderListItem extends StatelessWidget {
  final OrderSummary data;
  final VoidCallback onDetailPressed;
  final bool showCheckBox;
  final bool showActionButtons;
  final bool checked;
  final ValueChanged<bool> onCheckChanged;
  final VoidCallback onAcceptPressed;
  final VoidCallback onRejectPressed;
  final VoidCallback onCheckoutPressed;

  const OrderListItem({
    Key key,
    this.data,
    this.checked,
    this.onDetailPressed,
    this.showCheckBox,
    this.onCheckChanged,
    this.onAcceptPressed,
    this.onRejectPressed,
    this.onCheckoutPressed,
    this.showActionButtons,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(height: 4.0);
    yield _header().paddingSymmetric(horizontal: Constants.paddingHorizontal);
    yield _member().paddingSymmetric(horizontal: Constants.paddingHorizontal);
    yield _orderNumber()
        .paddingSymmetric(horizontal: Constants.paddingHorizontal);
    yield const Divider(
      height: 12,
      indent: kPadding,
      endIndent: kPadding,
    );
    yield _info().paddingSymmetric(horizontal: Constants.paddingHorizontal);
    yield const SizedBox(height: 8.0);
    // 已完成列表不需顯示付款狀態
    if (ORDERS_STATUS_ACTIVE.contains(data.status.orderStatus)) {
      yield _paymentStatus();
    }
    yield _bottom();
  }

  Widget _point() {
    return Container(
      width: 36,
      height: 36,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        color: OKColor.Primary,
      ),
      alignment: Alignment.center,
      child: const DecoratedBox(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
        ),
        child: Padding(
          padding: EdgeInsets.all(3.0),
          child: Text(
            'P',
            style: TextStyle(
              fontSize: 16,
              color: OKColor.Primary,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _coupon() {
    return Container(
      width: 36,
      height: 36,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        color: const Color(0xff0eacc1),
      ),
      alignment: Alignment.center,
      child: SvgPicture.asset(
        'assets/images/icon_ticket.svg',
        color: Colors.white,
        width: 16,
        height: 12,
      ),
    );
  }

  Widget _avatar() {
    return ListTile(
      horizontalTitleGap: 8,
      contentPadding: EdgeInsets.zero,
      leading: MemberAvatar(imageUrl: data.member?.avatar),
      title: Text(
        // '釋金城',
        data.member?.name ?? '現場消費者',
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.left,
        overflow: TextOverflow.ellipsis,
        maxLines: 2,
      ),
    );
  }

  // TODO: 顯示付款方式 (需修改 server 取得欄位)
  Widget _payMethod() {
    return SizedBox();
  }

  ///
  /// 顯示付款方式&付款狀態
  ///
  Widget _paymentStatus() {
    Iterable<Widget> children() sync* {
      yield const Spacer();
      yield Text(
        // '未付款',
        data.paymentStatus.paymentStatus.name ?? '',
        style: const TextStyle(
          fontSize: 16,
          color: Colors.white,
        ),
        textAlign: TextAlign.right,
      );
    }

    return Container(
      width: double.infinity,
      height: Constants.buttonHeight,
      padding: const EdgeInsets.symmetric(
        horizontal: Constants.paddingHorizontal,
      ),
      // color: const Color(0xFFFA821B),
      color: data.paymentStatus.paymentStatus.backgroundColor,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _member() {
    Iterable<Widget> children() sync* {
      var needSeparator = false;
      if (data.usingCoupon == true) {
        yield _coupon();
        needSeparator = true;
      }
      if (data.usingPoints == true) {
        if (needSeparator == true) {
          yield const SizedBox(width: 4.0);
        }
        yield _point();
        needSeparator = true;
      }
      if (needSeparator == true) {
        yield const SizedBox(width: 4.0);
      }
      yield Expanded(child: _avatar());
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Widget _tags() {
    Iterable<Widget> children() sync* {
      if (data.orderFromLine == true) {
        yield LineTag(
          text: 'Line下單',
          textColor: kColorLine,
        );
      }
      if (data.isPrint == 1) {
        yield LineTag(
          text: '已列印',
          textColor: Colors.blue,
        );
      }
      final checkbox = showCheckBox ?? false;
      if (checkbox == true) {
        yield SizedBox(
          child: Checkbox(
            value: checked ?? false,
            onChanged: onCheckChanged,
          ),
        );
      }
      final status = !checkbox;
      if (status == true) {
        yield LineTag(
          text: data.orderStatus.name,
          textColor: data.orderStatus.color,
        );
      }
    }

    return Wrap(
      alignment: WrapAlignment.end,
      children: children().toList(growable: false),
    );
  }

  Widget _header() {
    Iterable<Widget> children() sync* {
      yield StadiumButton.chip(
        backgroundColor: data.displayTypeColor ?? kColorError,
        onPressed: onDetailPressed,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(width: 4.0),
            Text(
              '${data.displayType}詳情',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            Icon(
              Icons.navigate_next,
              color: Colors.white,
            ),
          ],
        ),
      );
      yield Expanded(child: _tags());
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Widget _orderNumber() {
    Iterable<Widget> children() sync* {
      yield Expanded(
        child: LabelValue(
          labelText: '訂單編號：',
          valueText: data.orderNumber,
        ),
      );
      yield Text(
        data.displayCreatedAt ?? '',
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black,
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Widget _info() {
    Iterable<Widget> children() sync* {
      yield Expanded(
        child: LabelValue(
          labelText: this.data.orderType.mealName ?? '',
          valueText: this.data.displayMealAt ?? '',
          valueColor: this.data.displayMealAtColor,
        ),
      );
      if (data.displayPeople.isNotEmpty) {
        yield VerticalDivider(width: Constants.paddingHorizontal);
      }
      if (data.displayPeople.isNotEmpty && data.type.orderType.isDinnerHere) {
        yield Text(
          // '2大3小',
          data.displayPeople ?? '',
          style: const TextStyle(
            fontSize: 16,
            color: kColorPrimary,
          ),
          textAlign: TextAlign.right,
        );
      }
      if (data.displayTable.isNotEmpty) {
        yield VerticalDivider(width: Constants.paddingHorizontal);
      }
      if (data.displayTable.isNotEmpty) {
        yield Text(
          // 'A區3桌',
          data.displayTable ?? '',
          style: const TextStyle(
            fontSize: 16,
            color: kColorPrimary,
          ),
          textAlign: TextAlign.right,
        );
      }
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Widget _summary() {
    Iterable<InlineSpan> _children() sync* {
      yield TextSpan(
        text: (data.itemCount ?? 0).decimalStyle,
        style: TextStyle(fontWeight: FontWeight.w500),
      );
      yield TextSpan(
        text: ' 項，',
        style: TextStyle(
          fontSize: 17,
          color: Colors.black,
          fontWeight: FontWeight.w500,
        ),
      );
      yield TextSpan(
        // text: '\$820 ',
        text: data.total.currencyStyle,
        style: TextStyle(fontWeight: FontWeight.w500),
      );
      yield TextSpan(
        text: ' 元',
        style: TextStyle(
          fontSize: 17,
          color: OKColor.Gray22,
          fontWeight: FontWeight.w500,
        ),
      );
    }

    return Text.rich(
      TextSpan(
        style: const TextStyle(
          fontSize: 28,
          color: kColorPrimary,
        ),
        children: _children().toList(growable: false),
      ),
      textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
      textAlign: TextAlign.left,
    );
  }

  Widget _bottom() {
    Iterable<Widget> children() sync* {
      yield Expanded(child: _summary());
      if (showActionButtons == true ||
          ORDERS_STATUS_ACTIVE.contains(data.status.orderStatus)) {
        yield _ButtonBar(
          // 付款狀態
          paymentStatus: data.paymentStatus.paymentStatus,
          // 訂單狀態
          orderStatus: data.status.orderStatus,
          onAcceptPressed: onAcceptPressed,
          onDeniedPressed: onRejectPressed,
          onCheckoutPressed: onCheckoutPressed,
        );
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: Constants.paddingHorizontal,
        vertical: 12.0,
      ),
      decoration: BoxDecoration(
        color: Color(0xffe2e2e2),
      ),
      alignment: Alignment.centerLeft,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  static Widget divider() {
    return Container(
      height: 22,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment(0.0, -1.0),
          end: Alignment(0.0, 1.0),
          colors: [Color(0xff868686), Color(0xffa0a0a0), Color(0xffbcbcbc)],
          stops: [0.0, 0.187, 1.0],
        ),
      ),
    );
  }
}

class _ButtonBar extends StatelessWidget {
  // 付款狀態
  final PaymentStatus paymentStatus;
  // 訂單狀態
  final OrderStatus orderStatus;
  final VoidCallback onAcceptPressed;
  final VoidCallback onDeniedPressed;
  final VoidCallback onCheckoutPressed;

  const _ButtonBar({
    Key key,
    this.paymentStatus,
    this.orderStatus,
    this.onAcceptPressed,
    this.onDeniedPressed,
    this.onCheckoutPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() {
    // 已付款狀態顯示歸檔、棄單
    if ([PaymentStatus.Paid].contains(paymentStatus)) {
      return _paidButtons();
    }
    // 付款失敗只顯示歸檔按鈕
    if ([PaymentStatus.Failed, PaymentStatus.Timeout].contains(paymentStatus)) {
      return _payFailedButtons();
    }
    switch (orderStatus) {
      case OrderStatus.Padding:
        return _acceptAndReject();
      case OrderStatus.Accepted:
        return _checkout();
      default:
        return <Widget>[];
    }
  }

  // 只顯示歸檔按鈕
  Iterable<Widget> _payFailedButtons() sync* {
    yield ValueBuilder<Value<bool>>(
      initialValue: true.reactive,
      builder: (value, updateFn) {
        return StadiumButton(
          buttonText: '歸檔',
          backgroundColor: const Color(0xff3a6cc9),
          onPressed: onAcceptPressed,
        );
      },
    );
  }

  // 顯示歸檔、退點兩個按鈕
  Iterable<Widget> _paidButtons() sync* {
    yield StadiumButton(
      buttonText: '退點',
      backgroundColor: const Color(0xff3e4b5a),
      onPressed: onDeniedPressed,
    );
    yield const SizedBox(width: 8.0);
    yield ValueBuilder<Value<bool>>(
      initialValue: true.reactive,
      builder: (value, updateFn) {
        return StadiumButton(
          buttonText: '歸檔',
          backgroundColor: const Color(0xff3a6cc9),
          onPressed: onAcceptPressed,
        );
      },
    );
  }

  // 未付款單單，顯示棄單及接單
  Iterable<Widget> _acceptAndReject() sync* {
    yield StadiumButton(
      buttonText: '棄單',
      backgroundColor: kColorTabBar,
      onPressed: onDeniedPressed,
    );
    yield const SizedBox(width: 8.0);
    yield ValueBuilder<Value<bool>>(
      initialValue: true.reactive,
      builder: (value, updateFn) {
        return StadiumButton(
          child: value.obx(
            (state) => const Text(
              '接單',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
              textAlign: TextAlign.left,
            ),
            onLoading: SizedBox.fromSize(
              size: const Size.square(22.0),
              child: const CircularProgressIndicator(
                backgroundColor: Colors.white,
                strokeWidth: 2.0,
              ),
            ),
          ),
          backgroundColor: kColorPrimary,
          onPressed: onAcceptPressed,
        );
      },
      // if you need to call something outside the builder method.
      onUpdate: (value) => print("Value updated: $value"),
      onDispose: () => print("Widget unmounted"),
    );
  }

  Iterable<Widget> _checkout() sync* {
    yield StadiumButton(
      buttonText: '結帳',
      backgroundColor: kColorError,
      onPressed: onCheckoutPressed,
    );
  }
}
