import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/components/progressing.dart';
import 'package:muyipork/app/data/models/res/res_base.dart';

import 'dialog_general.dart';

///
/// 使用於按下儲存後顯示讀取中及成功或失敗
///
class FutureProgress<T> extends StatelessWidget {
  final Future<T> future;
  final WidgetBuilder onLoading;
  final NotifierBuilder<T> onData;
  final Widget Function(Object error) onError;

  const FutureProgress({
    Key key,
    @required this.future,
    this.onLoading,
    this.onData,
    this.onError,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<T>(
      future: future,
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          var message = snapshot.error.toString();
          if (snapshot.error is DioError) {
            final dioError = snapshot.error as DioError;
            final error = ResBase.resError(dioError.response);
            message = error.formattedErrorStr();
          }
          final errorWidget = onError?.call(snapshot.error);
          return errorWidget ?? DialogGeneral.alert(message);
        }
        if (snapshot.hasData) {
          final dataWidget = onData?.call(snapshot.data);
          if (dataWidget != null) {
            return dataWidget;
          }
          1.delay(() => Get.back<T>(result: snapshot.data));
          return Progressing.check();
        }
        // loading
        final loadingWidget = onLoading?.call(context);
        return loadingWidget ?? Progressing();
      },
    );
  }
}
