import 'package:flutter/material.dart';
import 'package:muyipork/app/data/models/other/member.dart';
import 'package:muyipork/colors.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/extension.dart';
import 'package:muyipork/enums.dart';

import 'member_avatar.dart';

class MemberItem extends StatelessWidget {
  final Member data;
  final Function onPressed;
  final Color backgroundColor;
  final bool nameOnly;

  const MemberItem({
    Key key,
    @required this.data,
    this.onPressed,
    this.backgroundColor,
    this.nameOnly,
  }) : super(key: key);

  MemberItem.nameOnly({
    Key key,
    @required this.data,
    this.onPressed,
    this.backgroundColor,
  })  : nameOnly = true,
        super(key: key);

  Widget _subtitle() {
    final showSubtitle = (nameOnly == null) || (nameOnly == false);
    if (showSubtitle == true &&
        data.mobilePhone != null &&
        data.mobilePhone.isNotEmpty) {
      return Text(
        // '09*****888',
        data.mobilePhone ?? '',
        style: const TextStyle(
          fontSize: 14,
          color: const Color(0xff666666),
        ),
        textAlign: TextAlign.left,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      );
    }
    return null;
  }

  Iterable<Widget> _leadingChildren() sync* {
    yield MemberAvatar(
      imageUrl: data.avatar,
    );
    yield Align(
      alignment: Alignment.bottomLeft,
      child: Visibility(
        visible: data?.isVip == Switcher.On.index,
        child: Vip(),
      ),
    );
  }

  Widget _leading() {
    return SizedBox(
      width: 44,
      height: 40,
      child: Stack(
        alignment: Alignment.centerRight,
        children: _leadingChildren().toList(growable: false),
      ),
    );
  }

  Widget _title() {
    return Text(
      // '釋金城',
      data.nicknameAndName ?? '',
      style: const TextStyle(
        fontSize: 16,
        color: Colors.black,
        fontWeight: FontWeight.w700,
      ),
      textAlign: TextAlign.left,
      overflow: TextOverflow.ellipsis,
      maxLines: 2,
    );
  }

  Iterable<Widget> _trailingChildren() sync* {
    if (data?.status?.switcher?.isOff ?? false) {
      yield Icon(
        Icons.block,
        color: OKColor.Error,
      );
    }
    if (onPressed != null) {
      yield Icon(
        Icons.navigate_next,
      );
    }
  }

  Widget _trailing() {
    final showTrailing = (nameOnly == null) || (nameOnly == false);
    if (showTrailing == true) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: _trailingChildren().toList(growable: false),
      );
    }
    return SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: onPressed,
      tileColor: backgroundColor ?? Colors.white,
      contentPadding: kContentPadding,
      leading: _leading(),
      title: _title(),
      subtitle: _subtitle(),
      trailing: _trailing(),
    );
  }
}

class Vip extends StatelessWidget {
  const Vip({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 2,
        horizontal: 4,
      ),
      decoration: ShapeDecoration(
        color: OKColor.Vip,
        shape: StadiumBorder(
          side: BorderSide(
            width: 1.0,
            color: Colors.white,
          ),
        ),
      ),
      child: Text(
        'VIP',
        style: TextStyle(
          fontSize: 10,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
