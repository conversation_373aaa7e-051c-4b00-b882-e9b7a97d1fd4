import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:muyipork/app/components/open_hour_edit_item.dart';
import 'package:muyipork/app/data/models/res/setting_get_res.dart';

//編輯一個 SettingWeekDay 用的 Widget, 特化型
class WeekDayEditingItem extends StatefulWidget {
  WeekDayEditingItem(this.settingWeekDay, {Key key, this.onChanged}) : super(key: key);

  final SettingWeekDay settingWeekDay;
  final Function onChanged;

  @override
  _WeekDayEditingItemState createState() => _WeekDayEditingItemState();
}

class _WeekDayEditingItemState extends State<WeekDayEditingItem> {

  @override
  Widget build(BuildContext context) {

    //The default editing content is an empty container.
    Widget editingContent = Container();
    if (widget.settingWeekDay.status == 1) {
      editingContent = Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Divider(),
          //編輯 openHours
          _editOpenHours(widget.settingWeekDay.hours),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Container(
          padding: EdgeInsets.all(12),
          color: Colors.white,
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.settingWeekDay.dayDisplayName(),
                      style: Get.textTheme.subtitle1,
                    ),
                  ),
                  Container(
                    height: 28,
                    child: Switch(
                        value: widget.settingWeekDay.status == 0 ? false : true,
                        onChanged: (b) {
                          setState(() {
                            widget.settingWeekDay.status = b ? 1 : 0;
                          });
                          widget.onChanged?.call();
                        }
                    ),
                  )
                ],
              ),
              editingContent
            ],
          ),
        ),
        Container(
          color: Colors.grey.shade100,
          height: 8,
        )
      ],
    );
  }

  Widget _editOpenHours(List<SettingOpenHour> hours) {
    List<Widget> columnChildren = [];

    //Edit existing hours
    // int removeIndex = -1;
    for(int i = 0 ; i < hours.length ; ++i) {
      columnChildren.add(OpenHourEditItem(OpenHourEditItemArgs(
        mainTime: hours[i].open,
        onMainTimeChanged: (mainTime) {
          hours[i].open = mainTime;
          widget.onChanged?.call();
        },
        secondaryTime: hours[i].close,
        onSecondaryTimeChanged: (secondaryTime) {
          hours[i].close = secondaryTime;
          widget.onChanged?.call();
        },
        rightButtonMode: OpenHourEditItemRightButtonMode.Remove,
        onRightButtonPressed: (mainTime, secondaryTime) {
          //Remove this one?
          print('Try remove the index: ' + i.toString());
          setState(() {
            hours.removeAt(i);
          });
          // removeIndex = i;
          widget.onChanged?.call();
          return true;
        }
      )));
    }

    // if (removeIndex != -1) {
    //   print('Remove the index: ' + removeIndex.toString());
    //   hours.removeAt(removeIndex);
    // }

    //Add new time?
    columnChildren.add(OpenHourEditItem(OpenHourEditItemArgs(
        mainTime: '',
        onMainTimeChanged: (mainTime) {
          //Do nothing
        },
        secondaryTime: '',
        onSecondaryTimeChanged: (secondaryTime) {
          //Do nothing
        },
        rightButtonMode: OpenHourEditItemRightButtonMode.Add,
        onRightButtonPressed: (mainTime, secondaryTime) {
          //Add a new hour
          // print('Add SettingOpenHour [' + mainTime + ', ' + secondaryTime + ']');
          if (mainTime != null && mainTime.isNotEmpty && secondaryTime != null && secondaryTime.isNotEmpty) {
            setState(() {
              hours.add(SettingOpenHour(
                open: mainTime,
                close: secondaryTime,
              ));
            });
            widget.onChanged?.call();
            return true;
          } else {
            return false;
          }
        }
    )));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: columnChildren,
    );
  }
}