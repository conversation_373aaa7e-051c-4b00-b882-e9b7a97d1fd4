import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:muyipork/app/data/models/other/order_detail.dart';
import 'package:muyipork/app/data/models/res/order_root.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:screenshot/screenshot.dart';

import 'label_value.dart';

class ReceiptView extends StatelessWidget {
  final String storeName;
  final String accountName;
  final OrderRoot ordersOrderIdGetRes;
  final ScreenshotController screenshotController;

  const ReceiptView({
    Key key,
    this.storeName,
    this.accountName,
    this.ordersOrderIdGetRes,
    this.screenshotController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Screenshot(
      controller: screenshotController,
      child: Container(
        // padding: EdgeInsets.all(16),
        width: 370.0,
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                // '店家名稱分店',
                this.storeName ?? '',
                style: Theme.of(context)
                    .textTheme
                    .headline5
                    .copyWith(color: Colors.black, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
            Builder(builder: (context) {
              return LabelValue(
                labelText: '列印時間：',
                valueText: '${DateTime.now().MMddHHmm} $accountName',
                labelStyle: kPrintStyle,
                valueStyle: kPrintStyle,
              ).paddingSymmetric(
                vertical: 8.0,
              );
            }),
            Row(
              children: [
                ColoredBox(
                  color: Colors.black,
                  child: Text(
                    // '內用',
                    ordersOrderIdGetRes?.data?.orderServiceDisplayStr() ?? '',
                    style: Theme.of(context)
                        .textTheme
                        .headline5
                        .copyWith(color: Colors.white),
                    textAlign: TextAlign.center,
                  ).paddingSymmetric(
                    horizontal: 4.0,
                    vertical: 8.0,
                  ),
                ),
                Container(
                  width: 8.0,
                  color: Colors.white,
                ),
                Expanded(
                  child: ColoredBox(
                    color: Colors.black,
                    child: Row(
                      children: [
                        Text(
                          // '168',
                          // '${this.data?.data?.id}' ?? '',
                          '${this.ordersOrderIdGetRes?.data?.orderSerialNumber}' ??
                              '',
                          style: Theme.of(context)
                              .textTheme
                              .headline5
                              .copyWith(color: Colors.white),
                          textAlign: TextAlign.center,
                        ).paddingSymmetric(
                          horizontal: 8.0,
                        ),
                        Expanded(
                          child: Text(
                            // 'A區3桌',
                            this.ordersOrderIdGetRes.displayTable,
                            style: Theme.of(context)
                                .textTheme
                                .headline5
                                .copyWith(color: Colors.white),
                            textAlign: TextAlign.right,
                          ).paddingSymmetric(
                            horizontal: 4.0,
                            vertical: 8.0,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Center(
              child: Text(
                '****************消費明細****************',
                style: kPrintStyle,
                textHeightBehavior:
                    TextHeightBehavior(applyHeightToFirstAscent: false),
                textAlign: TextAlign.center,
                softWrap: false,
                maxLines: 1,
                overflow: TextOverflow.clip,
              ).paddingSymmetric(
                vertical: 12.0,
              ),
            ),
            Builder(builder: (context) {
              return spaceLine;
            }),
            Builder(builder: (context) {
              return orderDetailsPart1;
            }),
            const SizedBox(
              height: 8.0,
            ),
            SizedBox(
              width: double.infinity,
              child: SvgPicture.asset(
                'assets/images/dot_line_1.svg',
                fit: BoxFit.fitWidth,
              ),
            ),
            const SizedBox(
              height: 8.0,
            ),
            Row(
              children: [
                Text.rich(
                  TextSpan(
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                    ),
                    children: [
                      TextSpan(
                        text: '合計 ',
                        style: kPrintStyle,
                      ),
                      TextSpan(
                        text: '${this.ordersOrderIdGetRes?.orderCount} ',
                        style: kPrintStyle,
                      ),
                      TextSpan(
                        text: '訂單，',
                        style: kPrintStyle,
                      ),
                      TextSpan(
                        text: '${ordersOrderIdGetRes.normalItemsCount} ',
                        style: kPrintStyle,
                      ),
                      TextSpan(
                        text: '項，',
                        style: kPrintStyle,
                      ),
                      TextSpan(
                        text:
                            '${this.ordersOrderIdGetRes?.data?.total?.decimalStyle}',
                        style: kPrintStyle,
                      ),
                      TextSpan(
                        text: '元',
                        style: kPrintStyle,
                      ),
                    ],
                  ),
                  textHeightBehavior: const TextHeightBehavior(
                    applyHeightToFirstAscent: false,
                  ),
                  textAlign: TextAlign.left,
                )
              ],
            ),
            Builder(builder: (context) {
              return spaceLine;
            }),
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              LabelValue(
                labelText: '商品小計：',
                valueText:
                    '${this.ordersOrderIdGetRes?.data?.subtotal?.decimalStyle ?? '0'}元',
                labelStyle: kPrintStyle,
                valueStyle: kPrintStyle,
              ),
              Visibility(
                visible: StoreType.Dinner ==
                    this.ordersOrderIdGetRes?.data?.storeType,
                child: LabelValue(
                  labelText: '服務費：',
                  valueText: () {
                    final value =
                        this.ordersOrderIdGetRes?.data?.serviceCharges ?? 0;
                    return '${value.decimalStyle}元';
                  }(),
                  labelStyle: kPrintStyle,
                  valueStyle: kPrintStyle,
                ),
                replacement: LabelValue(
                  labelText: '運費：',
                  valueText: () {
                    final value = this
                            .ordersOrderIdGetRes
                            ?.data
                            ?.orderShipping
                            ?.shippingFee ??
                        0;
                    return '${value.decimalStyle}元';
                  }(),
                  labelStyle: kPrintStyle,
                  valueStyle: kPrintStyle,
                ),
              ),
              // LabelValue(
              //   labelText: this.ordersOrderIdGetRes?.data?.orderStoreType?.feeName ?? '',
              //   valueText: () {
              //     final serviceFee = this.ordersOrderIdGetRes?.data?.serviceFee ?? 0;
              //     final shippingFee = this.ordersOrderIdGetRes?.data?.orderShipping?.shippingFee ?? 0;
              //     return '${}元';

              //   }(),
              //       '${this.ordersOrderIdGetRes.data.type < 3 ? this.ordersOrderIdGetRes?.data?.displayServiceFee : this.ordersOrderIdGetRes?.data?.orderShipping?.shippingFee?.displayCurrency}元',
              //   labelStyle: kPrintStyle,
              //   valueStyle: kPrintStyle,
              // ),
            ]),
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              LabelValue(
                labelText: '現場減價：',
                valueText:
                    '${this.ordersOrderIdGetRes?.getDiscountPrice(DiscountType.Discount)?.decimalStyle}元',
                labelStyle: kPrintStyle,
                valueStyle: kPrintStyle,
              ),
              LabelValue(
                labelText: '額外費用：',
                valueText: () {
                  final value = ordersOrderIdGetRes.additionalCharges;
                  return '${value.decimalStyle}元';
                }(),
                labelStyle: kPrintStyle,
                valueStyle: kPrintStyle,
              ),
            ]),
            // 餐飲外送會出現運費
            Visibility(
              visible: this.ordersOrderIdGetRes.data.type == 2,
              child: LabelValue(
                labelText: '運費：',
                valueText:
                    '${this.ordersOrderIdGetRes?.data?.orderShipping?.shippingFee?.decimalStyle ?? 0}元',
                labelStyle: kPrintStyle,
                valueStyle: kPrintStyle,
              ),
            ),
            Visibility(
              visible: this.ordersOrderIdGetRes.data.type > 2,
              child: LabelValue(
                labelText: '金流手續費：',
                valueText:
                    '${this.ordersOrderIdGetRes?.data?.orderPayment?.info?.paymentFee?.decimalStyle ?? 0}元',
                labelStyle: kPrintStyle,
                valueStyle: kPrintStyle,
              ),
            ),
            Builder(builder: (context) {
              return spaceLine;
            }),
            LabelValue(
              labelText: '商品總價：',
              valueText:
                  '${this.ordersOrderIdGetRes?.data?.total?.decimalStyle ?? '0'}元',
              labelStyle: TextStyle(
                fontSize: 25,
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
              valueStyle: TextStyle(
                fontSize: 25,
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
            ),
            Row(mainAxisAlignment: MainAxisAlignment.start, children: [
              LabelValue(
                labelText: '實收：',
                valueText:
                    '${this.ordersOrderIdGetRes?.data?.orderPayment?.paid?.decimalStyle ?? '0'}元',
                labelStyle: kPrintStyle,
                valueStyle: kPrintStyle,
              ),
              const SizedBox(
                width: 8.0,
              ),
              LabelValue(
                labelText: '找零：',
                valueText:
                    '${(this.ordersOrderIdGetRes?.data?.orderPayment?.change ?? 0) > 0 ? this.ordersOrderIdGetRes?.data?.orderPayment?.change?.decimalStyle : '0'}元',
                labelStyle: kPrintStyle,
                valueStyle: kPrintStyle,
              ),
            ]),
            Builder(builder: (context) {
              return spaceLine;
            }),
            Builder(builder: (context) {
              return orderDetailsPart2;
            }),
            Builder(builder: (context) {
              return spaceLine;
            }),
            Center(
              child: Text(
                this.ordersOrderIdGetRes.data.type < 3 ? '餐點明細' : '商品明細',
                style: kPrintStyle,
                textHeightBehavior:
                    TextHeightBehavior(applyHeightToFirstAscent: false),
                textAlign: TextAlign.center,
              ).paddingSymmetric(
                vertical: 12.0,
              ),
            ),
            Builder(builder: (context) {
              return spaceLine;
            }),
            Column(
              children: children,
            ),
            Builder(builder: (context) {
              return spaceLine;
            }),
            Row(mainAxisAlignment: MainAxisAlignment.end, children: [
              LabelValue(
                labelText: '小計：',
                valueText: '${ordersOrderIdGetRes.normalItemsCount}項',
                labelStyle: kPrintStyle,
                valueStyle: kPrintStyle,
              ),
              const SizedBox(
                width: 8.0,
              ),
              LabelValue(
                labelText: '金額：',
                valueText:
                    '${this.ordersOrderIdGetRes?.data?.total?.decimalStyle ?? '0'}元',
                labelStyle: kPrintStyle,
                valueStyle: kPrintStyle,
              ),
            ])
          ],
        ),
      ),
    );
  }

  Iterable<Widget> get children {
    // return List.generate(this.data?.data?.orderItems?.length ?? 0, (index) {
    final ls = this.ordersOrderIdGetRes?.allOrderItems ?? <OrderItem>[];
    return List.generate(ls.length, (index) {
      return _Item02(
        data: ls.elementAt(index),
      );
    });
  }

  Widget get spaceLine {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 8.0,
        ),
        SizedBox(
          width: double.infinity,
          child: SvgPicture.asset(
            'assets/images/dot_line_1.svg',
            fit: BoxFit.fitWidth,
          ),
        ),
        const SizedBox(
          height: 8.0,
        ),
      ],
    );
  }

  Widget get orderDetailsPart1 {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        LabelValue(
          labelText: '訂單編號：',
          valueText: this.ordersOrderIdGetRes?.data?.orderNumber ?? '',
          labelStyle: kPrintStyle,
          valueStyle: kPrintStyle,
        ),
        LabelValue(
          labelText: '結帳時間：',
          valueText: this
                  .ordersOrderIdGetRes
                  ?.data
                  ?.orderDiner
                  ?.checkoutAt
                  ?.localAt
                  ?.MMddHHmm ??
              '',
          labelStyle: kPrintStyle,
          valueStyle: kPrintStyle,
        ),
        Visibility(
          visible: this.ordersOrderIdGetRes.data.type < 3,
          child: LabelValue(
            labelText:
                this.ordersOrderIdGetRes?.data?.type == 0 ? '用餐時間：' : '取餐時間：',
            valueText:
                this.ordersOrderIdGetRes?.data?.orderDiner?.displayMealAt ?? '',
            labelStyle: kPrintStyle,
            valueStyle: kPrintStyle,
          ),
        ),
        // 目前取消取貨時間
        // Visibility(
        //   visible: this.ordersOrderIdGetRes.data.type > 2,
        //   child: LabelValue(
        //     labelText: '取貨時間：',
        //     valueText:
        //         this.ordersOrderIdGetRes?.data?.orderDiner?.displayMealAt ?? '',
        //     labelStyle: kPrintStyle,
        //     valueStyle: kPrintStyle,
        //   ),
        // ),
        LabelValue(
          labelText: '發票號碼：',
          valueText: this.ordersOrderIdGetRes?.data?.orderInvoice?.number ?? '',
          labelStyle: kPrintStyle,
          valueStyle: kPrintStyle,
        ),
        Visibility(
            visible:
                this.ordersOrderIdGetRes?.data?.orderInvoice?.vatNumber != null,
            child: LabelValue(
              labelText: '統一編號：',
              valueText:
                  this.ordersOrderIdGetRes?.data?.orderInvoice?.vatNumber ?? '',
              labelStyle: kPrintStyle,
              valueStyle: kPrintStyle,
            )),
        Visibility(
            visible:
                this.ordersOrderIdGetRes?.data?.orderInvoice?.carrierId != null,
            child: LabelValue(
              labelText: '載具：',
              valueText:
                  this.ordersOrderIdGetRes?.data?.orderInvoice?.carrierId ?? '',
              labelStyle: kPrintStyle,
              valueStyle: kPrintStyle,
            )),
        Visibility(
            visible:
                this.ordersOrderIdGetRes?.data?.orderInvoice?.npoBan != null,
            child: LabelValue(
              labelText: '愛心碼：',
              valueText:
                  this.ordersOrderIdGetRes?.data?.orderInvoice?.npoBan ?? '',
              labelStyle: kPrintStyle,
              valueStyle: kPrintStyle,
            )),
        LabelValue(
          labelText: '支付方式：',
          valueText: this.ordersOrderIdGetRes?.data?.displayOrderPayment ?? '',
          labelStyle: kPrintStyle,
          valueStyle: kPrintStyle,
        )
      ],
    );
  }

  Widget get orderDetailsPart2 {
    return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LabelValue(
            labelText: '訂單編號：',
            valueText: this.ordersOrderIdGetRes?.data?.orderNumber ?? '',
            labelStyle: kPrintStyle,
            valueStyle: kPrintStyle,
          ),
          LabelValue(
            labelText: '訂單時間：',
            valueText: this.ordersOrderIdGetRes?.data?.displayCreatedAt ?? '',
            labelStyle: kPrintStyle,
            valueStyle: kPrintStyle,
          ),
          LabelValue(
            labelText: '訂單狀態：',
            valueText: this.ordersOrderIdGetRes?.data?.displayStatus ?? '',
            labelStyle: kPrintStyle,
            valueStyle: kPrintStyle,
          ),
          LabelValue(
            labelText:
                this.ordersOrderIdGetRes.data.type < 3 ? '用餐方式：' : '取貨方式：',
            valueText:
                ordersOrderIdGetRes?.data?.orderServiceDisplayStr() ?? '',
            labelStyle: kPrintStyle,
            valueStyle: kPrintStyle,
          ),
          LabelValue(
            labelText: '付款方式：',
            valueText: ordersOrderIdGetRes?.data?.displayOrderPayment ?? '',
            labelStyle: kPrintStyle,
            valueStyle: kPrintStyle,
          ),
          LabelValue(
            labelText: '付款狀態：',
            valueText: ordersOrderIdGetRes?.data?.displayPaymentStatus ?? '',
            labelStyle: kPrintStyle,
            valueStyle: kPrintStyle,
          ),
          Visibility(
              visible: this.ordersOrderIdGetRes.data.type < 3,
              child: LabelValue(
                labelText: this.ordersOrderIdGetRes?.data?.type == 0
                    ? '用餐時間：'
                    : '取餐時間：',
                valueText:
                    this.ordersOrderIdGetRes?.data?.orderDiner?.displayMealAt ??
                        '',
                labelStyle: kPrintStyle,
                valueStyle: kPrintStyle,
              )),
          Builder(builder: (context) {
            return clientInfo;
          }),
        ]);
  }

  Widget get clientInfo {
    String labelNamePre;
    List<int> inStoreType = [0, 1, 3];
    String name;
    String phone;
    String address;
    if (this.ordersOrderIdGetRes.data.type < 3) {
      labelNamePre = '顧客';
    } else if (this.ordersOrderIdGetRes.data.type == 3) {
      labelNamePre = '取貨';
    } else {
      labelNamePre = '收件人';
    }

    if (inStoreType.contains(this.ordersOrderIdGetRes.data.type)) {
      name = this.ordersOrderIdGetRes?.data?.displayBuyerName ?? '現場消費者';
      phone = this.ordersOrderIdGetRes?.data?.displayBuyerPhone ?? '';
      address = this.ordersOrderIdGetRes?.data?.displayBuyerAddress ?? '';
    } else {
      name = this.ordersOrderIdGetRes?.data?.displayReceiverName ?? '';
      phone = this.ordersOrderIdGetRes?.data?.displayReceiverPhone ?? '';
      address = this.ordersOrderIdGetRes?.data?.displayReceiverAddress ?? '';
    }
    return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LabelValue(
            labelText: '$labelNamePre姓名：',
            valueText: name ?? '',
            labelStyle: kPrintStyle,
            valueStyle: kPrintStyle,
          ),
          LabelValue(
            labelText: '$labelNamePre電話：',
            valueText: phone ?? '',
            labelStyle: kPrintStyle,
            valueStyle: kPrintStyle,
          ),
          Visibility(
              visible:
                  !inStoreType.contains(this.ordersOrderIdGetRes.data.type),
              child: LabelValue(
                labelText: '$labelNamePre地址：',
                valueText: address ?? '',
                labelStyle: kPrintStyle,
                valueStyle: kPrintStyle,
              )),
          LabelValue(
            labelText: '顧客備註：',
            valueText: this.ordersOrderIdGetRes?.data?.dinerMemo ?? '',
            labelStyle: kPrintStyle,
            valueStyle: kPrintStyle,
          ),
        ]);
  }
}

class _Item02 extends StatelessWidget {
  final OrderItem data;

  const _Item02({
    Key key,
    @required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String productTitle = data.productName ?? '';
    // productTitle += data.quantity != null ? ' x ' + data.quantity.toString() : '';
    final quantity = data.quantity ?? 1;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 8.0,
        ),
        Text(
          '$productTitle',
          style: Theme.of(context)
              .textTheme
              .headline6
              .copyWith(color: Colors.black),
          textAlign: TextAlign.left,
        ),
        SizedBox(
          height: 4.0,
        ),
        Visibility(
          visible: this.data.productSpec1?.isNotEmpty ?? false,
          child: Text(
            // '半碗飯、不要太多肉',
            this.data?.productSpec1 ?? '',
            style: Theme.of(context)
                .textTheme
                .headline6
                .copyWith(color: Colors.black),
            textAlign: TextAlign.left,
          ),
        ),
        SizedBox(
          height: 4.0,
        ),
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Text(
            // '\$428',
            '\$${this.data.finalPrice.decimalStyle ?? 0} x $quantity',
            style: Theme.of(context)
                .textTheme
                .headline6
                .copyWith(color: Colors.black),
            textAlign: TextAlign.left,
          ),
          Text(
            '${(this.data.finalPrice * quantity).decimalStyle ?? 0} 元',
            style: Theme.of(context)
                .textTheme
                .headline6
                .copyWith(color: Colors.black),
            textAlign: TextAlign.right,
          ),
        ]),
      ],
    );
  }
}

// class _Page extends StatelessWidget {
//   final String storeName;
//   final OrdersOrderIdGetRes data;

//   const _Page({
//     Key key,
//     this.storeName,
//     this.data,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Center(
//           child: Text(
//             // '店家名稱分店',
//             this.storeName ?? '',
//             style: const TextStyle(
//               fontSize: 32,
//               color: Colors.black,
//               fontWeight: FontWeight.w700,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//         SizedBox(
//           height: 4.0,
//         ),
//         Center(
//           child: Text(
//             // '168',
//             this.data?.data?.orderNumber ?? '',
//             style: const TextStyle(
//               fontSize: 40,
//               color: Colors.black,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//         SizedBox(
//           height: 4.0,
//         ),
//         Center(
//           child: Text(
//             '消費明細',
//             style: const TextStyle(
//               fontSize: 24,
//               color: Colors.black,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//         SizedBox(
//           height: 16.0,
//         ),
//         Row(
//           children: [
//             ColoredBox(
//               color: Colors.black,
//               child: Text(
//                 '內用',
//                 style: const TextStyle(
//                   fontSize: 24,
//                   color: Colors.white,
//                 ),
//                 textAlign: TextAlign.center,
//               ).paddingSymmetric(
//                 vertical: 2.0,
//                 horizontal: 4.0,
//               ),
//             ),
//             Text(
//               // ' A區3桌',
//               this.data?.data?.orderDiner?.displayTable ?? '',
//               style: const TextStyle(
//                 fontSize: 24,
//                 color: Colors.black,
//               ),
//               textAlign: TextAlign.left,
//             ),
//           ],
//         ),
//         const SizedBox(
//           height: 8.0,
//         ),
//         Row(
//           children: [
//             const Text(
//               // '用餐時間：03-17 17:20',
//               '用餐時間：',
//               style: const TextStyle(
//                 fontSize: 24,
//                 color: Colors.black,
//               ),
//               textAlign: TextAlign.right,
//             ),
//             Text(
//               // '03-17 17:20',
//               this.data?.data?.orderDiner?.displayMealAt ?? '',
//               style: const TextStyle(
//                 fontSize: 24,
//                 color: Colors.black,
//               ),
//               textAlign: TextAlign.right,
//             ),
//           ],
//         ),
//         const SizedBox(
//           height: 20.0,
//         ),
//         SizedBox(
//           width: double.infinity,
//           child: SvgPicture.asset(
//             'assets/images/dot_line_1.svg',
//             fit: BoxFit.fitWidth,
//           ),
//         ),
//         const SizedBox(
//           height: 20.0,
//         ),
//         ...List.generate(this.data?.data?.orderItems?.length ?? 0, (index) {
//           return _Item(
//             data: this.data?.data?.orderItems?.elementAt(index),
//           );
//         }),
//         SizedBox(
//           width: double.infinity,
//           child: SvgPicture.asset(
//             'assets/images/dot_line_1.svg',
//             fit: BoxFit.fitWidth,
//           ),
//         ),
//         Text(
//           // '3 項，566 元',
//           '${this.data?.data?.subtotal?.displayCurrency} 元',
//           style: const TextStyle(
//             fontSize: 36,
//             color: Colors.black,
//             fontWeight: FontWeight.w500,
//           ),
//         ).paddingSymmetric(
//           vertical: 4.0,
//         ),
//         SizedBox(
//           width: double.infinity,
//           child: SvgPicture.asset(
//             'assets/images/dot_line_1.svg',
//             fit: BoxFit.fitWidth,
//           ),
//         ),
//         const SizedBox(
//           height: 20.0,
//         ),
//       ],
//     );
//   }
// }

// class _Item extends StatelessWidget {
//   final OrderItem data;

//   const _Item({
//     Key key,
//     @required this.data,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     String productTitle = data.productName ?? '';
//     productTitle += data.quantity != null ? ' x ' + data.quantity.toString() : '';

//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Text(
//           // '厚切腰內豬排(1)428',
//           productTitle,
//           style: const TextStyle(
//             fontSize: 24,
//             color: Colors.black,
//             fontWeight: FontWeight.w600,
//           ),
//           textAlign: TextAlign.left,
//         ),
//         Visibility(
//           visible: this.data.productSpec1?.isNotEmpty ?? false,
//           child: Text(
//             // '/不要切',
//             this.data?.productSpec1 ?? '',
//             style: const TextStyle(
//               fontSize: 24,
//               color: Colors.black,
//               fontWeight: FontWeight.w100,
//             ),
//             textAlign: TextAlign.left,
//           ),
//         ),
//         const SizedBox(
//           height: 20.0,
//         ),
//       ],
//     );
//   }
// }
