import 'dart:io';
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as Img;
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:muyipork/app/data/models/other/image_model.dart';
import 'package:muyipork/constants.dart';
import 'package:muyipork/enums.dart';
import 'package:muyipork/extension.dart';
import 'package:reorderables/reorderables.dart';

import 'dialog_actions.dart';
import 'image_item.dart';

class AlbumEditor extends StatelessWidget {
  static const MAX_TILE = 10;
  static const _THUMBNAIL_SIZE = 60.0;
  static const _IMAGE_SIZE = 600.0;
  final RxList<ImageModel> imagesToRemove;
  final RxList<ImageModel> tiles;
  final regexp = RegExp(r'^https?://');
  final bool editing;
  final ValueChanged<ImageModel> onPressed;

  AlbumEditor({
    Key key,
    @required this.tiles,
    @required this.imagesToRemove,
    @required this.editing,
    @required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => _main());
  }

  Widget _main() {
    final ls = tiles
        .map((e) {
          final fromNetwork = regexp.hasMatch(e.url);
          final child =
              fromNetwork ? _getNetworkImage(e.url) : _getLocalImage(e.url);
          final remover = () {
            if (tiles.remove(e)) {
              imagesToRemove.add(e);
            }
          };
          return ImageItem(
            child: child,
            onDeletePressed: editing ? remover : null,
            onPressed: () => onPressed?.call(e),
          );
        })
        .cast<Widget>()
        .toList();
    return editing ? _reorder(ls) : _wrap(ls);
  }

  Widget _getLocalImage(String path) {
    return SizedBox.fromSize(
      size: Size.square(_THUMBNAIL_SIZE),
      child: Image.file(
        File(path),
        fit: BoxFit.contain,
      ),
    );
  }

  Widget _getNetworkImage(String imageUrl) {
    return SizedBox.fromSize(
      size: Size.square(_THUMBNAIL_SIZE),
      child: CachedNetworkImage(
        fit: BoxFit.contain,
        imageUrl: imageUrl ?? '',
        placeholder: (context, url) {
          return CircularProgressIndicator();
        },
        errorWidget: (context, url, error) => Icon(
          Icons.image,
          size: _THUMBNAIL_SIZE,
          color: Colors.white,
        ),
      ),
    );
  }

  void _onReorder(int oldIndex, int newIndex) {
    logger.d('_onReorder oldIndex($oldIndex), newIndex($newIndex)');
    final tempImage = tiles[oldIndex];
    tiles[oldIndex] = tiles[newIndex];
    tiles[newIndex] = tempImage;
  }

  Widget get _plusIcon {
    return ImageItem(
      child: Icon(
        Icons.add,
        size: 60.0,
        color: Colors.white,
      ),
      onPressed: () {
        this._showImagePicker().then(
          (value) {
            tiles.add(ImageModel(
              url: value.uri.toFilePath(),
            ));
            // test
            // final path = value.uri.toFilePath();
            // controller.apiProvider.postImages([path]).then((value) {
            //   logger.d('message');
            // });
            // controller.apiProvider.getImage('31').then((value) {
            //   logger.d('message');
            // });
            // controller.apiProvider.deleteImage('31').then((value) {
            //   logger.d('message');
            // });
            // controller.apiProvider.getImages().then((value) {
            //   logger.d('message');
            // });
          },
        );
      },
    );
  }

  Widget _reorder(List<Widget> children) {
    return ReorderableWrap(
      needsLongPressDraggable: false,
      spacing: 30.0,
      runSpacing: 30.0,
      children: children,
      onReorder: _onReorder,
      onNoReorder: (int index) {
        //this callback is optional
        logger.d(
            '${DateTime.now().toString().substring(5, 22)} reorder cancelled. index:$index');
      },
      onReorderStarted: (int index) {
        //this callback is optional
        logger.d(
            '${DateTime.now().toString().substring(5, 22)} reorder started: index:$index');
      },
    );
  }

  Widget _wrap(List<Widget> children) {
    children.addIf(children.length < MAX_TILE, _plusIcon);
    return Wrap(
      spacing: 30.0,
      runSpacing: 30.0,
      children: children,
    );
  }

  ///
  /// 顯示圖片選擇器
  ///
  Future<File> _showImagePicker() {
    return DialogActions(
      titleText: '取得照片',
      actions: [
        '相機',
        '相簿',
      ],
    ).dialog<num>().then(
      (value) {
        final ImagePicker _picker = ImagePicker();
        switch (value.button) {
          case Button.Negative:
            return _picker.pickImage(
              source: ImageSource.camera,
              maxWidth: _IMAGE_SIZE,
              maxHeight: _IMAGE_SIZE,
            );
          case Button.Positive:
            return _picker.pickImage(
              source: ImageSource.gallery,
              maxWidth: _IMAGE_SIZE,
              maxHeight: _IMAGE_SIZE,
            );
          default:
        }
      },
    ).then(
      (value) {
        return _cropImage(value.path);
      },
    );
  }

  ///
  /// 裁切圖片
  ///
  Future<File> _cropImage(String path) {
    // create square image with white background
    final data = File(path).readAsBytesSync();
    Img.Image image = Img.decodeImage(data);
    final size = max(image.width, image.height);
    final blank = Img.Image.rgb(size, size);
    blank.fill(Colors.white.value);
    // bland
    final dstX = (size - image.width) ~/ 2;
    final dstY = (size - image.height) ~/ 2;
    final thumbnail = Img.copyInto(
      blank,
      image,
      dstX: dstX,
      dstY: dstY,
      blend: false,
    );
    // Resize the image to a 120x? thumbnail (maintaining the aspect ratio).
    // Img.Image thumbnail = Img.copyResizeCropSquare(image, 300);
    // Img.Image thumbnail2 = Img.copyResize(
    //   image,
    //   width: 300,
    //   height: 300,
    // );
    // Save the thumbnail as a PNG.
    // final newfile = '${path.path}/${path.filename}';
    File(path).writeAsBytesSync(Img.encodePng(thumbnail));

    return ImageCropper.cropImage(
      sourcePath: path,
      maxWidth: _IMAGE_SIZE.round(),
      maxHeight: _IMAGE_SIZE.round(),
      aspectRatio: CropAspectRatio(
        ratioX: 1.0,
        ratioY: 1.0,
      ),
      aspectRatioPresets: [
        CropAspectRatioPreset.square,
        // CropAspectRatioPreset.ratio3x2,
        // CropAspectRatioPreset.original,
        // CropAspectRatioPreset.ratio4x3,
        // CropAspectRatioPreset.ratio16x9
      ],
      androidUiSettings: AndroidUiSettings(
        toolbarTitle: 'Cropper',
        toolbarColor: Colors.deepOrange,
        toolbarWidgetColor: Colors.white,
        initAspectRatio: CropAspectRatioPreset.original,
        lockAspectRatio: true,
        // backgroundColor: Colors.white,
      ),
      iosUiSettings: IOSUiSettings(
          // minimumAspectRatio: 1.0,
          doneButtonTitle: '完成',
          cancelButtonTitle: '取消',
          aspectRatioLockDimensionSwapEnabled: true,
          aspectRatioLockEnabled: true),
    );
  }
}
