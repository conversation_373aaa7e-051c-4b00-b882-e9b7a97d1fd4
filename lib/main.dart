import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:bpscm/bpscm.dart';
import 'package:device_info/device_info.dart';
import 'package:dio/adapter.dart';
import 'package:dio/dio.dart';
import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_sunmi_printer/flutter_sunmi_printer.dart';

import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:muyipork/app/providers/api_provider.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/app/providers/notification_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/app/providers/printer_provider.dart';
import 'package:muyipork/app/providers/setting_provider.dart';
import 'package:package_info/package_info.dart';
import 'package:sizer/sizer.dart';

import 'app/providers/account_provider.dart';
import 'app/providers/coupon_provider.dart';
import 'app/providers/line_pay_provider.dart';
import 'app/providers/local_invoice_provider.dart';
import 'app/providers/member_provider.dart';
import 'app/providers/message_provider.dart';
import 'app/providers/order_provider.dart';
import 'app/providers/product_provider.dart';
import 'app/providers/revenue_provider.dart';
import 'app/providers/table_provider.dart';
import 'app/routes/app_pages.dart';
import 'constants.dart';
import 'extension.dart';
import 'firebase_options.dart';

void main() {
  runZonedGuarded(
    () => _main(),
    catchUnhandledExceptions,
  );
}

void catchUnhandledExceptions(Object error, StackTrace stack) {
  if (kReleaseMode) {
    // FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
  } else {
    FlutterError.presentError(
      FlutterErrorDetails(
        exception: error,
        stack: stack,
        library: 'main.dart',
        context: ErrorDescription('Top-level error handler'),
      ),
    );
  }
}

void _main() {
  // it should be the first line in main method
  WidgetsFlutterBinding.ensureInitialized();
  FlutterError.onError = (FlutterErrorDetails details) {
    // if (kReleaseMode) {
    if (kDebugMode) {
      // development mode, log to console
      FlutterError.dumpErrorToConsole(details);
    } else {
      // production mode, log to remote service
      // catchUnhandledExceptions(details.exception, details.stack);
      Zone.current.handleUncaughtError(details.exception, details.stack);
    }
  };
  // 背景執行
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  // AWAIT SERVICES INITIALIZATION.
  _initServices().then((value) => _runApp());
}

void _runApp() {
  runApp(
    Sizer(
      builder: (context, orientation, deviceType) {
        return GetMaterialApp(
          debugShowCheckedModeBanner: false,
          title: "OKSHOP集客多",
          initialRoute: AppPages.INITIAL,
          getPages: AppPages.routes,
          theme: ThemeData(
            appBarTheme: AppBarTheme(
              backgroundColor: Colors.transparent,
              brightness: Brightness.dark,
              elevation: 0.0,
              centerTitle: true,
            ),
            inputDecorationTheme: InputDecorationTheme(
              labelStyle: TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
              hintStyle: TextStyle(
                fontSize: 16,
                color: OKColor.Gray66,
              ),
            ),
            primaryColor: OKColor.Primary,
            accentColor: OKColor.Primary,
          ),
        );
      },
    ),
  );
}

Future<void> _initServices() async {
  logger.d('initServices');
  ByteData data =
      await PlatformAssetBundle().load('assets/ca/lets-encrypt-r3.pem');
  SecurityContext.defaultContext
      .setTrustedCertificatesBytes(data.buffer.asUint8List());
  await initializeDateFormatting();
  await Hive.initFlutter();
  final userDefault = await Hive.openBox(kKeySettings);
  // 中文化 server 回傳訊息翻譯
  try {
    await Get.putAsync(
      () async {
        final jsonString =
            await rootBundle.loadString('assets/models/code_message.json');
        return MessageProvider(codeMessage: jsonDecode(jsonString));
      },
      permanent: true,
    );
  } catch (e) {
    logger.e(e);
  }
  // await Hive.openBox(kKeyOrders);
  // await Hive.openLazyBox(kKeyOrders);
  // Get.putAsync(
  //   () => PackageInfo.fromPlatform(),
  //   permanent: true,
  // );
  try {
    await Get.putAsync(
      () => PackageInfo.fromPlatform(),
      permanent: true,
    );
  } catch (e) {
    logger.e(e);
  }
  Get.lazyPut(() => logger, fenix: true);
  // db (object box)
  Get.lazyPut(
    () => BoxProvider(
      userDefault: userDefault,
      logger: Get.find(),
    ),
    fenix: true,
  );
  // 儲存在本機的設定
  Get.lazyPut(
    () => PrefProvider(
      packageInfo: Get.find(),
      boxProvider: Get.find(),
    ),
    fenix: true,
  );
  // init firebase
  await _initFirebase(Get.find<PrefProvider>());
  // 裝置訊息
  // device info
  if (Platform.isAndroid) {
    try {
      final value = await DeviceInfoPlugin().androidInfo;
      logger.d('$value');
      userDefault.put(kKeyModel, value.model);
      // 機器序號
      userDefault.put(kMachineNumber, value.androidId);
    } catch (e) {
      logger.e(e);
    }
  } else if (Platform.isIOS) {
    try {
      final value = await DeviceInfoPlugin().iosInfo;
      logger.d('$value');
      userDefault.put(kKeyModel, value.model);
      // 機器序號
      userDefault.put(kMachineNumber, value.identifierForVendor);
    } catch (e) {
      logger.e(e);
    }
  }
  // inner printer
  try {
    final value = await SunmiPrinter.sunmiPrinterStatus();
    userDefault.put(kKeyInnerPrinter, value.index);
  } catch (e) {
    logger.e(e);
  }
  // 網路通訊
  Get.lazyPut(
    () {
      final dio = Dio();
      // dio.interceptors.add(LogInterceptor(
      //   requestHeader: true,
      //   requestBody: true,
      //   responseBody: true,
      //   responseHeader: false,
      // )); // 开启请求日志
      // dio.interceptors.add(PrettyDioLogger());
      // 忽略憑證錯誤
      (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
          (HttpClient client) {
        client.badCertificateCallback =
            (X509Certificate cert, String host, int port) => true;
        return client;
      };
      // customization
      // dio.interceptors.add(PrettyDioLogger(
      //   // requestHeader: true,
      //   requestBody: true,
      //   responseBody: true,
      //   // responseHeader: false,
      //   error: true,
      //   compact: true,
      //   // maxWidth: 90,
      // ));
      return dio;
    },
    fenix: true,
  );
  Get.lazyPut(
    () => ApiProvider(
      dio: Get.find(),
      prefProvider: Get.find(),
      messageProvider: Get.find(),
    ),
    fenix: true,
  );
  // 取得設定相關 api
  Get.lazyPut(() => SettingProvider(apiProvider: Get.find()), fenix: true);
  // LINE Pay
  Get.lazyPut(() => LinePayProvider(apiProvider: Get.find()), fenix: true);
  Get.lazyPut(
    () {
      final prefProvider = Get.find<PrefProvider>();
      return InvoiceProvider(
        dio: Dio(),
        apiKey: prefProvider.invoiceApiKey,
        posBAN: prefProvider.posBAN,
        baseUrl: prefProvider.invoiceUrl,
        soapUrl: prefProvider.soapUrl,
      );
    },
    fenix: true,
  );
  // 印表機
  Get.lazyPut(() => PrinterProvider(apiProvider: Get.find()), fenix: true);
  // 優惠券
  Get.lazyPut(() => CouponProvider(apiProvider: Get.find()), fenix: true);
  // 使用者帳戶操作
  Get.lazyPut(() => AccountProvider(apiProvider: Get.find()), fenix: true);
  Get.lazyPut(() => RevenueProvider(apiProvider: Get.find()), fenix: true);
  Get.lazyPut(() => ProductProvider(apiProvider: Get.find()), fenix: true);
  Get.lazyPut(() => TableProvider(apiProvider: Get.find()), fenix: true);
  Get.lazyPut(() => MemberProvider(apiProvider: Get.find()), fenix: true);
  Get.lazyPut(() => OrderProvider(memberProvider: Get.find()), fenix: true);
  await Get.putAsync(
    () async {
      final res = NotificationProvider(orderProvider: Get.find());
      await res.init();
      return res;
    },
    permanent: true,
  );
  // 預存發票資料
  Get.lazyPut(
    () => LocalInvoiceProvider(
      invoiceProvider: Get.find(),
      boxProvider: Get.find(),
    ),
    fenix: true,
  );
}

Future<void> _initFirebase(PrefProvider prefProvider) async {
  // Create the initialization for your desired push service here
  if (Firebase.apps.isEmpty) {
    await Firebase.initializeApp(
      options: await DefaultFirebaseOptions.getCurrentPlatform(
          prefProvider.isSandbox),
    );
  }

  final messaging = FirebaseMessaging.instance;
  // 使用 firebase 取得推播權限
  // final settings = await messaging.requestPermission(
  //   alert: true,
  //   announcement: false,
  //   badge: true,
  //   carPlay: false,
  //   criticalAlert: false,
  //   provisional: false,
  //   sound: true,
  // );

  // logger.d('User granted permission: ${settings.authorizationStatus}');

  // switch (settings.authorizationStatus) {
  //   case AuthorizationStatus.authorized:
  //     logger.d('User granted permission');
  //     break;
  //   case AuthorizationStatus.provisional:
  //     logger.d('User granted provisional permission');
  //     break;
  //   case AuthorizationStatus.denied:
  //     logger.d('User denied permission');
  //     break;
  //   case AuthorizationStatus.notDetermined:
  //     logger.d('User not determined permission');
  //     break;
  //   default:
  //     print('User declined or has not accepted permission');
  // }

  try {
    final fcmToken = await messaging.getToken();
    logger.d('[FirebaseMessaging] FCM token($fcmToken)');
    prefProvider.fcmToken = fcmToken;
  } on FirebaseException catch (e) {
    logger.e(e);
  }

  /// Update the iOS foreground notification presentation options to allow
  /// heads up notifications.
  await messaging.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );
}

// Declared as global, outside of any class
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.
  log('[FCM] Background messages($message)');
  log("Handling a background message: ${message.messageId}");
  // if (Firebase.apps.isEmpty) {
  //   await Firebase.initializeApp();
  // }
  // init hive
  await Hive.initFlutter();
  final userDefault = await Hive.openBox(kKeySettings);
  // init logger
  Get.lazyPut(() => logger, fenix: true);
  // db (object box)
  Get.lazyPut(
    () => BoxProvider(
      userDefault: userDefault,
      logger: Get.find(),
    ),
    fenix: true,
  );
  // 儲存在本機的設定
  Get.lazyPut(
    () => PrefProvider(
      packageInfo: Get.find(),
      boxProvider: Get.find(),
    ),
    fenix: true,
  );
  // init firebase
  await _initFirebase(Get.find<PrefProvider>());
  // init notification
  final notificationProvider = await Get.putAsync(
    () async {
      final res = NotificationProvider(orderProvider: Get.find());
      await res.init();
      return res;
    },
    permanent: true,
  );
  notificationProvider.showNotification(message);
}
