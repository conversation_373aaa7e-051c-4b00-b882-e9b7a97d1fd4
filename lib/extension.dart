// FIXME: try remove me.
export 'package:okshop_common/okshop_common.dart';
// FIXME: try remove me.
export 'package:okshop_model/okshop_model.dart';
import 'package:esc_pos_utils/esc_pos_utils.dart';
import 'package:flutter_godex_printer/flutter_godex_printer.dart';
import 'package:get_storage/get_storage.dart';
import 'package:sizer/sizer.dart';
import 'package:muyipork/app/providers/product_provider.dart';
import 'package:okshop_model/okshop_model.dart';
import 'package:okshop_esc_pos/okshop_esc_pos.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:ui';

import 'package:bpscm/bpscm.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart'
    hide Key
    show CircularProgressIndicator, Colors, Dialog, TimeOfDay;
import 'package:flutter/widgets.dart' hide Key;
import 'package:get/get.dart' hide Condition;
import 'package:intl/intl.dart';
import 'package:muyipork/app/data/models/other/payment_bank.dart';
import 'package:package_info/package_info.dart';
import 'package:okshop_common/okshop_common.dart';
import 'app/components/dialog_general.dart';
import 'app/components/member_picker.dart';
import 'app/components/progressing.dart';
import 'app/components/sticker_widget.dart';
import 'app/data/InvoiceData.dart';
import 'app/data/models/other/brands_info.dart';
import 'app/data/models/other/brands_invoice.dart';
import 'app/data/models/other/brands_invoice_req.dart';
import 'app/data/models/other/brands_news.dart';
import 'app/data/models/other/brands_news_put.dart';
import 'app/data/models/other/brands_put.dart';
import 'app/data/models/other/district.dart';
import 'app/data/models/other/jwt.dart';
import 'app/data/models/other/line_pay_setting.dart';
import 'app/data/models/other/local_settings.dart';
import 'app/data/models/other/multiple_payment.dart';
import 'app/data/models/other/order_detail.dart';
import 'app/data/models/other/orders_collection.dart';
import 'app/data/models/other/pagination.dart';
import 'app/data/models/other/payment_bank_put.dart';
import 'app/data/models/other/payment_instore.dart';
import 'app/data/models/other/printer_task.dart';
import 'app/data/models/other/product_categories_put.dart';
import 'app/data/models/other/qr_format.dart';
import 'app/data/models/other/reports_statements.dart';
import 'app/data/models/other/setting_pay.dart';
import 'app/data/models/other/setting_point.dart';
import 'app/data/models/other/shipping_delivery.dart';
import 'app/data/models/other/store_account.dart';
import 'app/data/models/other/store_account_post.dart';
import 'app/data/models/other/store_account_put.dart';
import 'app/data/models/req/orders_combining_post_req.dart';
import 'app/data/models/req/orders_orderid_invoice_post_req.dart';
import 'app/data/models/req/orders_post_req.dart';
import 'app/data/models/req/products_post_req.dart';
import 'app/data/models/req/setting_order_fee.dart';
import 'app/data/models/res/member_req.dart';
import 'app/data/models/res/orders_get_res.dart';
import 'app/data/models/res/order_root.dart';
import 'app/data/models/res/res_base.dart';
import 'app/data/models/res/setting_get_res.dart';
import 'app/providers/member_provider.dart';
import 'app/providers/message_provider.dart';
import 'app/providers/pref_provider.dart';
import 'app/providers/printer_provider.dart';
import 'constants.dart';
import 'enums.dart';
import 'keys.dart';

DateTime utcToLocal(String value) {
  var ret = DateTime.tryParse('${value}z');
  if (ret == null) {
    ret = DateTime.tryParse('$value');
  }
  return ret?.toLocal();
}

extension OrderRootX on OrderRoot {
  void refresh() {
    // 合併訂單需計算使用積點
    final points = data?.redeemMemberPoints ?? 0;
    if (points == 0 && subOrder != null && subOrder.isNotEmpty) {
      data.redeemMemberPoints = subOrder.fold(0, (previousValue, element) {
        return previousValue + (element.redeemMemberPoints ?? 0).abs();
      });
    }
  }

  // 是否已付款
  bool get isPaid => data.isPaid;

  // 運費
  num get orderShippingFee {
    return subOrderList.fold(data.orderShippingFee, (previousValue, element) {
      return previousValue + element.orderShippingFee;
    });
  }

  // shortcut: 金流手續費
  num get orderPaymentFee {
    return subOrderList.fold(data.orderPaymentFee, (previousValue, element) {
      return previousValue + element.orderPaymentFee;
    });
  }

  // shortcut: 服務費
  num get serviceCharges => data.serviceCharges;
  // shortcut: 額外費用
  num get additionalCharges => data.additionalCharges;
  // shortcut: 依據訂單 type 得到的地址
  OrderAddress get targetOrderAddress => data.targetOrderAddress;
  // shortcut
  OrderType get orderType => data?.type?.orderType ?? OrderType.Max;
  // shortcut
  StoreType get storeType => data.storeType;
  // order id
  num get id => data.id;
  // 訂單發票狀態
  InvoiceStatus get invoiceStatus {
    return data?.invoiceStatus ?? InvoiceStatus.Max;
  }

  /// 判斷可合併結帳
  bool get isCombineAvailable {
    // 零售不可合併結帳
    if (data.type.orderType.isRetail) {
      return false;
    }
    // 有使用積點不可合併結帳
    // if (data.redeemMemberPoints != null && data.redeemMemberPoints != 0) {
    //   return false;
    // }
    return true;
  }

  String get displayTable {
    final value1 = data?.orderDiner?.displayTable ?? '';
    if (value1.isNotEmpty) {
      return value1;
    }
    final orderDetail = subOrderList.firstWhere((element) {
      final value2 = element?.orderDiner?.displayTable ?? '';
      return value2.isNotEmpty;
    }, orElse: () => null);
    return orderDetail?.orderDiner?.displayTable ?? '';
  }

  bool get statusCompleted => data.status == OrderStatus.Completed.index;

  bool get statusPadding => data.status == OrderStatus.Padding.index;

  Iterable<OrderItem> get normalItems {
    // 有子訂單代表這是合併訂單，不計算 master 訂單的 normal items
    final list = subOrderList;
    if (list.isNotEmpty) {
      return list.fold<List<OrderItem>>(<OrderItem>[],
          (previousValue, element) {
        previousValue.addAll(element.normalItems);
        return previousValue;
      });
    }
    // 無子訂單代表這是單一訂單
    if (data != null) {
      return data.normalItems;
    }
    return <OrderItem>[];
  }

  num get normalItemsCount {
    return normalItems.fold<num>(0, (previousValue, element) {
      return previousValue + element.nnQuantity;
    });
  }

  num get normalItemsPrice {
    return normalItems.fold<num>(0, (previousValue, element) {
      return previousValue + element.stackPrice;
    });
  }

  num get orderCount {
    final count = subOrderCount;
    if (count > 0) {
      // 有子訂單代表這是合併訂單
      return count;
    }
    // 無子訂單代表這是單一訂單
    if (data != null) {
      return 1;
    }
    return 0;
  }

  Iterable<OrderDetail> get subOrderList {
    subOrder ??= <OrderDetail>[];
    return subOrder;
  }

  // 子訂單數量
  num get subOrderCount => subOrderList.length;

  num get subtotal => data?.subtotal ?? 0;

  num get paid => data?.orderPayment?.paid ?? 0;

  num get change => data?.orderPayment?.change ?? 0;

  // 取得折價價格
  num getDiscountPrice(DiscountType value) {
    return data.getDiscountPrice(value) ?? 0;
  }

  // 取得第一項折價項目
  OrderDiscount firstOrderDiscount(DiscountType value) {
    return data.firstOrderDiscount(value);
  }

  DateTime get checkoutAt {
    final dateTime = utcToLocal(data?.orderDiner?.checkoutAt);
    return dateTime;
  }

  // 包含 (可用 ItemType 取得)
  // 1. 額外費用 (order_items)
  // 2. 服務費 (order_items)
  // 不含
  // 1. 運費 (order_shipping)
  // 2. 金流手續費 (order_payments.info.payment_fee)
  // 3. 現場折價 (order_discount)
  Iterable<OrderItem> get allOrderItems sync* {
    // 包含子訂單的 normal items
    // 合併訂單的主訂單不會有 normal items
    yield* normalItems;
    // 主訂單的非 normal items (服務費、額外費用)
    yield* data.orderItemList
        .where((element) => element.type != ItemType.Normal.index);
  }

  // 所有含價格的物件
  // 包含:
  // 1. 運費 (order_shipping)
  // 2. 金流手續費 (order_payments.info.payment_fee)
  // 3. 現場減價 (order_discount)
  Iterable<OrderItem> get _allEveryItems sync* {
    // final ret = <OrderItem>[];
    yield* allOrderItems;
    // 運費
    final shippingFee = orderShippingFee;
    if (shippingFee is num && shippingFee > 0) {
      yield OrderItem(
        // productName: orderShipping.shippingName ?? '運費',
        productName: '運費',
        quantity: 1,
        // productSpec1: data.nnOrderShipping.displayShippingMethodId,
        finalPrice: shippingFee,
      );
    }
    // 金流手續費
    final paymentFee = orderPaymentFee;
    if (paymentFee is num && paymentFee > 0) {
      yield OrderItem(
        // productName: orderPayment.name ?? '金流手續費',
        // 強制變更為金流手續費
        productName: '金流手續費',
        quantity: 1,
        // productSpec1: orderPayment.displayPaymentMethodId,
        finalPrice: paymentFee,
      );
    }
    // 現場折價
    final discount = firstOrderDiscount(DiscountType.Discount);
    if (discount != null && discount.nnDiscountPriceAbs > 0) {
      yield OrderItem(
        productName: discount.displayName,
        finalPrice: discount.discountPrice,
      );
    }
    // 積點折抵
    final redeemMemberPoints = data.nnRedeemMemberPoints;
    if (redeemMemberPoints is num && redeemMemberPoints != 0) {
      yield OrderItem(
        productName: '積點折抵',
        finalPrice: -redeemMemberPoints.abs(),
      );
    }
    // 優惠券
    final couponDiscount = firstOrderDiscount(DiscountType.Coupon);
    if (couponDiscount != null && couponDiscount.discountDescription != null) {
      final coupon = couponDiscount.discountDescription.asCoupon();
      final hasTitle = coupon.title != null && coupon.title.isNotEmpty;
      yield OrderItem(
        productName: hasTitle ? coupon.title : '優惠券',
        productSpec1: coupon.displayType,
        finalPrice: couponDiscount.discountPrice ?? 0,
      );
    }
  }

  Receipt asReceipt({
    String storeName,
    String userName,
    DateTime printAt,
    num servicePercentage,
  }) {
    data.orderItems ??= <OrderItem>[];
    return Receipt(
      orderId: data.id,
      servicePercentage: servicePercentage ?? 0,
      storeName: storeName ?? '',
      printAt: printAt ?? DateTime.now(),
      userName: userName ?? '',
      type: data.type,
      source: data.orderDiner.source,
      status: data.status,
      orderNumber: data.orderNumber,
      checkoutAt: checkoutAt,
      invoiceNumber: data.orderInvoice?.number,
      invoiceStatus: data.orderInvoice?.status,
      vatNumber: data.orderInvoice?.vatNumber,
      npoBan: data.orderInvoice?.npoBan,
      carrierId: data.orderInvoice?.carrierId,
      mealAt: data.orderDiner.mealAt.localAt,
      createdAt: data.createdAt.localAt,
      sellerMemo: data.comment,
      buyerName: targetOrderAddress?.displayName ?? '',
      buyerPhone: targetOrderAddress?.displayPhone ?? '',
      buyerAddress: targetOrderAddress?.displayAddress ?? '',
      buyerMemo: data.orderDiner.memo,
      paid: data.paid,
      change: data.orderPaymentChange,
      // seat: data.orderDiner?.displayTable ?? '',
      seat: displayTable ?? '',
      total: data.total,
      subtotal: data.subtotal,
      discount: data.getDiscountPrice(DiscountType.Discount),
      otherFee: data.additionalCharges,
      serviceFee: data.serviceCharges,
      deliveryFee: data.orderShipping?.shippingFee ?? 0.0,
      paymentStatus: data.paymentStatus,
      // 付款方式
      // paymentName: data.orderPayment?.name ?? '',
      paymentName: data.displayOrderPayment,
      // 積點
      redeemMemberPoints: data.redeemMemberPoints,
      // 金流手續費
      paymentFee: data?.orderPaymentFee,
      // 訂單數
      billCount: orderCount,
      paymentMethodId: data?.orderPayment?.payMethodId ?? -1,
      items: _allEveryItems
          .map((e) => Item(
                itemName: e.productName,
                quantity: e.quantity,
                unitPrice: e.finalPrice,
                comment: e.productSpec1,
                type: e.type,
              ))
          .toList(),
    );
  }

  Invoice asInvoice() {
    // final brandsInfo = Get.find<PrefProvider>().brandsInfo;
    return Invoice(
      // storeName: brandsInfo.name,
      // printMark: printMark ?? 0,
      dateTime: checkoutAt,
      invoiceNumber: data.orderInvoice?.number ?? '',
      randomNumber: data.orderInvoice?.randomNumber ?? '',
      // totalAmount: data.total,
      // seller: taxId,
      buyer: data.orderInvoice?.vatNumber ?? '',
      // taxRate: taxRate,
      items: _allEveryItems.map((e) {
        return Item(
          itemName: e.productName,
          quantity: e.quantity,
          unitPrice: e.finalPrice,
          comment: e.productSpec1,
          productId: e.productId,
          type: e.type,
          // taxRate: taxRate,
        );
      }).toList(),
    );
  }

  bool get isNeedToPrint {
    final vatNumber = data.orderInvoice?.vatNumber ?? '';
    if (vatNumber.isNotEmpty) {
      // 有統編列印發票
      return true;
    }
    final carrierId = data.orderInvoice?.carrierId ?? '';
    if (carrierId.isNotEmpty) {
      // 有載具不列印發票
      return false;
    }
    final npoBan = data.orderInvoice?.npoBan ?? '';
    if (npoBan.isNotEmpty) {
      // 有愛心碼不列印發票
      return false;
    }
    return true;
  }

  PosInvoiceNewsReq asPosInvoiceNewsReq({
    String apiKey,
    String taxId,
    String posBan,
    String storeCode,
    String storeName,
  }) {
    return PosInvoiceNewsReq(
      posBan: posBan, // Pos 廠商統編
      apiKey: apiKey, // Pos 廠商 ApiKey
      sellerBan: taxId, // 賣方統編
      storeCode: storeCode, // 店別代碼 (一定要)
      storeName: storeName, // 相關號碼(店別名稱)
      registerCode: '', // 機號
      orderNo: data.orderNumber, // 交易序號
      state: 1, // 發票狀態 1:發票開立, 4:折讓開立
      invoiceNo: data.orderInvoice.number, // 發票號碼
      invoiceDate: checkoutAt.yMdHms, // 發票日期時間 (yyyy/MM/dd HH:mm:ss)
      // allowanceDate: '', // 折讓日期時間 (yyyy/MM/dd HH:mm:ss)
      buyerBan: data.orderInvoice.vatNumber, // 買方統編
      printMark: isNeedToPrint ? 'Y' : 'N', // 紙本電子發票已列印註記
      // memberId: '', // 會員代號
      // checkNo: '', // 檢查碼
      // invoiceType: '35', // 發票類型
      // groupMark: '', // 彙開註記
      // salesAmt: 0, // 應稅銷售額合計 (二聯含稅$3,780, 三聯未稅$3,600)(由子項目計算)
      // freeTaxSalesAmt: 0, // 免稅銷售額合計 (由子項目計算)
      zeroTaxSalesAmt: 0.0, // 零稅率銷售額合計
      // taxAmt: 0, // 營業稅額
      // totalAmt: data.total, // 總計 (由子項目計算)
      // 由品項決定稅別
      // taxType: '$taxType', // 課稅別/稅率別 1:應稅, 2:零稅率, 3:免稅, 9:混和應稅與免稅或零
      taxRate: TaxRate.TX.value, // 稅率 (買賣業請帶 0.05)
      discountAmt: 0.0, // 折扣金額
      healthyAmt: 0.0, // 健康捐
      carrierType:
          data.orderInvoice.carrierTypeForServer, // 載具類別號碼 共通性載具(手機條碼): 3J0002
      carrierId1: data.orderInvoice.carrierId, // 載具顯碼 Id1 /12345678
      carrierId2: data.orderInvoice.carrierId, // 載具顯碼 Id2 /12345678
      npoBan: data.orderInvoice.npoBan, // 發票捐贈對象=愛心碼
      randomNumber: data.orderInvoice.randomNumber, // 發票防偽碼 前端隨機產生 (0-9)
      // mainRemark: '', // 主檔備註，若交易是用信用卡, 需記載卡號末 4 碼
      invoiceDetails: _allEveryItems.map((e) => e.toInvoiceDetail()).toList(),
    );
  }

  // TODO: refactor
  // 從現有詳細訂單做一個可以 Put 用的結構 OrdersPostReq
  OrdersPostReq asOrdersPutReq({ProductProvider productProvider}) {
    final ordersPostReq = OrdersPostReq(
      memberId: data?.memberId,
      table1Id: data?.orderDiner?.table1Id,
      table2Id: data?.orderDiner?.table2Id,
      adult: data?.orderDiner?.adult ?? 0,
      child: data?.orderDiner?.child ?? 0,
      type: data?.type,
      source: data?.orderDiner?.source,
      subtotal: data?.subtotal ?? 0,
      fee: data?.serviceCharges ?? 0,
      discount: data?.getDiscountPrice(DiscountType.Discount) ?? 0,
      additionalCharges: data.additionalCharges,
      redeemMemberPoints: data?.redeemMemberPoints?.abs(),
      // pointDiscountLimit,
      // pointGet,
      // memberCouponId,
      // memberCouponDiscount,
      // memberCouponExtraPrice,
      total: data?.total ?? 0,
      paid: data?.orderPayment?.info?.paid ?? 0.0,
      change: data?.orderPayment?.info?.change ?? 0.0,
      paymentStatus: data?.paymentStatus,
      status: data?.status,
      multiplePayment: (data?.orderPayments ?? <OrderPayment>[])
          .map((e) => e.asMultiplePayment())
          .toList(),
      paymentMethodId:
          data?.orderPayment?.payMethodId ?? AppPayMethod.Cash.index,
      paymentFee: data?.orderPayment?.info?.paymentFee ?? 0.0,
      shippingMethodId: data?.orderShipping?.shippingMethodId,
      shippingFee: data?.orderShipping?.shippingFee ?? 0.0,
      // mealAt,
      comment: data?.comment ?? '',
      memo: data?.orderDiner?.memo ?? '',
      invoice: data?.orderInvoice != null,
      invoiceType: data?.invoiceType,
      // invoiceInfo,
      invoiceNumber: data?.orderInvoice?.number,
      randomNumber: data?.orderInvoice?.randomNumber,
      invoicePaper: data?.orderInvoice?.invoicePaper,
      vatNumber: data?.orderInvoice?.vatNumber ?? data?.invoiceInfo?.vatNumber,
      carrierType: data?.orderInvoice?.carrierType,
      carrierId: data?.orderInvoice?.carrierId ?? data?.invoiceInfo?.carrierId,
      npoBan: data?.orderInvoice?.npoBan,
      buyerName: data?.buyerAddress?.name ?? '',
      buyerPhone: data?.buyerAddress?.phone ?? '',
      buyerCityId: data?.buyerAddress?.cityId ?? -1,
      buyerCityareaId: data?.buyerAddress?.cityAreaId ?? -1,
      buyerAddress: data?.buyerAddress?.address ?? '',
      receiverName: data?.receiverAddress?.name ?? '',
      receiverPhone: data?.receiverAddress?.phone ?? '',
      receiverCityId: data?.receiverAddress?.cityId ?? 0,
      receiverCityareaId: data?.receiverAddress?.cityAreaId ?? 0,
      receiverAddress: data?.receiverAddress?.address ?? '',
      // NOTE: 必須使用序列化+反序列化，才不會互相影響資料。
      items: normalItems.map((e) => OrderItem.fromJson(e.toJson())).toList(),
    );
    ordersPostReq.restoreAdditionProducts(productProvider);

    // 複製會員 id
    // ordersPostReq.memberId = data.memberId;

    //這邊一堆不明狀況，我不知道怎麼把詳情做成一個可以送出的資料
    //??
    // ordersPostReq.memberId = data.memberId;
    // ordersPostReq.table1Id = data.orderDiner.table1Id;
    // ordersPostReq.table2Id = data.orderDiner.table2Id;
    // ordersPostReq.adult = data.orderDiner.adult;
    // ordersPostReq.child = data.orderDiner.child;
    // ordersPostReq.type = data.type;
    // ordersPostReq.source = data.orderDiner.source;
    // ordersPostReq.subtotal = data.subtotal;

    // ordersPostReq.fee = data.getFee('服務費');

    //WTF how do I do this?
    // ordersPostReq.discount = data.getDiscount(DiscountType.Discount);

    // ordersPostReq.additionalCharges = data.additionalCharges;
    // ordersPostReq.total = data.total;
    // if (data.redeemMemberPoints != null) {
    //   ordersPostReq.redeemMemberPoints = data.redeemMemberPoints.abs();
    // }

    // ordersPostReq.paymentStatus = data.paymentStatus;
    // ordersPostReq.status = data.status;

    //我不知道為什麼，總之 Server 只吃這樣
    if (data.orderDiner.mealAt != null) {
      DateTime mealAtDateTime =
          DateFormat('yyyy-MM-ddTHH:mm:ss').parseUTC(data.orderDiner.mealAt);
      // DateTime mealAtLocalDateTime = mealAtDateTime.toLocal();
      ordersPostReq.mealAt =
          DateFormat('yyyy-MM-dd HH:mm:ss').format(mealAtDateTime);
    }

    // ordersPostReq.comment = data.comment;
    // ordersPostReq.memo = data?.orderDiner?.memo ?? '';

    //Server 沒有告訴我的潛規則: 第一格是 buyer address
    // if (data.buyerAddress != null) {
    //   ordersPostReq.buyerName = data.buyerAddress?.name ?? '';
    //   ordersPostReq.buyerPhone = data.buyerAddress?.phone ?? '';
    //   ordersPostReq.buyerCityId = data.buyerAddress?.cityId ?? -1;
    //   ordersPostReq.buyerCityareaId = data.buyerAddress?.cityAreaId ?? -1;
    //   ordersPostReq.buyerAddress = data.buyerAddress?.address ?? '';
    // }

    //Server 沒有告訴我的潛規則: 第二格是 receiver address
    // if (data.receiverAddress != null) {
    //   ordersPostReq.receiverName = data.receiverAddress?.name ?? '';
    //   ordersPostReq.receiverPhone = data.receiverAddress?.phone ?? '';
    //   ordersPostReq.receiverCityId = data.receiverAddress?.cityId ?? 0;
    //   ordersPostReq.receiverCityareaId = data.receiverAddress?.cityAreaId ?? 0;
    //   ordersPostReq.receiverAddress = data.receiverAddress?.address ?? '';
    // }

    // ordersPostReq.items =
    //     data.orderItems.map((e) => OrderItem.clone(e)).toList();
    //List.from(data.orderItems.map((e) => OrderItem.clone(e)));

    // 特殊: 移除另外計算的欄位
    ordersPostReq.items ??= <OrderItem>[];
    ordersPostReq.items.removeWhere((element) {
      return element.productName == '服務費' || element.productName == '額外費用';
    });

    // print('ordersPostReq.items length: ' + ordersPostReq.items.length.toString());
    // 發票
    // data.invoiceType;
    // if (data.invoiceInfo != null) {
    //   ordersPostReq.vatNumber = data.invoiceInfo.vatNumber;
    //   ordersPostReq.carrierId = data.invoiceInfo.carrierId;
    // }

    // payment 訂單詳情上跟本沒有找零資料? 事實上是藏在 orderPayment Info 內
    // final orderPayment = data.orderPayment;
    // ordersPostReq.paid = orderPayment?.info?.paid ?? 0.0;
    // ordersPostReq.change = orderPayment?.info?.change ?? 0.0;
    // ordersPostReq.paymentMethodId =
    //     orderPayment?.payMethodId ?? AppPayMethod.Cash.index;
    // 金流手續費
    // ordersPostReq.paymentFee = orderPayment?.info?.paymentFee ?? 0.0;
    // 多重支付
    // if (data.orderPayments != null) {
    //   ordersPostReq.multiplePayment =
    //       data.orderPayments.map((e) => e.asMultiplePayment()).toList();
    //   // 餐飲，移除金額為 0 的項目
    //   if (data.orderType.isDinner) {
    //     ordersPostReq.multiplePayment
    //         .removeWhere((element) => (element.money ?? 0) <= 0);
    //   }
    // }
    // 餐飲，移除金額為 0 的項目
    if (data.orderType.isDinner) {
      ordersPostReq.multiplePayment
          .removeWhere((element) => (element.money ?? 0) <= 0);
    }
    // shipping
    // final orderShipping = data.orderShipping;
    // ordersPostReq.shippingMethodId = orderShipping?.shippingMethodId;
    // ordersPostReq.shippingFee = orderShipping?.shippingFee ?? 0.0;
    // 優惠券
    final couponDiscount = data.firstOrderDiscount(DiscountType.Coupon);
    if (couponDiscount != null && couponDiscount.discountDescription != null) {
      final coupon = couponDiscount.discountDescription.asCoupon();
      ordersPostReq.memberCouponId = coupon.id;
      // way1:
      // ordersPostReq.memberCouponDiscount =
      //     couponDiscount.discountDescription.discount ?? 0;
      ordersPostReq.memberCouponDiscount =
          (couponDiscount.discountPrice ?? 0).abs();
    }

    return ordersPostReq;
  }
}

extension ExtensionOrderInvoice on OrderInvoice {
  // 是否可列印發票
  bool get printable {
    // 開立狀態才可補印
    if (invoiceStatus.isInvoice) {
      // 例外: 有愛心碼，不可補印
      if (containsNpoBan) {
        return false;
      }
      return true;
    }
    return false;
  }

  bool get containsNpoBan => npoBan != null && npoBan.isNotEmpty;

  String get carrierTypeForServer {
    carrierId ??= '';
    return carrierId.isNotEmpty ? '3J0002' : '';
  }

  InvoiceStatus get invoiceStatus => status.invoiceStatus;

  String get displayStatus {
    if (invoiceStatus.isInvoice) {
      // 特殊: 開立狀態並且有愛心碼，顯示 '已捐贈'
      if (containsNpoBan) {
        return '已捐贈';
      }
    }
    return invoiceStatus?.name ?? '';
  }
}

extension ExtensionDioError on DioError {
  // e.error: "Http status error [401]";
  // e.message: "Http status error [401]"
  // e.statusMessage: "Unauthorized";
  // e.statusMessage: "OK";
  // e.statusCode: 401
  // e.statusCode: 200
  // e.response.statusCode: 401
  // e.response.statusMessage: "Unauthorized"
  // e.response.data(dynamic)
  // e.response.data: {
  //   "error": {
  //     "code": "0100",
  //     "message": "token非法 or 過期"
  //   }
  // }
  // 沒有連線時:
  // e.message: "SocketException: Failed host lookup: 'dev-api-diner-app.omos.tw' (OS Error: No address associated with hostname, errno = 7)"
  // e.response: null
  // e.error: SocketException(
  //  address: null,
  //  message: "Failed host lookup: 'dev-api-diner-app.omos.tw'"
  //  port: null,
  //  osError: OSError(
  //    errorCode: 7,
  //    message: "No address associated with hostname",
  //  )
  // )

  ResError get resError {
    if (error is SocketException) {
      return ResError(
        // message: "沒有網路連線\n${error.message}",
        message: '請先連結網路',
      );
    }
    if (response != null && response.data != null) {
      final data = Map.from(response.data);
      if (data.containsKey(Keys.Error.value)) {
        return ResError.fromJson(data[Keys.Error.value]);
      }
    }
    return ResError(
      message: message,
    );
  }

  String get responseMessage => resError.localMessage;
}

extension ExtensionJwt on Jwt {
  Duration get remainingTimeToRenew => renewDateTime.difference(DateTime.now());

  DateTime get renewDateTime => expirationDateTime.subtract(1.minutes);

  DateTime get expirationDateTime {
    final seconds = this.exp ?? 0;
    final duration = Duration(seconds: seconds);
    return DateTime.fromMillisecondsSinceEpoch(0).add(duration);
  }

  bool get isExpired => DateTime.now().isAfter(expirationDateTime);

  Duration get remainingTime => expirationDateTime.difference(DateTime.now());

  num get roleId => role?.id ?? 0;

  StoreRole get storeRole => roleId.storeRole;

  bool get isBoss => storeRole.isBoss;

  bool get isTemporary => Keys.Temporary == tokenType;

  String get dir => '$channelId.$clientId.$brandId';
}

extension ExtensionOrderDiner on OrderDiner {
  String get displayTable {
    table1Name ??= '';
    table2Name ??= '';
    return table1Name + table2Name;
  }

  String get displayPeople {
    adult ??= 0;
    child ??= 0;
    final a = adult > 0 ? '$adult大' : '';
    final c = child > 0 ? '$child小' : '';
    return a + c;
  }

  String get displayMealAt {
    if (mealAt != null && mealAt.isNotEmpty) {
      return mealAt.localAt.MMddHHmm;
    }
    return '';
  }

  Color get displayMealAtColor {
    if (mealAt == null) {
      return Colors.transparent;
    }
    return Colors.black;
  }

  // TODO: remove me
  String get displaySource {
    switch (source) {
      case 0:
        return '訂購人';
      case 1:
        return '收件人';
      default:
        return '';
    }
  }

  //內用區域桌號顯示字串
  String get partitionTableName => (table1Name ?? '') + (table2Name ?? '');
}

extension ExtensionOrderAddress on OrderAddress {
  void init() {
    address ??= '';
    city ??= '';
    cityId ??= 1;
    cityarea ??= '';
    cityAreaId ??= 1;
    name ??= '';
    phone ??= '';
    postcode ??= '';
    type ??= AddressType.Max.index;
  }

  AddressType get addressType => type?.addressType ?? AddressType.Max;

  String get displayName => name ?? '';

  String get displayPhone {
    if ('-' == phone) {
      return '';
    }
    return phone ?? '';
  }

  String get displayAddress {
    final a = postcode ?? '';
    final b = city ?? '';
    final c = cityarea ?? '';
    final d = address ?? '';
    return '$a$b$c$d';
  }
}

extension ExtensionOrderDetail on OrderDetail {
  // 是否已付款
  bool get isPaid => paymentStatus == PaymentStatus.Paid.index;

  // 運費資訊
  OrderShipping get nnOrderShipping {
    orderShipping ??= OrderShipping(
      shippingFee: 0,
      shippingId: 0,
      shippingMethodId: 0,
      shippingName: '',
    );
    return orderShipping;
  }

  // 運費
  num get orderShippingFee {
    nnOrderShipping.shippingFee ??= 0;
    return nnOrderShipping.shippingFee;
  }

  Iterable<OrderDiscount> get orderDiscountList {
    orderDiscount ??= <OrderDiscount>[];
    return orderDiscount;
  }

  Iterable<OrderDiscount> getOrderDiscountList(DiscountType value) {
    return orderDiscountList.where((element) => element.discountType == value);
  }

  // 積點折抵
  num get nnRedeemMemberPoints {
    redeemMemberPoints ??= 0;
    return redeemMemberPoints;
  }

  OrderAddress get targetOrderAddress {
    switch (orderType) {
      case OrderType.DinnerHere:
      case OrderType.DinnerToGo:
      case OrderType.RetailToGo:
      case OrderType.RetailInStore:
        return buyerAddress;
      case OrderType.DinnerDelivery:
      case OrderType.RetailDelivery:
        return receiverAddress;
      default:
        final ret = OrderAddress();
        ret.init();
        return ret;
    }
  }

  bool get containsPoints {
    return redeemMemberPoints is num && redeemMemberPoints.abs() > 0;
  }

  bool get containsCoupon {
    return orderDiscountList
        .any((element) => element.type == DiscountType.Coupon.index);
  }

  num get isPrint => orderDiner?.isPrint ?? Switcher.Off.index;

  InvoiceStatus get invoiceStatus {
    return orderInvoice?.invoiceStatus ?? InvoiceStatus.Max;
  }

  Sticker asStickerAt(num index) => asSticker(orderItems.elementAt(index));

  Sticker asSticker(OrderItem orderItem) {
    return Sticker(
      tagStyle: StickerStyle.Filled.index,
      headerTitleNum: '$orderSerialNumber',
      headerTitleBlockText: type.orderType.getNameWithSource(orderSource),
      headerTitleNote: partitionTableName,
      headerSubTitle: '時間: ' + createdAtStickerDisplayStr(),
      headerSubTitleNote: orderItem.nnFinalPrice.currencyStyle,
      bodyTitle: orderItem.productName,
      bodySubTitle: orderItem.productSpec1,
      // bodyContent: 'bodyContent',
    );
  }

  num get paid {
    if (orderPayments != null) {
      return orderPayments.fold<num>(0, (previousValue, element) {
        return previousValue + max(0, element.paid ?? 0);
      });
    }
    return 0;
  }

  OrderSource get orderSource {
    return orderDiner?.source?.orderSource ?? OrderSource.Max;
  }

  Iterable<OrderPayment> get orderPaymentList {
    orderPayments ??= <OrderPayment>[];
    return orderPayments;
  }

  // 金流手續費
  num get orderPaymentFee {
    // (舊) 從單一支付方式取得金流手續費
    // 為向下相容，故 server 保留此結構並傳送過來
    // if (orderPayment != null) {
    //   return orderPayment?.info?.paymentFee ?? 0.0;
    // }
    // (新) 從多重支付裡取得金流手續費
    // 第一個物件即單一支付的物件
    return orderPaymentList.fold<num>(0, (previousValue, element) {
      return previousValue + (element.paymentFee ?? 0);
    });
  }

  num get orderPaymentChange {
    return orderPaymentList.fold<num>(0, (previousValue, element) {
      return previousValue + max(0, element.change ?? 0);
    });
  }

  String get displayOrderPayment {
    // app 單
    if (orderSource.isApp) {
      // 未結帳，不顯示
      switch (paymentStatus.paymentStatus) {
        case PaymentStatus.Outstanding:
          return '';
        case PaymentStatus.Balance:
          return '';
        default:
          break;
      }
    }
    // 多重支付優先呈現
    if (orderPayments != null && orderPayments.isNotEmpty) {
      return orderPayments
          .where((element) {
            return element.name != null &&
                element.name.isNotEmpty &&
                element.paid != null &&
                element.paid > 0;
          })
          .map((e) => e.displayPayment)
          .join('+');
    }
    // 單一支付
    if (orderPayment != null &&
        orderPayment.name != null &&
        orderPayment.name.isNotEmpty) {
      return orderPayment.displayPayment;
    }
    return '';
  }

  // 判斷多重支付
  bool get isMutiplePayment =>
      orderPayments != null && orderPayments.length > 1;

  // 判斷單一支付
  bool get isSinglePayment =>
      orderPayments != null && orderPayments.length == 1;

  // 沒有任何支付方式
  bool get noMultiplePayment => orderPayments == null || orderPayments.isEmpty;

  bool containsPayment(AppPayMethod value) => indexOfPayment(value) >= 0;

  num indexOfPayment(AppPayMethod value) {
    if (orderPayments != null) {
      return orderPayments.indexWhere((element) {
        return element.payMethodId.appPayMethod == value;
      });
    }
    return -1;
  }

  String get displayTakeMethod {
    if (orderType.isDinner) {
      return '取餐方式：';
    }
    if (orderType.isRetail) {
      return '取貨方式：';
    }
    return '';
  }

  String get displayInvoiceStatus => orderInvoice?.displayStatus ?? '';

  OrderType get orderType => type?.orderType ?? OrderType.Max;

  StoreType get storeType => orderType.storeType;

  OrderAddress get buyerAddress {
    orderAddresses ??= <OrderAddress>[];
    return orderAddresses.firstWhere(
      (element) => element.addressType.isBuyer,
      orElse: () {
        final ret = OrderAddress();
        ret.init();
        return ret;
      },
    );
  }

  OrderAddress get receiverAddress {
    orderAddresses ??= <OrderAddress>[];
    return orderAddresses.firstWhere(
      (element) => element.addressType.isRecipient,
      orElse: () {
        final ret = OrderAddress();
        ret.init();
        return ret;
      },
    );
  }

  String get displayBuyerAddress => buyerAddress?.displayAddress ?? '';

  String get displayBuyerName => buyerAddress?.name ?? '';

  String get displayBuyerPhone {
    // 特殊: 建立訂單時購買人電話須有值，故填入代表 '無' 的符號
    final ret = buyerAddress?.phone ?? '';
    if ('-' == ret) {
      return '';
    }
    return ret;
  }

  String get displayReceiverName => receiverAddress?.name ?? '';

  String get displayReceiverPhone {
    // 特殊: 建立訂單時購買人電話須有值，故填入代表 '無' 的符號
    final ret = receiverAddress?.phone ?? '';
    if ('-' == ret) {
      return '';
    }
    return ret;
  }

  String get displayReceiverAddress {
    return receiverAddress?.displayAddress ?? '';
  }

  // flat: order source
  bool get isOrderFromLine {
    return orderDiner?.source?.orderSource?.isLine ?? false;
  }

  OrderStatus get orderStatus => status?.orderStatus ?? OrderStatus.Max;

  String get displayStatus => orderStatus.name;

  // 訂單日期
  String get displayCreatedAt {
    if (createdAt != null && createdAt.isNotEmpty) {
      return createdAt.localAt.MMddHHmm;
    }
    return '';
  }

  String get displayType {
    type ??= -1;
    final orderSource = orderDiner.source.orderSource;
    return type.orderType.getNameWithSource(orderSource);
  }

  Color get displayTypeColor {
    type ??= -1;
    final source = orderDiner?.source?.orderSource;
    return type.orderType.getColorWithSource(source ?? OrderSource.Max);
  }

  num get orderSerialNumber => num.tryParse(orderSerial) ?? 0;

  String get orderSerial {
    orderNumber ??= '';
    return orderNumber.takeLast(6).padLeft(6, '0');
  }

  Iterable<OrderItem> get orderItemList {
    orderItems ??= <OrderItem>[];
    return orderItems;
  }

  Iterable<OrderItem> _getOrderItemWithType(ItemType itemType) {
    return orderItemList.where((element) => element.type == itemType.index);
  }

  // 取得一般商品 (不含額外費用、服務費)
  Iterable<OrderItem> get normalItems => _getOrderItemWithType(ItemType.Normal);

  // 一般商品數量總和
  num get normalItemsCount {
    return normalItems.fold<num>(0, (previousValue, element) {
      return previousValue + element.nnQuantity;
    });
  }

  // 一般商品價錢總和
  num get normalItemsPrice {
    return normalItems.fold<num>(0, (previousValue, element) {
      return previousValue + element.stackPrice;
    });
  }

  // 服務費
  num get serviceCharges {
    final it = _getOrderItemWithType(ItemType.ServiceFee);
    return it.isNotEmpty ? it.first.finalPrice : 0;
  }

  // 額外費用
  num get additionalCharges {
    final it = _getOrderItemWithType(ItemType.Additional);
    return it.isNotEmpty ? it.first.finalPrice : 0;
  }

  // 取得第一個折價項目
  OrderDiscount firstOrderDiscount(DiscountType value) {
    return orderDiscountList.firstWhere(
      (element) => element.discountType == value,
      orElse: () => OrderDiscount(
        discountName: '',
        discountPrice: 0,
        type: DiscountType.Max.index,
      ),
    );
  }

  // 取得折價券折扣
  num get couponDiscount => getDiscountPrice(DiscountType.Coupon) ?? 0;

  // 取得折價價格
  num getDiscountPrice(DiscountType value) {
    return getOrderDiscountList(value).fold(0, (previousValue, element) {
      element.discountPrice ??= 0;
      return previousValue + element.discountPrice ?? 0;
    });
  }

  String get dinerMemo => orderDiner?.memo ?? '';

  // TODO: remove me
  String get displayMealType {
    if (isRetail) {
      return '取貨時間：';
    }
    type ??= -1;
    if (type == 0) {
      return '用餐時間：';
    }
    return '取餐時間：';
  }

  String get displayPaymentStatus => paymentStatus?.paymentStatus?.name ?? '';

  bool get isRetail {
    type ??= 0;
    return type.orderType.isRetail;
  }

  String get displayMealAt => orderDiner?.displayMealAt ?? '';

  Color get displayMealAtColor =>
      orderDiner?.displayMealAtColor ?? Colors.transparent;

  String get displayShippingFee {
    final value = orderShipping?.shippingFee ?? 0.0;
    return value.decimalStyle;
  }

  OrderSummary asOrderSummary() {
    return OrderSummary(
      adult: orderDiner?.adult ?? 0,
      child: orderDiner?.child ?? 0,
      createdAt: createdAt,
      id: id,
      isPrint: isPrint,
      itemCount: normalItemsCount,
      // masterId:
      mealAt: orderDiner?.mealAt,
      member: Member(
        // avatar: '',
        id: memberId,
        name: memberName,
      ),
      orderNumber: orderNumber,
      paymentStatus: paymentStatus,
      source: orderDiner?.source,
      status: status,
      table1Id: orderDiner?.table1Id,
      table1Name: orderDiner?.table1Name,
      table2Id: orderDiner?.table2Id,
      table2Name: orderDiner?.table2Name,
      total: total,
      type: type,
      usingCoupon: containsCoupon,
      usingPoints: containsPoints,
      updatedAt: updatedAt,
    );
  }
}

extension ExtensionOrderShipping on OrderShipping {
  // TODO: remove me
  String get displayShippingMethodId {
    switch (shippingMethodId) {
      case 1:
        return '自取';
      case 2:
        return '宅配：常溫';
      case 3:
        return '宅配：低溫';
      default:
        return '';
    }
  }
}

extension ExtensionOrderPayment on OrderPayment {
  num get paid => info?.paid ?? total ?? 0;

  num get change => info?.change ?? 0;

  num get paymentFee => info?.paymentFee ?? 0;

  String get other => info?.other ?? '';

  String get displayPayment {
    // return '$name$other(\$${paid.decimalStyle})';
    return '$name$other';
  }

  MultiplePayment asMultiplePayment() {
    return MultiplePayment(
      paymentMethodId: paymentMethodId,
      money: paid,
      other: other,
    );
  }
}

// FIXME: move to okshop_common
extension OrderSummaryX on OrderSummary {
  // 訂單狀態 enum
  OrderStatus get orderStatus => status.orderStatus;
  // 主訂單
  bool get isMaster => !isSlave;
  // 子訂單
  bool get isSlave => masterId is num && masterId > 0;
  // 合併結帳判斷
  bool get combineAvailable {
    // 規則: 零售不可合併結帳
    if (type.orderType.isRetail) {
      return false;
    }
    // 使用積點不可合併結帳，因為已先扣除，使用會重複扣點
    if (true == usingPoints) {
      return false;
    }
    return true;
  }

  num get orderSerialNumber => num.tryParse(orderSerial);

  String get orderSerial => orderNumber?.takeLast(6) ?? '000000';

  OrderType get orderType {
    type ??= -1;
    return type.orderType;
  }

  OrderSource get orderSource {
    source ??= OrderSource.App.index;
    return source.orderSource;
  }

  // flat: get type color
  Color get displayTypeColor => orderType.getColorWithSource(orderSource);

  // flat: get type name
  String get displayType => orderType.getNameWithSource(orderSource);

  // flat: order from line
  bool get orderFromLine => orderSource.isLine;

  // 訂單日期
  String get displayCreatedAt {
    if (createdAt != null && createdAt.isNotEmpty) {
      return createdAt.localAt.MMddHHmm;
    }
    return '';
  }

  DateTime get mealDateTimeAt => utcToLocal(mealAt);

  // 用餐時間
  String get displayMealAt {
    if (mealAt != null && mealAt.isNotEmpty) {
      return mealAt.localAt.MMddHHmm;
    }
    return '';
  }

  Color get displayMealAtColor {
    if (mealAt != null && mealAt.isNotEmpty) {
      return Colors.black;
    }
    return Colors.transparent;
  }

  ///
  /// 人數
  ///
  String get displayPeople {
    adult ??= 0;
    child ??= 0;
    final a = adult > 0 ? '$adult大' : '';
    final c = child > 0 ? '$child小' : '';
    return '$a$c';
  }

  // 桌號
  String get displayTable {
    table1Name ??= '';
    table2Name ??= '';
    return '$table1Name$table2Name';
  }

  //Get the create at date time.
  DateTime createAtLocalDateTime() {
    if (createdAt != null) {
      DateTime utcDateTime = DateTime.parse(createdAt);
      return DateTime.utc(
          utcDateTime.year,
          utcDateTime.month,
          utcDateTime.day,
          utcDateTime.hour,
          utcDateTime.minute,
          utcDateTime.second,
          utcDateTime.millisecond,
          utcDateTime.microsecond);
    }
    return null;
  }

  //Meal at local date time.
  DateTime mealAtLocalDateTime() {
    if (mealAt != null) {
      DateTime utcDateTime = DateTime.parse(mealAt);
      return DateTime.utc(
          utcDateTime.year,
          utcDateTime.month,
          utcDateTime.day,
          utcDateTime.hour,
          utcDateTime.minute,
          utcDateTime.second,
          utcDateTime.millisecond,
          utcDateTime.microsecond);
    }
    return null;
  }

  //是否在某時間內用餐
  bool isMealWithin(OpenHoursDateTime openHoursDateTime) {
    DateTime useDateTime = mealAtLocalDateTime();
    if (useDateTime != null) {
      return openHoursDateTime.contains(useDateTime);
    } else {
      return false;
    }
  }
}

extension ExtensionStatelessWidget on Widget {
  Future<bool> initializeController() {
    final completer = Completer<bool>();

    /// Callback called after widget has been fully built
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      completer.complete(true);
    });

    return completer.future;
  }

  Widget center() {
    return Center(
      child: this,
    );
  }

  Widget align({
    Alignment alignment,
  }) {
    return Align(
      alignment: alignment ?? Alignment.center,
      child: this,
    );
  }

  Widget expanded() => Expanded(child: this);

  Widget sizedBox({double width, double height}) =>
      SizedBox(width: width, height: height, child: this);

  Future<T> dialog<T>({
    EdgeInsets insetPadding,
    bool barrierDismissible,
  }) {
    return Get.dialog<T>(
      Dialog(
        insetPadding: insetPadding ?? EdgeInsets.zero,
        backgroundColor: Colors.transparent,
        elevation: 0.0,
        child: SizedBox(
          width: 300.dw,
          child: this,
        ),
      ),
      barrierDismissible: barrierDismissible ?? true,
    );
  }

  Future<T> sheet<T>({
    final bool header,
    final bool isDismissible,
    final bool enableDrag,
    final bool isScrollControlled,
    final bool ignoreSafeArea,
  }) {
    return Get.bottomSheet<T>(
      this,
      isScrollControlled: isScrollControlled ?? true,
      enableDrag: enableDrag ?? false,
      ignoreSafeArea: ignoreSafeArea ?? false,
      isDismissible: isDismissible ?? true,
      enterBottomSheetDuration: 100.milliseconds,
      exitBottomSheetDuration: 100.milliseconds,
    );
  }
}

extension ExtensionStoreAccount on StoreAccount {
  String get displayLastLogin {
    if (lastLogin != null && lastLogin.isNotEmpty) {
      return lastLogin.localAt.yMdHms;
    }
    return '';
  }

  StoreAccountPut asStoreAccountPut([String password]) {
    return StoreAccountPut(
      roleId: role.id,
      name: name,
      password: password,
      status: status,
      comment: comment,
    );
  }

  StoreAccountPost asStoreAccountPost([String password]) {
    return StoreAccountPost(
      roleId: role.id,
      username: username,
      name: name,
      password: password,
      status: status,
      comment: comment,
    );
  }
}

extension ExtensionPackageInfo on PackageInfo {
  String get displayVersion => '$version ($buildNumber)';
}

extension ExtensionSettingGetRes on SettingGetRes {
  int get lineOrderDineIn {
    return data?.other?.lineOrder?.dineIn ?? 0;
  }

  set lineOrderDineIn(int value) {
    data?.other?.lineOrder?.dineIn = value;
  }

  bool get rejectWhenClosed {
    return data?.other?.auto?.close != 0 ?? false;
  }

  set rejectWhenClosed(bool value) {
    data?.other?.auto?.close = value ? 1 : 0;
  }

  int get closeMin {
    return data?.other?.auto?.closeMin ?? 0;
  }

  set closeMin(int value) {
    data?.other?.auto?.closeMin = value;
  }

  BrandsType get checkoutType {
    final last = BrandsType.values.length - 1;
    final index = data?.other?.checkoutType ?? last;
    return BrandsType.values.elementAt(index);
  }

  set checkoutType(BrandsType value) {
    data?.other?.checkoutType = value.index;
  }

  Switcher get printOrder {
    return data.other?.printOrder?.switcher ?? Switcher.Off.index;
  }

  set printOrder(Switcher value) {
    data.other?.printOrder = value.index;
  }
}

extension ExtensionResError on ResError {
  String get localMessage {
    final messageProvider = Get.find<MessageProvider>();
    final msg = messageProvider[code] ?? message;
    // if (code != null && code.isNotEmpty) {
    //   return "$code $msg";
    // }
    if (messageDetail != null && messageDetail.isNotEmpty) {
      return '$msg\n\n$messageDetail';
    }
    return msg;
  }
}

// 2021/09/13 12:00:00
final _dateTimeFormatFull = DateFormat('yyyy/MM/dd HH:mm:ss');
// 09/13 12:00
// final _dateTimeFormatOrder = DateFormat.Md('zh_TW').add_Hm();
final _dateTimeFormatOrder = DateFormat("M-d HH:mm");
// 2021-9-13
final _dateTimeFormatRevenue = DateFormat("y-M-d");
// 10月26日 週二
final _dateTimeFormatWeek = DateFormat.MMMEd('zh_TW');

extension ExtensionDateTime on DateTime {
  // 09-13 12:00
  String get MMddHHmm => _dateTimeFormatOrder.format(this);
  // 2021/9/14 16:00:15
  String get yMdHms => _dateTimeFormatFull.format(this);
  // 2021-9-3
  String get yMd => _dateTimeFormatRevenue.format(this);
  // 10月26日 週二
  String get MMMEd => _dateTimeFormatWeek.format(this);
}

extension ExtensionSettingWeekDay on SettingWeekDay {
  bool inRange(TimeOfDay timeOfDay) {
    if (1 == status) {
      return hours.any((element) => element.inRange(timeOfDay));
    }
    return false;
  }
}

extension ExtensionSettingOpenHour on SettingOpenHour {
  bool inRange(TimeOfDay timeOfDay) {
    final begin = openTimeOfDay.millisecondsSinceEpoch;
    final end = closeTimeOfDay.millisecondsSinceEpoch;
    final current = timeOfDay.millisecondsSinceEpoch;
    return begin <= current && current <= end;
  }

  DateTime get openDateTime => DateFormat('HH:mm').parse(open);

  DateTime get closeDateTime {
    // return DateFormat('HH:mm').parse(close);
    if (close == '24:00') {
      return DateFormat('HH:mm').parse('23:59');
    }
    return DateFormat('HH:mm').parse(close);
  }

  TimeOfDay get openTimeOfDay => TimeOfDay.fromDateTime(openDateTime);

  TimeOfDay get closeTimeOfDay => TimeOfDay.fromDateTime(closeDateTime);
}

extension ExtensionTimeOfDay on TimeOfDay {
  int get millisecondsSinceEpoch => (hour * 60 + minute) * 60 * 1000;

  String get display {
    final hh = '0$hour'.takeLast(2);
    final mm = '0$minute'.takeLast(2);
    return '$hh:$mm';
  }

  DateTime get withToday {
    final now = DateTime.now();
    return DateTime(
      now.year,
      now.month,
      now.day,
      hour,
      minute,
    );
  }

  DateTime withDate(DateTime date) {
    return DateTime(
      date.year,
      date.month,
      date.day,
      hour,
      minute,
    );
  }
}

extension ExtensionBrandsInfo on BrandsInfo {
  Map<String, Object> get _other {
    if (other != null && other.isNotEmpty) {
      final jsonString = other.replaceAll(RegExp('OrderedMap'), '');
      return jsonDecode(jsonString);
    }
    return <String, Object>{};
  }

  Map<String, num> get googleMap => _other['google-map'];

  String get website => _other['website'];

  String get shoppingWebsite => _other['shopping_website'];

  String get fb => _other['fb'];

  String get ig => _other['ig'];

  String get line => _other['line'];

  String get google => _other['google'];

  BrandsPut asBrandsPut() {
    return BrandsPut(
      type: type,
      name: name,
      taxId: taxId,
      lineName: lineName,
      lineId: lineId,
      lineChannelId: lineChannelId,
      lineSecretCode: lineSecretCode,
      lineAccessToken: lineAccessToken,
      lineLiffId: lineLiffId,
    );
  }
}

extension ExtensionProductInfo on ProductInfo {
  // 轉為排序結構
  ProductCategoriesPut asProductCategoriesPut() {
    return ProductCategoriesPut(
      kind: kind,
      categoryId: categoryId,
      productId: productId,
      sort: sort,
    );
  }

  // product id 可能重複，使用 product id + category id 取 hash
  num get id => '$categoryId.$productId'.hashCode;

  bool get hasStock {
    switch (kind.productKind) {
      case ProductKind.DinnerApp: // 餐飲店內
      case ProductKind.DinnerLine: // 餐飲線上
        return stock != null && stock > 0;
      case ProductKind.Retail: // 零售
        return isAvailable ?? false;
      default:
        return false;
    }
  }

  // 針對餐飲及零售，取得是否售完的值根據不同的參數
  bool get isSoldOut => !hasStock;

  set isSoldOut(bool value) {
    switch (kind.productKind) {
      case ProductKind.DinnerApp: // 餐飲店內
      case ProductKind.DinnerLine: // 餐飲線上
        stock = (!value).switcher.index;
        break;
      case ProductKind.Retail: // 零售
        isAvailable = !value;
        break;
      default:
    }
  }
}

extension ExtensionPaymentBank on PaymentBank {
  PaymentBankPut toPaymentBankPut() {
    final json = <String, dynamic>{};
    json.addAll(toJson());
    json.addAll(setting.toJson());
    return PaymentBankPut.fromJson(json);
  }
}

// extension ExtensionRx<T> on Rx<T> {
//   Widget obx(
//     NotifierBuilder<T> widget, {
//     Widget Function(Object error) onError,
//     Widget onLoading,
//     // Widget onEmpty,
//   }) {
//     return StreamBuilder<T>(
//       stream: stream,
//       builder: (context, snapshot) {
//         if (snapshot.hasError) {
//           return onError != null
//               ? onError(snapshot.error)
//               : Center(child: Text('An error occurred: ${snapshot.error}'));
//         }
//         if (snapshot.hasData) {
//           return widget(snapshot.data);
//         }
//         return onLoading ?? const Center(child: CircularProgressIndicator());
//       },
//     );
//   }
// }

extension ExtensionBrandsInvoiceReq on BrandsInvoiceReq {
  String get displayTaxType {
    taxType ??= TaxType.None.index;
    switch (taxType.taxType) {
      case TaxType.TX:
        return '應稅';
      case TaxType.Free:
        return '免稅';
      case TaxType.Mix:
        return '混合稅率';
      default:
        return '稅率未定義';
    }
  }

  bool get guiEnabled {
    guiException ??= Switcher.Off.index;
    return guiException.switcher.isOn;
  }

  set guiEnabled(bool value) => guiException = value ? 1 : 0;

  bool get statusEnabled {
    status ??= Switcher.Off.index;
    return status.switcher.isOn;
  }

  set statusEnabled(bool value) => status = value.switcher.index;
}

extension ExtensionBrandsInvoice on BrandsInvoice {
  bool get invoiceEnabled {
    status ??= Switcher.Off.index;
    return status.switcher.isOn;
  }

  bool get invoiceSwitchEnabled {
    guiException ??= Switcher.Off.index;
    return guiException.switcher.isOn;
  }

  // TODO: remove me
  bool get isMixTaxType {
    return 3 == taxType;
  }

  // TODO: remove me
  num get taxTypeForServer {
    switch (taxType) {
      case 1: // 應稅
        return kTaxTypeNormal;
      case 2: // 免稅
        return kTaxTypeFree;
      case 3: // 混合稅率
        return kTaxTypeMix;
      default:
        return -1;
    }
  }
}

extension ExtensionOrderItem on OrderItem {
  void restoreAdditionProducts(ProductProvider productProvider) {
    productSpec1Ids ??= <num>[];
    additionProducts =
        productProvider.getAdditionProductFromIds(productSpec1Ids).toList();
  }

  void init() {
    finalPrice ??= 0;
    id ??= 0;
    isVip ??= Switcher.Off.index;
    productCategoryIds ??= <num>[];
    productName ??= '';
    productSpec1 ??= '';
    productSpec1Ids ??= <num>[];
    productTaxType = TaxType.None.index;
    quantity ??= 1;
    type ??= ItemType.Max.index;
  }

  ///
  /// TODO: rename asInvoiceDetail
  /// 只處理含稅價，在別處計算稅額
  ///
  InvoiceDetail toInvoiceDetail() {
    finalPrice ??= 0.0; // 單價 (含稅)
    quantity ??= 1; // 數量
    // 折價處理
    num discountAmt = 0.0;
    if (finalPrice < 0.0) {
      discountAmt = finalPrice;
      finalPrice = 0.0;
    }
    final totalAmt = finalPrice * quantity; // 總價 (含稅)

    return InvoiceDetail(
      // sequenceNo: id?.toString() ?? null,
      sequenceNo: null,
      // 品項序號
      itemName: productName ?? '',
      // 品名
      qty: quantity,
      // 數量
      // unit: '', // 單位
      // unitPrice: finalPrice.round(), // 單價
      // salesAmt: 0, // 銷售額 (三聯:  3,600, 二聯: 3,780)
      // taxAmt: 0, // 稅額 (三聯: $180, 二聯: 0)
      totalAmt: totalAmt.roundToDouble(),
      // 金額(含稅)
      discountAmt: discountAmt,
      // 折扣
      healthAmt: 0.0,
      // 健康捐
      relateNumber: productId?.toString() ?? null, // 相關號碼 (紀錄產品序號)
      // remark: '', //備註
    );
  }

  // 最後價格
  num get nnFinalPrice {
    finalPrice ??= 0;
    return finalPrice;
  }

  // 服務費為空值
  num get nnQuantity {
    quantity ??= 1;
    return quantity;
  }

  // 單價 x 數量
  num get stackPrice => nnFinalPrice * nnQuantity;
  // 設定結算金額 (單件)
  // 因區分 一般價 或 vip 價，產品價格由外部傳入，在加上額外價
  void calFinalPrice(num productPrice) {
    additionProducts ??= [];
    finalPrice =
        additionProducts.fold(productPrice ?? 0, (previousValue, element) {
      return previousValue + (element.price ?? 0);
    });
  }
}

extension ExtensionOrdersPostReq on OrdersPostReq {
  void refreshPointGet(PrefProvider prefProvider) {
    pointGet = _calculatePointGet(prefProvider);
  }

  // 由總價計算獲得點數
  num _calculatePointGet(PrefProvider prefProvider) {
    // 取得設定
    final sp = prefProvider.settingPoint.firstWhere(
      (element) => element.storeType == storeType,
      orElse: () => SettingPoint(),
    );
    if (sp.isAvailable && memberId is num && memberId > 0) {
      // 無條件捨去
      final ret = (total / sp.cashRatio).floor();
      logger.d('[OrdersSumUpController] pointGet($ret)');
      return ret;
    }
    return 0;
  }

  void restoreAdditionProducts(ProductProvider productProvider) {
    items ??= <OrderItem>[];
    for (var item in items) {
      item.restoreAdditionProducts(productProvider);
    }
  }

  OrdersCombiningPostReq asOrdersCombiningPostReq(Iterable<num> orderIds) {
    final ret = OrdersCombiningPostReq.fromJson(toJson());
    ret.orderIds = List<num>.from(orderIds);
    return ret;
  }

  bool validateReedit() {
    return true;
  }

  bool validate() {
    // 檢查總價
    total ??= 0;
    if (total < 0) {
      throw '商品總價不可為負值';
    }
    // 實收檢查
    validatePaid();
    // 積點檢查
    validatePoint();
    // 統編檢查
    validateTaxId();
    // 載具檢查
    validateCarrier();
    // 積點檢查
    return true;
  }

  bool validatePoint() {
    // final current = max(0, controller.member?.points ?? 0);
    // final usage = controller.draft?.redeemMemberPoints ?? 0;
    // if (usage > current) {
    //   throw '會員積點不足';
    // }
    return true;
  }

  bool validatePaid() {
    paid ??= 0;
    paid = paid.round();
    total ??= 0;
    total = total.round();
    if (paid > 0 && paid < total) {
      throw '實收金額不足';
    }
    return true;
  }

  bool validateTaxId() {
    if (vatNumber == null || vatNumber.isEmpty) {
      return true;
    }
    carrierId ??= '';
    vatNumber ??= '';
    if (vatNumber.isNotEmpty && carrierId.isNotEmpty) {
      throw '統一編號及載具/愛心碼不能同時輸入';
    }
    if (!Utils.isTaxId(vatNumber)) {
      throw '統一編號錯誤';
    }
    return true;
  }

  bool validateCarrier() {
    // 不填入資料合法
    if (carrierId == null || carrierId.isEmpty) {
      return true;
    }
    carrierId ??= '';
    vatNumber ??= '';
    if (vatNumber.isNotEmpty && carrierId.isNotEmpty) {
      throw '統一編號及載具/愛心碼不能同時輸入';
    }
    final isFormat = Utils.isCarrierId(carrierId) || Utils.isNpoBan(carrierId);
    if (!isFormat) {
      throw '載具/愛心碼 格式錯誤';
    }
    return true;
  }

  num get nnFee {
    fee ??= 0;
    return fee;
  }

  bool get modifiable {
    status ??= OrderStatus.Max.index;
    return status.orderStatus.isActive;
  }

  // TODO: try remove me.
  //重新計算總價以及找零
  //加上服務費、現場折價、額外費用、運費、金流手續費
  //編輯送出前必須要做一次重算
  void recalculate() {
    //總金額 = subtotal - fee - discount + additionalCharges
    additionalCharges ??= 0.0;
    discount ??= 0.0;
    shippingFee ??= 0.0;
    paymentFee ??= 0.0;
    redeemMemberPoints ??= 0.0;
    memberCouponDiscount ??= 0.0;
    memberCouponExtraPrice ??= 0.0;
    total = subtotal +
        nnFee +
        discount +
        additionalCharges +
        shippingFee +
        paymentFee +
        memberCouponExtraPrice +
        (-memberCouponDiscount) +
        (-redeemMemberPoints);
    // 找零不可有負數
    change = max(0, paid - total);
  }

  OrderSource get orderSource {
    source ??= OrderSource.Max.index;
    return source.orderSource;
  }

  num get nnDiscountAbs {
    discount ??= 0;
    return discount.abs();
  }

  num _calServiceFeeFromSetting(SettingOrderFee setting) {
    // 內用、現場點餐才計算服務費
    if ([OrderType.DinnerHere, OrderType.DinnerOrder].contains(orderType)) {
      return setting?.calServiceFee(normalItemsPrice, nnDiscountAbs) ?? 0;
    }
    return 0;
  }

  // flat method
  ShippingMethod get shippingMethod {
    shippingMethodId ??= ShippingMethod.None.index;
    return shippingMethodId.shippingMethod;
  }

  // 從產品運送方式取得訂單運送方式
  ShippingMethod getShippingMethodFromShippingType(ShippingType shippingType) {
    if (orderType.isRetailToGo) {
      shippingMethodId = ShippingMethod.PickUp.index;
      return shippingMethod;
    }
    if (orderType.isRetailDelivery) {
      if (shippingType.isCold) {
        shippingMethodId = ShippingMethod.Cold.index;
      } else {
        shippingMethodId = ShippingMethod.Amb.index;
      }
      return shippingMethod;
    }
    shippingMethodId = ShippingMethod.None.index;
    return shippingMethod;
  }

  // 計算運費 (傳入運費及條件)
  num _calShippingFee(Amb amb) {
    // 零售宅配才計算運費
    final condition = [OrderType.RetailDelivery];
    if (condition.contains(orderType)) {
      // 未達低消
      final needShippingFee = normalItemsPrice < (amb?.shippingFree ?? 0);
      if (true == needShippingFee) {
        // 低溫或常溫運費由外部傳入
        return amb?.shippingFee ?? 0;
      }
    }
    return 0;
  }

  num get nnShippingFee {
    shippingFee ??= 0;
    return shippingFee;
  }

  Iterable<OrderItem> get normalItems {
    items ??= <OrderItem>[];
    return items.where((element) {
      return element.type?.itemType?.isNormal ?? false;
    });
  }

  num get normalItemsCount {
    return normalItems.fold<num>(0, (previousValue, element) {
      return previousValue + element.nnQuantity;
    });
  }

  num get normalItemsPrice {
    subtotal = normalItems.fold<num>(0, (previousValue, element) {
      return previousValue + element.stackPrice;
    });
    return subtotal;
  }

  OrderType get orderType {
    type ??= OrderType.Max.index;
    return type.orderType;
  }

  StoreType get storeType => orderType.storeType;

  ///
  /// 填入資料 (server's rule, must have something)
  ///
  void ensureData() {
    if (buyerName == null || buyerName.isEmpty) {
      buyerName = '現場消費者';
    }
    if (buyerPhone == null || buyerPhone.isEmpty) {
      buyerPhone = '-';
    }
    if (receiverName == null || receiverName.isEmpty) {
      receiverName = '-';
    }
    if (receiverPhone == null || receiverPhone.isEmpty) {
      receiverPhone = '-';
    }
    if (receiverCityId == null || receiverCityId == 0) {
      receiverCityId = 1;
    }
    if (receiverCityareaId == null || receiverCityareaId == 0) {
      receiverCityareaId = 1;
    }
    if (receiverAddress == null || receiverAddress.isEmpty) {
      receiverAddress = '-';
    }
    if (ShippingMethod.None.index == shippingMethodId) {
      shippingMethodId = null;
    }
  }

  ///
  /// 計算優惠券加價減價
  ///
  void _updateWithCoupon(final Coupon coupon) {
    memberCouponExtraPrice = null;
    memberCouponDiscount = null;

    // 低消判斷
    if (coupon != null) {
      coupon.minPrice ??= 0;
      // 小計 (items 的和)
      subtotal = normalItemsPrice;
      if (subtotal >= coupon.minPrice) {
        memberCouponDiscount = coupon.getMemberDiscount(subtotal);
        memberCouponExtraPrice = coupon.memberCouponExtraPrice ?? 0;
      }
    }
  }

  List<OrderItem> getItems() {
    items ??= <OrderItem>[];
    return items;
  }

  num getTotal({
    Coupon coupon,
    num serviceFee,
    // 計算服務費需要的參數，由外部傳入
    SettingOrderFee settingOrderFee,
    // 計算運費需要的參數，由外部傳入
    Amb amb,
  }) {
    // 計算優惠券費用
    _updateWithCoupon(coupon);
    // 計算服務費，不可為負數
    fee = serviceFee ?? max(0, _calServiceFeeFromSetting(settingOrderFee));
    fee = fee.round();
    // 計算運費
    shippingFee = _calShippingFee(amb).round();
    // 計算總價
    total = normalItemsPrice + // 小計
        // 額外費用
        (additionalCharges ?? 0) +
        // 現場減價
        (0 - (discount ?? 0).abs()) +
        // 優惠券加價
        (memberCouponExtraPrice ?? 0) +
        // 優惠券折價
        (0 - (memberCouponDiscount ?? 0).abs()) +
        // 運費
        shippingFee +
        // 金流費 (LINE 訂單才有)
        (paymentFee ?? 0) +
        // 服務費: 由外部計算
        nnFee +
        // 使用點數減價
        (0 - (redeemMemberPoints ?? 0).abs());
    total = total.round();
    // 找零不可有負數
    change = max(0, (paid ?? 0) - total);
    return total;
  }

  // 判斷多重支付
  bool get isMutiplePayment {
    return multiplePayment != null && multiplePayment.length > 1;
  }

  // 判斷單一支付
  bool get isSinglePayment {
    return multiplePayment != null && multiplePayment.length == 1;
  }

  // 沒有任何支付方式
  bool get noMultiplePayment {
    return multiplePayment == null || multiplePayment.isEmpty;
  }

  SettingPay getSettingPay(AppPayMethod value) {
    final prefProvider = Get.find<PrefProvider>();
    final settingPayList = prefProvider?.settingPay;
    if (settingPayList != null) {
      // 搜尋 setting pay, 由 pay method id 取得 id
      return settingPayList.firstWhere(
        (element) => value == element.payMethodId?.appPayMethod,
        orElse: () => null,
      );
    }
    return null;
  }

  num indexOfPayment(AppPayMethod value) {
    final settingPay = getSettingPay(value);
    if (settingPay != null && multiplePayment != null) {
      return multiplePayment
          .indexWhere((element) => element.paymentMethodId == settingPay.id);
    }
    return -1;
  }

  ///
  /// 特殊: 重構多重支付方式
  /// 目前 paid 為總支付金額，需更正為現金支付金額
  /// server 規則:
  /// 收到的現金(Paid)由 總價+找零 計算出來
  /// paid: 190 (現金實收) = 140 (商品總價) + 60 (找零) - 10 (信用卡支付)
  ///
  void refactorMultiplePayment() {
    multiplePayment ??= [];
    var index = indexOfPayment(AppPayMethod.Cash);
    if (index >= 0) {
      // 有現金，移至第一個
      multiplePayment.insert(0, multiplePayment.removeAt(index));
    } else {
      // 沒有現金，新增一個
      final settingPay = getSettingPay(AppPayMethod.Cash);
      if (settingPay != null) {
        final money = multiplePayment.isEmpty ? paid : 0;
        multiplePayment.insert(
          0,
          MultiplePayment(
            paymentMethodId: settingPay.id,
            money: money,
            other: '',
          ),
        );
      }
    }
    index = indexOfPayment(AppPayMethod.Cash);
    if (index >= 0) {
      final settingPay = getSettingPay(AppPayMethod.Cash);
      final others = multiplePayment.fold(0, (previousValue, element) {
        if (element.paymentMethodId == settingPay.id) {
          // 排除現金
          return previousValue;
        }
        return previousValue + element.money;
      });
      // 取得現金支付
      final data = multiplePayment.elementAt(index);
      // 特殊: 現金實收，不寫在 MultiplePayment.money
      paid = data.money;
      // 特殊: 要讓所有支付(排除現金)相加等於總價，所以價差填入現金 money
      data.money = total - others;
    }
  }

  // TODO: rename asOrdersOrderIdInvoicePostReq
  OrdersOrderIdInvoicePostReq toOrdersOrderIdInvoicePostReq() {
    return OrdersOrderIdInvoicePostReq(
      invoiceNumber: invoiceNumber,
      randomNumber: randomNumber,
      invoicePaper: isNeedToPrint,
      vatNumber: vatNumber,
      carrierType: carrierType,
      carrierId: carrierId,
      npoBan: npoBan,
    );
  }

  bool get hasVatNumber {
    vatNumber ??= '';
    return vatNumber.isNotEmpty;
  }

  bool get isNeedToPrint {
    if (hasVatNumber) {
      // 有統編列印發票
      return true;
    }
    carrierId ??= '';
    if (carrierId.isNotEmpty) {
      // 有載具不列印發票
      return false;
    }
    npoBan ??= '';
    if (npoBan.isNotEmpty) {
      // 有愛心碼不列印發票
      return false;
    }
    return true;
  }

  // TODO: remove me, use normalItemsPrice instead
  num get itemsTotal {
    items ??= <OrderItem>[];
    return items.fold<num>(0, (previousValue, element) {
      return previousValue + element.stackPrice;
    });
  }

  // TODO: remove me, use normalItemsCount instead.
  num get itemsCount {
    items ??= <OrderItem>[];
    return items.fold<num>(0, (previousValue, element) {
      return previousValue + (element.quantity ?? 0);
    });
  }
}

extension ExtensionBrandsBasic on BrandsBasic {
  CustomUrl getCustomUrlAt(num index) {
    customUrl ??= <CustomUrl>[];
    while (customUrl.length < 3) {
      customUrl.add(CustomUrl(
        name: '',
        url: '',
      ));
    }
    return customUrl.elementAt(index);
  }

  Iterable<District> get districts {
    final prefProvider = Get.find<PrefProvider>();
    return prefProvider.getDistricts(cityId);
  }

  BrandsBasic asPut() {
    final ret = BrandsBasic.fromJson(toJson());
    ret.address ??= '';
    ret.phone ??= '';
    ret.taxName ??= '';
    return ret;
  }
}

extension ExtensionProductSingle on ProductSingle {
  void init() {
    createdAt ??= '';
    id ??= 0;
    price ??= 0;
    stock ??= 0;
    isAvailable ??= Switcher.Off.index;
    stockUnlimited ??= Switcher.On.index;
    shippingType ??= ShippingType.Max.index;
    isHidden ??= Switcher.Off.index;
    taxType ??= TaxType.TX.index; // 預設應稅
    isVip ??= Switcher.Off.index;
    vipPrice ??= price; // 預設等同一般價格
    summary ??= '';
    title ??= '';
    updatedAt ??= '';
    productCategories ??= <ProductCategory>[];
    productAdditionCategories ??= <ProductAdditionCategory>[];
    productImages ??= <ProductsImage>[];
  }

  //轉換成一個可以編輯的 ProductsPostReq (給 Product Editing Page 編輯送出用)
  //其實是轉成一個簡化版本的 request modal 物件
  ProductsPostReq generateAReqModal(int kind) {
    return ProductsPostReq(
      isVip: isVip ?? 0,
      vipPrice: vipPrice ?? 0,
      kind: kind,
      title: title,
      summary: summary,
      price: price,
      stock: stock,
      isHidden: isHidden,
      taxType: taxType,
      isAvailable: isAvailable,
      stockUnlimited: stockUnlimited,
      shippingType: shippingType,
      categories: productCategories == null
          ? []
          : productCategories.map((e) => e.categoryId).toList(),
      additionCategories: productAdditionCategories == null
          ? []
          : productAdditionCategories
              .map((e) => e.generateAReqModal())
              .toList(),
    );
  }

  TaxType get taxTypeEnum {
    taxType ??= TaxType.TX.index;
    return taxType.taxType;
  }

  List<ProductAdditionCategory> get nnProductAdditionCategories {
    productAdditionCategories ??= <ProductAdditionCategory>[];
    return productAdditionCategories;
  }
}

extension ExtensionProductAdditionCategory on ProductAdditionCategory {
  //轉換成一個可以編輯的 AdditionCategorySetting (給 Product Editing Page 編輯送出用)
  //其實是轉成一個簡化版本的 request modal 物件
  AdditionCategorySetting generateAReqModal() {
    return AdditionCategorySetting(
      additionCategoryId: additionCategoryId,
      title: title,
      required: this.required,
      option: option,
      optionMin: optionMin,
      optionMax: optionMax,
    );
  }
}

extension ExtensionBrandsNews on BrandsNews {
  BrandsNewsPut asBrandsNewsPut() {
    return BrandsNewsPut(news: news);
  }
}

extension ExtensionOrderDiscount on OrderDiscount {
  DiscountType get discountType => type.discountType;

  String get displayName {
    return '現場減價'; // TODO: 修改 server name 才是正確的方式
    if (discountName != null && discountName.isNotEmpty) {
      return discountName;
    }
    return discountType.name;
  }

  num get nnDiscountPriceAbs {
    discountPrice ??= 0;
    return discountPrice.abs();
  }
}

extension ExtensionMultiplePayment on MultiplePayment {
  String get displayMoney {
    if (money != null && money > 0) {
      return money.decimalStyle;
    }
    return '';
  }
}

extension ExtensionTextEditingController on TextEditingController {
  void selectAll() {
    selection = TextSelection(
      baseOffset: 0,
      extentOffset: text.length,
    );
  }
}

extension ExtensionOrdersCombiningPostReq on OrdersCombiningPostReq {
  num indexOfPayment(AppPayMethod value) {
    final settingPay = getSettingPay(value);
    if (settingPay != null && multiplePayment != null) {
      return multiplePayment
          .indexWhere((element) => element.paymentMethodId == settingPay.id);
    }
    return -1;
  }

  SettingPay getSettingPay(AppPayMethod value) {
    final prefProvider = Get.find<PrefProvider>();
    final settingPayList = prefProvider?.settingPay;
    if (settingPayList != null) {
      // 搜尋 setting pay, 由 pay method id 取得 id
      return settingPayList.firstWhere(
        (element) => value == element.payMethodId?.appPayMethod,
        orElse: () => null,
      );
    }
    return null;
  }

  ///
  /// 特殊: 重構多重支付方式
  /// 目前 paid 為總支付金額，需更正為現金支付金額
  ///
  void refactorMultiplePayment() {
    multiplePayment ??= [];
    var index = indexOfPayment(AppPayMethod.Cash);
    if (index >= 0) {
      // 有現金，移至第一個
      multiplePayment.insert(0, multiplePayment.removeAt(index));
    } else {
      // 沒有現金，新增一個
      final settingPay = getSettingPay(AppPayMethod.Cash);
      if (settingPay != null) {
        final money = multiplePayment.isEmpty ? paid : 0;
        multiplePayment.insert(
          0,
          MultiplePayment(
            paymentMethodId: settingPay.id,
            money: money,
            other: '',
          ),
        );
      }
    }
    index = indexOfPayment(AppPayMethod.Cash);
    if (index >= 0) {
      final settingPay = getSettingPay(AppPayMethod.Cash);
      final others = multiplePayment.fold(0, (previousValue, element) {
        if (element.paymentMethodId == settingPay.id) {
          // 排除現金
          return previousValue;
        }
        return previousValue + element.money;
      });
      // 取得現金支付
      final data = multiplePayment.elementAt(index);
      // 特殊: 現金實收，不寫在 MultiplePayment.money
      paid = data.money;
      // 特殊: 要讓所有支付(排除現金)相加等於總價，所以價差填入現金 money
      data.money = total - others;
    }
  }
}

extension ExtensionReportsStatements on ReportsStatements {
  num get invoiceVoidQuantity =>
      (app?.invoiceVoidQuantity ?? 0) + (online?.invoiceVoidQuantity ?? 0);
  num get invoiceVoidAmount =>
      (app?.invoiceVoidAmount ?? 0) + (online?.invoiceVoidAmount ?? 0);
  num get invoiceAmount =>
      (app?.invoiceAmount ?? 0) + (online?.invoiceAmount ?? 0);
  num get invoiceQuantity =>
      (app?.invoiceQuantity ?? 0) + (online?.invoiceQuantity ?? 0);
  num get refundQuantity =>
      (app?.refundQuantity ?? 0) + (online?.refundQuantity ?? 0);
  num get refundAmount =>
      (app?.refundAmount ?? 0) + (online?.refundAmount ?? 0);
  num get orderAmount => (app?.orderAmount ?? 0) + (online?.orderAmount ?? 0);
  num get orderTax => (app?.orderTax ?? 0) + (online?.orderTax ?? 0);
  num get orderTotal => (app?.orderTotal ?? 0) + (online?.orderTotal ?? 0);
  num get extraCharges =>
      (app?.extraCharges ?? 0) + (online?.extraCharges ?? 0);
  num get onSiteDiscount =>
      (app?.onSiteDiscount ?? 0) + (online?.onSiteDiscount ?? 0);

  Iterable<String> get invoiceVoidNumber {
    final ls = <String>[];
    ls.addAll(app?.invoiceVoidNumber ?? <String>[]);
    ls.addAll(online?.invoiceVoidNumber ?? <String>[]);
    return ls;
  }

  Iterable<RefundOrder> get refundOrders {
    final ls = <RefundOrder>[];
    ls.addAll(app?.refundOrders ?? <RefundOrder>[]);
    ls.addAll(online?.refundOrders ?? <RefundOrder>[]);
    return ls;
  }

  Iterable<Payment> get payment {
    final ls = <Payment>[];
    ls.addAll(app?.payment ?? <Payment>[]);
    ls.addAll(online?.payment ?? <Payment>[]);
    final keys = ls.fold<Set<String>>(Set<String>(), (previousValue, element) {
      previousValue.add(element.channelPayMethodName);
      return previousValue;
    });
    return keys.fold<List<Payment>>(<Payment>[], (previousValue, key) {
      final it = ls.where((element) => element.channelPayMethodName == key);
      final payment = it.fold<Payment>(
        Payment(
          channelPayMethodName: key,
          income: 0,
          expenses: 0,
        ),
        (previousValue, element) {
          previousValue.expenses += element.expenses ?? 0;
          previousValue.income += element.income ?? 0;
          return previousValue;
        },
      );
      previousValue.add(payment);
      return previousValue;
    });
  }
}

extension ExtensionQrFormat on QrFormat {
  bool get isMember {
    return 'member' == type && memberId != null && memberId > 0;
  }

  bool get isOrder {
    return 'order' == type && orderId != null && orderId > 0;
  }

  bool get isCoupon {
    return false;
    return 'coupon' == type;
  }

  bool get isCoupon1 {
    return false;
    return 'cooperation_coupon' == type;
  }

  bool get isCoupon2 {
    return false;
    return 'questionnaire_coupon' == type;
  }

  bool get isQuest {
    return false;
    return 'questionnaire' == type;
  }
}

extension ExtensionMemberProvider on MemberProvider {
  Future<num> pickerDialog() {
    final completer = Completer<num>();
    final selected = RxNum(0);
    DialogGeneral(
      DialogArgs(
        header: DialogGeneral.titleText('選擇會員'),
        content: MemberPicker(
          selected: selected,
          data: getMembersFromLocalStorage(),
        ),
        mainButtonText: '確定',
        secondaryButtonText: '取消',
        onMainButtonPress: () {
          if (selected.value > 0) {
            completer.complete(selected.value);
          }
        },
      ),
    ).dialog();
    return completer.future;
  }
}

extension ExtensionDiscountDescription on DiscountDescription {
  Coupon asCoupon() => Coupon.fromRawJson(toRawJson());
}

extension ExtensionShippingDelivery on ShippingDelivery {
  Amb getAmb(ShippingMethod shippingMethod) {
    // 自取
    if (shippingMethod.isPickUp) {
      return Amb(
        status: Switcher.Off.index,
        shippingFee: 0,
        shippingFree: 0,
        description: '',
      );
    }
    // 低溫
    if (shippingMethod.isCold) {
      if (cold != null && cold.status == Switcher.On.index) {
        return cold;
      }
    }
    if (amb != null && amb.status == Switcher.On.index) {
      return amb;
    }
    return Amb(
      status: Switcher.Off.index,
      shippingFee: 0,
      shippingFree: 0,
      description: '',
    );
  }

  // 低溫運費開始檢查，其中一項開啟就成立
  bool get enabled => Switcher.On.index == getAmb(ShippingMethod.Cold).status;
}

extension SettingLabelX on SettingLabel {
  void pushTask(List<int> task) {
    final printerProvider = Get.find<PrinterProvider>();
    if (printerProvider != null) {
      printerProvider.pushTask(uuid, task);
    }
  }

  GodexPrinter asGodexPrinter() {
    return GodexPrinter(
      name: name,
      ip: host,
      port: '$port',
      macAddress: macAddress,
    );
  }

  Future<void> flushQueue(List<PrinterTask> queue) async {
    // 目前只有 lan 印表機可使用
    if (PrinterType.all.contains(type)) {
      await _flushEsc(queue);
    } else {
      await _flushGodex(queue);
    }
  }

  ///
  /// 檢查印表機連線狀態
  ///
  Future<bool> validate() async {
    return true;
    // Socket s;
    // try {
    //   s = await Socket.connect(
    //     host,
    //     port ?? 9100,
    //     timeout: 400.milliseconds,
    //   );
    //   return s != null;
    // } finally {
    //   s?.destroy();
    //   await 400.milliseconds.delay();
    // }
  }

  Future<void> _flushEsc(Iterable<PrinterTask> tasks) async {
    if (await validate() == true) {
      // sunmi
      final bytes = tasks.fold<List<int>>(<int>[], (previousValue, element) {
        previousValue += element.bytes;
        return previousValue;
      });
      try {
        tasks.forEach((task) => task.status = TaskStatus.Processing.index);
        final ret = await rawBytes(bytes);
        final status = ret == PosPrintResult.success
            ? TaskStatus.Completed
            : TaskStatus.Pending;
        // 失敗: 重印
        tasks.forEach((task) => task.status = status.index);
      } catch (e) {
        // 例外: 重印
        tasks.forEach((element) => element.status = TaskStatus.Pending.index);
        rethrow;
      }
    }
  }

  Future<void> _flushGodex(List<PrinterTask> tasks) async {
    if (await validate() == true) {
      final godex = asGodexPrinter();
      await godex.execute(() async {
        for (var task in tasks) {
          try {
            task.status = TaskStatus.Processing.index;
            final ret = await godex.putImage(task.bytes);
            final status =
                ret == true ? TaskStatus.Completed : TaskStatus.Pending;
            task.status = status.index;
          } catch (e) {
            logger.e('[_flushGodex] godex.execute:$e');
            // 失敗: 標記為待處理
            task.status = TaskStatus.Pending.index;
            rethrow;
          } finally {
            // 印完後等待
            await 600.milliseconds.delay();
          }
        }
      });
    }
  }
}

extension ExtensionSettingGetData on SettingGetData {
  SettingOrderFee get settingOrderFee {
    other ??= SettingOther();
    other.fee ??= SettingOrderFee();
    other.fee.type ??= ServiceFeeType.None.index;
    other.fee.percent ??= 0;
    return other.fee;
  }
}

extension ExtensionSettingOrderFee on SettingOrderFee {
  num calServiceFee(num price, [num discount]) {
    price ??= 0;
    discount ??= 0;
    switch (serviceFeeType ?? ServiceFeeType.None) {
      case ServiceFeeType.Origin:
        return max(0, price * floatPercent);
      case ServiceFeeType.Discount:
        return max(0, (price - discount) * floatPercent);
      default:
        return 0;
    }
  }

  ServiceFeeType get serviceFeeType {
    type ??= ServiceFeeType.None.index;
    return type.serviceFeeType;
  }

  num get floatPercent {
    percent ??= 0;
    return percent * 0.01;
  }
}

extension ExtensionSettingPoint on SettingPoint {
  StoreType get storeType {
    type ??= StoreType.Max.index;
    return type.storeType;
  }

  bool get isAvailable {
    if (isRewardPoints == null || isRewardPoints.switcher.isOff) {
      return false;
    }
    if (cashRatio == null || cashRatio <= 0) {
      return false;
    }
    return true;
  }
}

extension ExtensionProductsPostReq on ProductsPostReq {
  String get initialVipPrice {
    vipPrice = (vipPrice ?? 0).round();
    return vipPrice > 0 ? '$vipPrice' : '';
  }

  String get initialPrice {
    price = (price ?? 0).round();
    return price > 0 ? '$price' : '';
  }

  bool validate() {
    if (title == null || title.isEmpty) {
      throw '名稱為必填項目';
    }
    if (categories == null || categories.isEmpty) {
      throw '必須選擇一個分類';
    }
    return true;
  }

  // 嘗試新增一個規格
  bool addNewAdditionCategory(num additionCategoryId, String defaultTitle) {
    final doesNotExist = !containsAdditionCategory(additionCategoryId);
    additionCategories ??= [];
    additionCategories.addIf(
      doesNotExist,
      AdditionCategorySetting(
        additionCategoryId: additionCategoryId,
        title: defaultTitle,
      ),
    );
    return doesNotExist;
  }

  // 嘗試移除一個規格
  bool removeAdditionCategory(num additionCategoryId) {
    additionCategories ??= [];
    final index = additionCategories.indexWhere(
        (element) => element.additionCategoryId == additionCategoryId);
    if (index >= 0) {
      additionCategories.removeAt(index);
      return true;
    }
    return false;
  }

  // 是否已有某個規格
  bool containsAdditionCategory(num additionCategoryId) {
    additionCategories ??= [];
    return additionCategories
        .any((element) => element.additionCategoryId == additionCategoryId);
  }

  // 取得一個規格的必選狀態
  bool isAdditionCategoryRequired(num additionCategoryId) {
    final element = tryGetAdditionCategorySetting(additionCategoryId);
    element.required ??= 0;
    return element.required > 0;
  }

  // 嘗試設定一個規格為必選
  bool setAdditionCategoryRequired(num additionCategoryId, bool b) {
    final element = tryGetAdditionCategorySetting(additionCategoryId);
    element.required = b.switcher.index;
    return element.isValid;
  }

  // 嘗試取得一個規格設定資料
  AdditionCategorySetting tryGetAdditionCategorySetting(
      num additionCategoryId) {
    additionCategories ??= [];
    final index = additionCategories.indexWhere(
        (element) => element.additionCategoryId == additionCategoryId);
    if (index >= 0) {
      return additionCategories.elementAt(index);
    }
    return AdditionCategorySetting();
  }

  // 移除資料中已經不存在的 categories (確保資料正確性用)
  void cleanUpNonExistCategories(Iterable<num> categoryIds) {
    categories ??= <int>[];
    final uniqueIds = categories.toSet();
    uniqueIds.removeWhere((element) => !categoryIds.contains(element));
    categories = uniqueIds.toList();
  }

  // 移除資料中不存在的 assition categories (確保資料正確性用)
  void cleanUpNonExistAdditionCategories(Iterable<Category> exists) {
    additionCategories ??= [];
    additionCategories.removeWhere((element) {
      return exists.every((e) => e.id != element.additionCategoryId);
    });
  }
}

extension ExtensionAdditionCategorySetting on AdditionCategorySetting {
  bool get isValid => additionCategoryId is num && additionCategoryId > 0;
}

extension ExtensionPaymentInstore on PaymentInstore {
  LinePaySetting get linePaySetting {
    setting ??= '{}';
    return LinePaySetting.fromRawJson(setting);
  }
}

extension LocalSettingsX on LocalSettings {
  num get nnPrintInvoice {
    printInvoice ??= Switcher.On.index;
    return printInvoice;
  }

  num get nnPrintReceipt {
    printReceipt ??= Switcher.Off.index;
    return printReceipt;
  }

  num get nnPrintReceiptCount {
    printReceiptCount ??= 1;
    return printReceiptCount;
  }

  num get nnPrintItemReceipt {
    printItemReceipt ??= Switcher.Off.index;
    return printItemReceipt;
  }

  num get nnPrintItemReceiptCount {
    printItemReceiptCount ??= 1;
    return printItemReceiptCount;
  }

  AutoPrint getAutoPrint(StoreType type) {
    return autoPrint.firstWhere((element) {
      return element.type == type.index;
    }, orElse: () {
      if (type.isRetail) {
        final res = AutoPrint(
          type: StoreType.Retail.index,
          enabled: Switcher.Off.index,
          receipt: Switcher.Off.index,
          sticker: Switcher.Off.index,
        );
        autoPrint.add(res);
        return res;
      } else {
        final res = AutoPrint(
          type: StoreType.Dinner.index,
          enabled: autoPrintReceipt ?? Switcher.Off.index,
          receipt: autoPrintReceipt ?? Switcher.Off.index,
          sticker: autoPrintSticker ?? Switcher.Off.index,
        );
        autoPrint.add(res);
        return res;
      }
    });
  }
}

extension ExtensionGetStorage on GetStorage {
  Stream<String> watch([String key]) async* {
    final streamController = StreamController<String>();
    Function disposeListen;
    if (key != null && key.isNotEmpty) {
      disposeListen = listenKey(key, (e) {
        streamController.add(key);
      });
    } else {
      disposeListen = listen(() {
        streamController.add('');
      });
    }
    yield* streamController.stream;
    logger.d('[ExtensionGetStorage] watch finally');
    disposeListen?.call();
    streamController.close();
  }
}

extension MemberX on Member {
  bool match(String value) {
    if (value == null || value.isEmpty) {
      return true;
    }
    final regexp = RegExp(value, caseSensitive: false);
    if (regexp.hasMatch(memo ?? '')) {
      return true;
    }
    if (regexp.hasMatch(mobilePhone ?? '')) {
      return true;
    }
    if (regexp.hasMatch(name ?? '')) {
      return true;
    }
    if (regexp.hasMatch(nicknameStore ?? '')) {
      return true;
    }
    return false;
  }
}

extension MemberReqX on MemberReq {
  num get offset => (nnPage - 1) * limit;
  num get length => offset + limit;
  num get nnPage => page ?? 1;
  num get nextPage => nnPage + 1;
  num get nnLimit => limit ?? kLimit;
  bool get nnHasMore => hasMore ?? false;
  bool validate(Member member) {
    if (keyword != null && keyword.isNotEmpty) {
      // 搜尋關鍵字
      if (!member.match(keyword)) {
        return false;
      }
    }
    return true;
  }

  Iterable<Condition<Member>> _orAny() sync* {
    if (keyword != null && keyword.isNotEmpty) {
      yield Member_.mobilePhone.endsWith(keyword);
      yield Member_.memo.contains(keyword);
      yield Member_.name.contains(keyword);
      yield Member_.nicknameStore.contains(keyword);
    }
  }

  Condition<Member> toQueryCondition() {
    final it = _orAny();
    if (it.isNotEmpty) {
      return it.reduce((value, element) => value.or(element));
    }
    return null;
  }
}

extension OrderReqX on OrderReq {
  num get offset => (nnPage - 1) * limit;
  num get length => offset + limit;
  num get nnPage => page ?? 1;
  num get nextPage => nnPage + 1;
  num get nnLimit => limit ?? kLimit;

  bool validate(OrderSummary order) {
    // status，有內容才檢查
    if (status != null && status.isNotEmpty) {
      if (!status.contains(order.status)) {
        return false;
      }
    }
    // source，有內容才檢查
    if (source != null) {
      if (source != order.source) {
        return false;
      }
    }
    // table1，有內容才檢查
    if (table1Id is num && table1Id > 0) {
      if (table1Id != order.table1Id) {
        return false;
      }
      // table2，有內容才檢查
      if (table2Id is num && table2Id > 0) {
        if (table2Id != order.table2Id) {
          return false;
        }
      }
    }
    // order number，有內容才檢查
    if (keyword != null && keyword.isNotEmpty) {
      // 搜尋訂單編號
      if (!order.orderNumber.endsWith(keyword)) {
        return false;
      }
    }
    // name，搜尋會員有內容才檢查
    if (name != null && name.isNotEmpty) {
      final regexp = RegExp(name, caseSensitive: false);
      if (!regexp.hasMatch(order.member?.name ?? '')) {
        return false;
      }
    }
    // mobile phone，有內容才檢查
    if (mobilePhone != null && mobilePhone.isNotEmpty) {
      final memberMobilePhone = order.member?.mobilePhone ?? '';
      if (!memberMobilePhone.endsWith(mobilePhone)) {
        return false;
      }
    }
    return true;
  }

  Condition<OrderSummary> toQueryCondition() {
    final it = _andAll();
    if (it.isNotEmpty) {
      return it.reduce((value, element) => value.and(element));
    }
    return null;
  }

  Iterable<Condition<OrderSummary>> _andAll() sync* {
    // status，有內容才檢查
    if (status != null && status.isNotEmpty) {
      yield OrderSummary_.status.oneOf(List.castFrom<num, int>(status));
    }
    // source，有內容才檢查
    if (source != null) {
      yield OrderSummary_.source.equals(source);
    }
    // type，有內容才檢查
    if (type != null && type.isNotEmpty) {
      yield OrderSummary_.type.oneOf(List.castFrom<num, int>(type));
    }
    // table1，有內容才檢查
    if (table1Id is num && table1Id > 0) {
      yield OrderSummary_.table1Id.equals(table1Id);
      // table2，有內容才檢查
      if (table2Id is num && table2Id > 0) {
        yield OrderSummary_.table2Id.equals(table2Id);
      }
    }
    // is print，有內容才檢查
    if (isPrint is num) {
      yield OrderSummary_.isPrint.equals(isPrint);
    }
    // order number，有內容才檢查
    if (keyword != null && keyword.isNotEmpty) {
      // 搜尋訂單編號
      yield OrderSummary_.orderNumber.endsWith(keyword);
    }
    // order number
    if (orderNumber != null && orderNumber.isNotEmpty) {
      yield OrderSummary_.orderNumber.endsWith(orderNumber);
    }
  }

  Condition<Member> toMemberQueryCondition() {
    final it = _orAny();
    if (it.isNotEmpty) {
      return it.reduce((value, element) => value.or(element));
    }
    return null;
  }

  Iterable<Condition<Member>> _orAny() sync* {
    if (memberId is num && memberId > 0) {
      yield Member_.id.equals(memberId);
    }
    // name，搜尋會員有內容才檢查
    if (name != null && name.isNotEmpty) {
      yield Member_.name.contains(name);
      yield Member_.nicknameStore.contains(name);
    }
    // mobile phone，有內容才檢查
    if (mobilePhone != null && mobilePhone.isNotEmpty) {
      yield Member_.mobilePhone.endsWith(mobilePhone);
    }
  }
}

extension OrderSelectorFilterX on OrderSelectorFilter {
  bool validate(OrderSummary order) {
    // 比對 id
    if (id != null) {
      if (id == order.id) {
        return true;
      }
    }
    // 已結帳單不可合併
    if ([PaymentStatus.Paid].contains(order.paymentStatus?.paymentStatus)) {
      return false;
    }
    // 還沒被接的訂單不可被處理
    if (![OrderStatus.Accepted].contains(order.status?.orderStatus)) {
      return false;
    }
    // 排除 source (app, line) 不同的訂單，不同來源訂單不可一起結
    // if (sourceOrKind != null) {
    //   if (sourceOrKind != order.source) {
    //     return false;
    //   }
    // }
    // 訂單末三碼
    if (last3Digits != null && last3Digits.isNotEmpty) {
      if (!(order.orderNumber ?? '').endsWith(last3Digits)) {
        return false;
      }
    }
    // 最後比對 table
    if (table1Id == null) {
      // 沒有桌號，不列入
      return false;
    }
    if (table1Id != null) {
      if (table1Id != order.table1Id) {
        return false;
      }
      if (table2Id != null) {
        if (table2Id != order.table2Id) {
          return false;
        }
      }
    }
    return true;
  }
}

extension NumX on num {
  double get dh => (toDouble() * Constants.designHeightRatio).h;
  double get dw => (toDouble() * Constants.designWidthRatio).w;
  double get dsp => sp;
  String get twoDigits => '$this'.padLeft(2, '0');
}

extension GetInterfaceX on GetInterface {
  Future<T> showLoading<T>() {
    return Progressing(
      message: '請稍候...',
    ).dialog();
  }

  Future<T> showAlert<T>(String content) {
    return DialogGeneral.alert(content).dialog<T>();
  }

  Future<Button> showConfirm(
    String title,
    String content, {
    String textConfirm,
    String textCancel,
    VoidCallback onConfirm,
    VoidCallback onCancel,
  }) {
    return DialogGeneral.quest(
      content,
      mainButtonText: textConfirm ?? Button.Positive.text,
      secondaryButtonText: textCancel ?? Button.Negative.text,
      onMainButtonPressed:
          onConfirm ?? () => Get.back<Button>(result: Button.Positive),
      onSecondaryButtonPress:
          onCancel ?? () => Get.back<Button>(result: Button.Negative),
    ).dialog<Button>(
      barrierDismissible: true,
    );
    // return defaultDialog<Button>(
    //   title: title,
    //   content: Text(content),
    //   textConfirm: textConfirm ?? Button.Positive.text,
    //   textCancel: textCancel ?? Button.Negative.text,
    //   onConfirm: onConfirm ?? () => Get.back<Button>(result: Button.Positive),
    //   onCancel: onCancel ?? () => Get.back<Button>(result: Button.Negative),
    // );
  }
}

extension IterableX<T extends Widget> on Iterable<T> {
  Widget column({
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.min,
  }) {
    return Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: toList(growable: false),
    );
  }

  Widget row({
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.min,
  }) {
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: toList(growable: false),
    );
  }
}

extension PrinterTaskX on PrinterTask {
  bool get isPending => status == TaskStatus.Pending.index;
}

extension InvoiceDataX on InvoiceData {
  DateTime get dateTime => DateTime.fromMillisecondsSinceEpoch(date ?? 0);
  // 發票期別 ex: 10404
  String get invoicePeriod {
    final month = dateTime.month;
    final year = dateTime.year;
    final period = month % 2 == 0 ? month : month + 1;
    // 如果是個位數則補零
    return '$year${period.twoDigits}';
  }

  bool verify({String seller, DateTime date}) {
    if (seller != this.seller) {
      return false;
    }
    // 檢查發票期別是否相同
    final currentPeriod = _getInvoicePeriod(date);
    if (invoicePeriod != currentPeriod) {
      return false;
    }
    return true;
  }

  String _getInvoicePeriod(DateTime date) {
    final month = date.month;
    final year = date.year;
    final period = month % 2 == 0 ? month : month + 1;
    return '$year${period.twoDigits}';
  }
}
