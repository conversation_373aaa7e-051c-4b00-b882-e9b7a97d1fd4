import 'package:flutter_test/flutter_test.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mockito/annotations.dart';
import 'package:muyipork/app/data/models/other/brands_invoice.dart';
import 'package:muyipork/app/data/models/other/local_settings.dart';
import 'package:muyipork/app/providers/box_provider.dart';
import 'package:muyipork/app/providers/pref_provider.dart';
import 'package:muyipork/constants.dart';
import 'package:okshop_common/okshop_common.dart';
import 'package:package_info/package_info.dart';

class MyPrefProvider extends PrefProvider {
  final BrandsInvoice _brandsInvoice;
  final LocalSettings _localSettings;

  MyPrefProvider({
    BrandsInvoice brandsInvoice,
    LocalSettings localSettings,
  })  : _brandsInvoice = brandsInvoice,
        _localSettings = localSettings;

  BrandsInvoice get brandsInvoice => _brandsInvoice;
  LocalSettings get localSettings => _localSettings;
}

@GenerateMocks([
  BoxProvider,
  PackageInfo
], customMocks: <MockSpec<dynamic>>[
  MockSpec<GetStorage>(
    as: #MockGetStorage,
    fallbackGenerators: {
      #getKeys: mShim,
      #getValues: mShim,
    },
  ),
])

// root function for const function
T mShim<T>(T a, int b) {
  if (a is int) return 1 as T;
  throw 'unknown';
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUp(() {
    logger.d('setUp - 1');
  });

  // group('InvoiceData', () {
  //   // test pop invoice data
  //   test('pop invoice data', () {
  //     final mockBoxProvider = MockBoxProvider();
  //     final prefProvider = PrefProvider(
  //       boxProvider: mockBoxProvider,
  //       packageInfo: MockPackageInfo(),
  //     );
  //     final mockGetStorage = MockGetStorage();
  //     when(mockBoxProvider.getGsBox(any)).thenReturn(mockGetStorage);
  //     when(mockGetStorage.hasData(any)).thenReturn(true);
  //     when(mockGetStorage.remove(any)).thenAnswer((_) async {});
  //     when(mockGetStorage.read(kKeyInvoiceData)).thenReturn({
  //       'seller': '83193989',
  //       'date': 0,
  //       'number': 'AA12345678',
  //     });
  //     final invoiceData = prefProvider.popInvoiceData();
  //     expect(invoiceData.seller, '83193989');
  //   });

  //   // test push invoice data
  //   test('push invoice data', () {
  //     final mockBoxProvider = MockBoxProvider();
  //     final prefProvider = PrefProvider(
  //       boxProvider: mockBoxProvider,
  //       packageInfo: MockPackageInfo(),
  //     );
  //     final mockGetStorage = MockGetStorage();
  //     when(mockBoxProvider.getGsBox(any)).thenReturn(mockGetStorage);
  //     when(mockGetStorage.write(any, any)).thenAnswer((_) async {});
  //     final invoiceData = InvoiceData(
  //       seller: '83193989',
  //       date: 0,
  //       number: 'AA12345678',
  //     );
  //     prefProvider.pushInvoiceData(invoiceData);
  //     verify(mockGetStorage.write(kKeyInvoiceData, invoiceData.toJson()));
  //   });
  // });

  // test('test with mock', () {
  //   final prefProvider = PrefProvider(
  //     boxProvider: MockBoxProvider(),
  //     packageInfo: MockPackageInfo(),
  //   );

  //   when(prefProvider.userDefault.get(kKeyBrandsInvoice, defaultValue: '{}'))
  //       .thenAnswer((_) {
  //     return BrandsInvoice(
  //       guiException: Switcher.On.index,
  //       status: Switcher.Off.index,
  //     ).toRawJson();
  //   });

  //   when(prefProvider.userDefault.get(Keys.LocalSettings, defaultValue: '{}'))
  //       .thenAnswer((_) {
  //     return LocalSettings(
  //       printInvoice: Switcher.On.index,
  //     ).toRawJson();
  //   });

  //   expect(prefProvider.invoiceEnabled, false);
  // });

  group('invoice disabled', () {
    test('invoice disabled', () {
      final prefProvider = MyPrefProvider(
        brandsInvoice: BrandsInvoice(
          guiException: Switcher.On.index,
          status: Switcher.Off.index,
        ),
        localSettings: LocalSettings(
          printInvoice: Switcher.On.index,
        ),
      );

      expect(prefProvider.invoiceEnabled, false);
    });
  });

  group('invoice enabled with gui exception', () {
    test('local invoice enabled', () {
      final prefProvider = MyPrefProvider(
        brandsInvoice: BrandsInvoice(
          guiException: Switcher.On.index,
          status: Switcher.On.index,
        ),
        localSettings: LocalSettings(
          printInvoice: Switcher.On.index,
        ),
      );
      expect(prefProvider.invoiceEnabled, true);
    });

    test('local invoice disabled', () {
      final prefProvider = MyPrefProvider(
        brandsInvoice: BrandsInvoice(
          guiException: Switcher.On.index,
          status: Switcher.On.index,
        ),
        localSettings: LocalSettings(
          printInvoice: Switcher.Off.index,
        ),
      );
      expect(prefProvider.invoiceEnabled, false);
    });
  });

  group('invoice enabled without gui exception', () {
    setUp(() {
      logger.d('setUp - 2');
    });

    test('local invoice enabled', () {
      final prefProvider = MyPrefProvider(
        brandsInvoice: BrandsInvoice(
          guiException: Switcher.Off.index,
          status: Switcher.On.index,
        ),
        localSettings: LocalSettings(
          printInvoice: Switcher.On.index,
        ),
      );
      expect(prefProvider.invoiceEnabled, true);
    });

    test('local invoice disabled', () {
      final prefProvider = MyPrefProvider(
        brandsInvoice: BrandsInvoice(
          guiException: Switcher.Off.index,
          status: Switcher.On.index,
        ),
        localSettings: LocalSettings(
          printInvoice: Switcher.Off.index,
        ),
      );
      expect(prefProvider.invoiceEnabled, true);
    });
  });
}
