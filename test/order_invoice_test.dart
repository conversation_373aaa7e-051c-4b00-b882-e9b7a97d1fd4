import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:muyipork/app/data/models/other/order_detail.dart';
import 'package:muyipork/app/data/models/req/orders_orderid_invoice_post_req.dart';
import 'package:muyipork/app/data/models/req/orders_post_req.dart';
import 'package:muyipork/extension.dart';

main() {
  group('copy invoice data from post req', () {
    final src = OrdersPostReq(
      invoiceNumber: "AA00000010",
      randomNumber: "3361",
      invoicePaper: true,
      vatNumber: "12656354",
      carrierType: 3,
      carrierId: "/KK2X9NQ",
      npoBan: "000",
    );

    OrdersOrderIdInvoicePostReq dest;

    setUpAll(() {
      dest = src.toOrdersOrderIdInvoicePostReq();
    });
    test('invoiceNumber', () {
      expect(dest.invoiceNumber, src.invoiceNumber);
    });
    test('randomNumber', () {
      expect(dest.randomNumber, src.randomNumber);
    });
    test('invoicePaper', () {
      expect(dest.invoicePaper, src.invoicePaper);
    });
    test('vatNumber', () {
      expect(dest.vatNumber, src.vatNumber);
    });
    test('carrierType', () {
      expect(dest.carrierType, src.carrierType);
    });
    test('carrierId', () {
      expect(dest.carrierId, src.carrierId);
    });
    test('npoBan', () {
      expect(dest.npoBan, src.npoBan);
    });
  });

  group('order detail', () {
    const jsonString =
        '{"order_invoice":{"carrier_id":"/8LR6-P2","carrier_type":3,"invoice_paper":true,"npo_ban":"987","number":"GG12345678","random_number":"1234","status":0,"vat_number":"12345678"}}';
    final orderDetail = OrderDetail.fromJson(jsonDecode(jsonString));

    test('invoice status', () {
      expect(orderDetail.displayInvoiceStatus, '已捐贈');
    });

    test('order invoice object', () {
      final orderInvoice = orderDetail.orderInvoice;
      expect(orderInvoice.invoiceStatus.name, '開立');
      expect(orderInvoice.displayStatus, '已捐贈');
    });
  });
}
