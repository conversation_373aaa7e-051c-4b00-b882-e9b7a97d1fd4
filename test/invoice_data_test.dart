import 'package:flutter_test/flutter_test.dart';
import 'package:muyipork/app/data/InvoiceData.dart';
import 'package:muyipork/app/providers/local_invoice_provider.dart';
import 'package:muyipork/extension.dart';

void main() {
  group('Invoice Period', () {
    test('2024-01-01 should be in period 202402', () {
      // Arrange
      final invoiceData = InvoiceData(
        date: DateTime(2024, 1, 1).millisecondsSinceEpoch,
      );

      // Act
      final period = invoiceData.invoicePeriod;

      // Assert
      expect(period, equals('202402'));
    });

    test('20240201 should be in period 202402', () {
      // Arrange
      final invoiceData = InvoiceData(
        date: DateTime(2024, 2, 1).millisecondsSinceEpoch,
      );

      // Act
      final period = invoiceData.invoicePeriod;

      // Assert
      expect(period, equals('202402'));
    });
  });

  group('InvoiceData 期別驗證測試', () {
    test('相同期別應該通過驗證', () {
      final invoiceData = InvoiceData(
        seller: 'seller123',
        date: DateTime(2025, 1, 15).millisecondsSinceEpoch, // 1月 -> 202502期
        number: 'INV001',
      );

      final result = invoiceData.verify(
        seller: 'seller123',
        date: DateTime(2025, 2, 10), // 2月 -> 202502期
      );

      expect(result, true);
    });

    test('不同期別應該不通過驗證', () {
      final invoiceData = InvoiceData(
        seller: 'seller123',
        date: DateTime(2025, 1, 15).millisecondsSinceEpoch, // 1月 -> 202502期
        number: 'INV001',
      );

      final result = invoiceData.verify(
        seller: 'seller123',
        date: DateTime(2025, 3, 10), // 3月 -> 202504期
      );

      expect(result, false);
    });

    test('不同賣方應該不通過驗證', () {
      final invoiceData = InvoiceData(
        seller: 'seller123',
        date: DateTime(2025, 1, 15).millisecondsSinceEpoch,
        number: 'INV001',
      );

      final result = invoiceData.verify(
        seller: 'seller456',
        date: DateTime(2025, 1, 20),
      );

      expect(result, false);
    });
  });
}
