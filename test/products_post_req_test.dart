import 'package:flutter_test/flutter_test.dart';
import 'package:muyipork/app/data/models/req/products_post_req.dart';
import 'package:muyipork/extension.dart';

void main() {
  group('product post req test', () {
    ProductsPostReq req = ProductsPostReq();

    setUp(() {
      print('setUp');
      req = ProductsPostReq();
      req.addNewAdditionCategory(0, 'test');
    });

    test('add exist AdditionCategory', () {
      final actual = req.addNewAdditionCategory(0, 'test');
      expect(actual, false);
    });

    test('add non exist AdditionCategory', () {
      final actual = req.addNewAdditionCategory(1, 'test');
      expect(actual, true);
    });

    test('remove exist AdditionCategory', () {
      final actual = req.removeAdditionCategory(0);
      expect(actual, true);
    });

    test('remove non exist AdditionCategory', () {
      final actual = req.removeAdditionCategory(1);
      expect(actual, false);
    });

    test('', () {
      //
    });
  });
}
