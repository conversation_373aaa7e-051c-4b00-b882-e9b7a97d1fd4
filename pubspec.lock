# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      url: "https://pub.dartlang.org"
    source: hosted
    version: "22.0.0"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.1"
  archive:
    dependency: transitive
    description:
      name: archive
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.2"
  args:
    dependency: transitive
    description:
      name: args
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.5.0"
  barcode:
    dependency: transitive
    description:
      name: barcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  bpscm:
    dependency: "direct main"
    description:
      path: "."
      ref: "679b076a8bc233ce1d8c6f25fba3f7e045cdd85d"
      resolved-ref: "679b076a8bc233ce1d8c6f25fba3f7e045cdd85d"
      url: "https://bitbucket.org/umomos/bpscm.git"
    source: git
    version: "0.0.1"
  build:
    dependency: transitive
    description:
      name: build
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  build_config:
    dependency: transitive
    description:
      name: build_config
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.0"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.2"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "7.1.0"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.0"
  built_value:
    dependency: transitive
    description:
      name: built_value
      url: "https://pub.dartlang.org"
    source: hosted
    version: "8.0.4"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  charset_converter:
    dependency: transitive
    description:
      name: charset_converter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.0"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.15.0"
  convert:
    dependency: transitive
    description:
      name: convert
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.0"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.1+4"
  crypto:
    dependency: transitive
    description:
      name: crypto
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.3"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  device_info:
    dependency: "direct main"
    description:
      name: device_info
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.2"
  device_info_platform_interface:
    dependency: transitive
    description:
      name: device_info_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  dio:
    dependency: "direct main"
    description:
      name: dio
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  encrypt:
    dependency: transitive
    description:
      name: encrypt
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.1"
  esc_pos_bluetooth:
    dependency: transitive
    description:
      path: "."
      ref: master
      resolved-ref: c721fea984e78f8ccf5f48a924bf1a3c071c01c3
      url: "https://bitbucket.org/umomos/esc_pos_bluetooth.git"
    source: git
    version: "0.4.1"
  esc_pos_printer:
    dependency: transitive
    description:
      path: "."
      ref: master
      resolved-ref: "45490f3b51a2b37a043bdb5c4c66071cb2229f4a"
      url: "https://bitbucket.org/umomos/esc_pos_printer.git"
    source: git
    version: "4.1.0"
  esc_pos_utils:
    dependency: transitive
    description:
      path: "."
      ref: master
      resolved-ref: "41de7f2001a206b7ac8943c92024947673f2afbc"
      url: "https://bitbucket.org/umomos/esc_pos_utils.git"
    source: git
    version: "1.1.0"
  expandable:
    dependency: "direct main"
    description:
      name: expandable
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  fast_gbk:
    dependency: transitive
    description:
      name: fast_gbk
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  ffi:
    dependency: transitive
    description:
      name: ffi
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  file:
    dependency: transitive
    description:
      name: file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.0"
  firebase_analytics:
    dependency: "direct main"
    description:
      name: firebase_analytics
      url: "https://pub.dartlang.org"
    source: hosted
    version: "9.1.1"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.0+7"
  firebase_core:
    dependency: transitive
    description:
      name: firebase_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.12.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.4"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.6.0"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      url: "https://pub.dartlang.org"
    source: hosted
    version: "10.0.7"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.5"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.5"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  flat_buffers:
    dependency: transitive
    description:
      name: flat_buffers
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.5"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_bluetooth_basic:
    dependency: transitive
    description:
      path: "."
      ref: master
      resolved-ref: dc45fbea67168e0db6433955b1b2cbf3c0599944
      url: "https://bitbucket.org/umomos/flutter_bluetooth_basic.git"
    source: git
    version: "0.1.7"
  flutter_blurhash:
    dependency: transitive
    description:
      name: flutter_blurhash
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.0"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  flutter_godex_printer:
    dependency: "direct main"
    description:
      path: "."
      ref: b38cb137e7c9e7118f5ad303121c02c0ccd057fd
      resolved-ref: b38cb137e7c9e7118f5ad303121c02c0ccd057fd
      url: "https://bitbucket.org/umomos/flutter_godex_printer.git"
    source: git
    version: "0.0.1"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.4"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      path: flutter_local_notifications
      ref: "460679baf917048e0894d571dae34d97fb724822"
      resolved-ref: "460679baf917048e0894d571dae34d97fb724822"
      url: "https://github.com/umomos/flutter_local_notifications.git"
    source: git
    version: "6.0.0-dev.1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.0"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.2"
  flutter_star_prnt:
    dependency: "direct main"
    description:
      name: flutter_star_prnt
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.4"
  flutter_sunmi_printer:
    dependency: "direct main"
    description:
      path: "."
      ref: "47bf21b3966d5274802b1640c0e33e8d26bfca58"
      resolved-ref: "47bf21b3966d5274802b1640c0e33e8d26bfca58"
      url: "https://bitbucket.org/umomos/flutter_sunmi_printer.git"
    source: git
    version: "0.2.1"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.22.0"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.3"
  get:
    dependency: "direct main"
    description:
      name: get
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.4"
  get_storage:
    dependency: "direct main"
    description:
      name: get_storage
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.3"
  glob:
    dependency: transitive
    description:
      name: glob
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  graphs:
    dependency: transitive
    description:
      name: graphs
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0"
  hex:
    dependency: transitive
    description:
      name: hex
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  hive:
    dependency: "direct main"
    description:
      name: hive
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  hive_flutter:
    dependency: "direct main"
    description:
      name: hive_flutter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  http:
    dependency: transitive
    description:
      name: http
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.13.1"
  http_mock_adapter:
    dependency: "direct dev"
    description:
      name: http_mock_adapter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.3"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  image:
    dependency: "direct main"
    description:
      name: image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.2"
  image_cropper:
    dependency: "direct main"
    description:
      name: image_cropper
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.1"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.3+2"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0"
  intl:
    dependency: "direct main"
    description:
      name: intl
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.17.0"
  io:
    dependency: transitive
    description:
      name: io
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.4"
  js:
    dependency: transitive
    description:
      name: js
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.3"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.1"
  jwt_decoder:
    dependency: "direct main"
    description:
      name: jwt_decoder
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  lints:
    dependency: transitive
    description:
      name: lints
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  logger:
    dependency: "direct main"
    description:
      name: logger
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  logging:
    dependency: transitive
    description:
      name: logging
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.10"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  mime:
    dependency: transitive
    description:
      name: mime
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.3"
  mockito:
    dependency: "direct dev"
    description:
      name: mockito
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.15"
  objectbox:
    dependency: "direct main"
    description:
      name: objectbox
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.0"
  objectbox_flutter_libs:
    dependency: "direct main"
    description:
      name: objectbox_flutter_libs
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.0"
  objectbox_generator:
    dependency: "direct dev"
    description:
      name: objectbox_generator
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0+1"
  okshop_common:
    dependency: "direct overridden"
    description:
      path: "."
      ref: "608972984cfe15332e1516b715fd9bf651fe224a"
      resolved-ref: "608972984cfe15332e1516b715fd9bf651fe224a"
      url: "https://bitbucket.org/umomos/okshop_common.git"
    source: git
    version: "0.0.1"
  okshop_esc_pos:
    dependency: "direct main"
    description:
      path: "."
      ref: "9dd914252d4c91dea01a1ab94e2b9dac19f664e5"
      resolved-ref: "9dd914252d4c91dea01a1ab94e2b9dac19f664e5"
      url: "https://bitbucket.org/umomos/okshop_esc_pos.git"
    source: git
    version: "0.0.1"
  okshop_model:
    dependency: "direct overridden"
    description:
      path: "."
      ref: "587661a5ea71d55419f8767fcdafc543af694ea7"
      resolved-ref: "587661a5ea71d55419f8767fcdafc543af694ea7"
      url: "https://bitbucket.org/umomos/okshop_model.git"
    source: git
    version: "0.0.1"
  package_config:
    dependency: transitive
    description:
      name: package_config
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  package_info:
    dependency: "direct main"
    description:
      name: package_info
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.2"
  path:
    dependency: "direct main"
    description:
      name: path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.5.0"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  path_provider_macos:
    dependency: transitive
    description:
      name: path_provider_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.11.0"
  permission_handler:
    dependency: "direct main"
    description:
      path: permission_handler
      ref: d4ae21be8728a2c6bc216932101f6d455a2b846f
      resolved-ref: d4ae21be8728a2c6bc216932101f6d455a2b846f
      url: "https://github.com/umomos/flutter-permission-handler.git"
    source: git
    version: "8.1.6"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.6.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.0"
  photo_view:
    dependency: "direct main"
    description:
      name: photo_view
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.13.0"
  ping_discover_network:
    dependency: transitive
    description:
      name: ping_discover_network
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0+1"
  platform:
    dependency: transitive
    description:
      name: platform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.5.1"
  pool:
    dependency: transitive
    description:
      name: pool
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.1"
  process:
    dependency: transitive
    description:
      name: process
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.1"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  qr:
    dependency: transitive
    description:
      name: qr
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  qr_code_scanner:
    dependency: "direct main"
    description:
      name: qr_code_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.5.2"
  quiver:
    dependency: transitive
    description:
      name: quiver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  reorderables:
    dependency: "direct main"
    description:
      name: reorderables
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.1"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.26.0"
  screenshot:
    dependency: "direct main"
    description:
      name: screenshot
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.0"
  shelf:
    dependency: transitive
    description:
      name: shelf
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  sizer:
    dependency: "direct main"
    description:
      name: sizer
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.15"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0+3"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0+2"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.10.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  stream_transform:
    dependency: "direct main"
    description:
      name: stream_transform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.19"
  timeago:
    dependency: "direct main"
    description:
      name: timeago
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  timezone:
    dependency: transitive
    description:
      name: timezone
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.0"
  timing:
    dependency: transitive
    description:
      name: timing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  tuple:
    dependency: "direct main"
    description:
      name: tuple
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.0.9"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  uuid:
    dependency: transitive
    description:
      name: uuid
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.4"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.0"
  wifi:
    dependency: transitive
    description:
      name: wifi
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.5"
  win32:
    dependency: transitive
    description:
      name: win32
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.5"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  xml:
    dependency: transitive
    description:
      name: xml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.1.2"
  yaml:
    dependency: transitive
    description:
      name: yaml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
sdks:
  dart: ">=2.12.0 <3.0.0"
  flutter: ">=2.0.0"
